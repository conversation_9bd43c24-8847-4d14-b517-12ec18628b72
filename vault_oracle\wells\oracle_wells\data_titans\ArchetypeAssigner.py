#!/usr/bin/env python3
# DIGITAL FINGERPRINT: UUID=4d5e6f7a-8b9c-0d1e-2f3a-4b5c6d7e8f9a | DATE=2025-06-26
"""
(C) 2025 Hyper Medusa Neural Vault, All Rights Reserved.
PROPRIETARY & CONFIDENTIAL — UNAUTHORIZED USE, COPYING, OR DISTRIBUTION IS STRICTLY PROHIBITED.

This file contains proprietary algorithms, business logic, and intellectual property of Hyper Medusa Neural Vault.
Use, reproduction, or disclosure by any means without explicit written permission is a violation of applicable law.
"""

"""
HYPER MEDUSA NEURAL VAULT - ArchetypeAssigner Business Value Documentation
============================================================================

ArchetypeAssigner.py
--------------------
Divine basketball archetype assignment engine for mapping players to mythological archetypes and Olympian deities.

Business Value:
- Proprietary archetype assignment: Divine, quantum, and expert-level mapping for player segmentation.
- Competitive Edge: Enables unique player insights, team chemistry, and prediction enhancement.
- Integration: Connects with Oracle Focus, DataTitan, and simulation modules for holistic analytics.
- Explainability: Provides confidence scoring, quantum signature, and performance optimization.
- Extensibility: Designed for plugin analytics, new archetypes, and custom endpoints.

For further details, see module-level docstrings and architecture documentation.
"""

"""
Expert ArchetypeAssigner v3.0: Divine Basketball Archetype Assignment Engine
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
Expert Basketball Intelligence Integration:
- Maps clustered players to 32 mythological archetypes with divine precision
- Links each archetype to patron Olympian deities with basketball intelligence
- Captures primary, secondary, and tertiary heroic influences with confidence scoring
- Quantum signature generation for secure archetype verification
- Full integration with NBA/WNBA prediction and odds systems
- Expert error handling and performance optimization
"""


import os
import numpy as np
import hmac
import hashlib
import logging
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from enum import Enum
from dataclasses import dataclass
from vault_oracle.core.oracle_focus import oracle_focus
from vault_oracle.interfaces.expert_messaging_orchestrator import ExpertMessagingOrchestrator
from vault_oracle.core.expert_metrics_registry import get_expert_metrics
from vault_oracle.interfaces.expert_messaging_orchestrator import get_messaging_orchestrator_sync
import asyncio



# Import Oracle Focus and Expert components
try:
    EXPERT_INTEGRATION_AVAILABLE = True
except ImportError:
    # Fallback for standalone usage
    def oracle_focus(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    ExpertMessagingOrchestrator = None
    get_expert_metrics = lambda: None
    EXPERT_INTEGRATION_AVAILABLE = False

logger = logging.getLogger(__name__)

# Log integration status
if EXPERT_INTEGRATION_AVAILABLE:
    logger.info("🏀 MEDUSA VAULT: ArchetypeAssigner fully integrated with Expert Oracle system")
else:
    logger.warning("🏀 TITAN WARNING: Expert Oracle integration not available, using fallback mode")

@dataclass
class ArchetypeAnalysis:
 """Expert archetype analysis with basketball intelligence"""
 hero_id: str
 primary_archetype: Dict[str, Any]
 secondary_archetype: Dict[str, Any]
 tertiary_archetype: Dict[str, Any]
 confidence_matrix: Dict[str, float]
 basketball_intelligence_score: float
 quantum_signature: str
 analysis_timestamp: datetime
 archetype_evolution: Optional[List[str]] = None

class ArchetypeCategory(Enum):
 """Basketball archetype categories"""
 OFFENSIVE_INITIATOR = "offensive_initiator"
 SCORING_SPECIALIST = "scoring_specialist"
 BIG_MAN_SPECTRUM = "big_man_spectrum"
 DEFENSIVE_ARCHITECT = "defensive_architect"
 HYBRID_ROLE = "hybrid_role"
 SPECIALIZED_WEAPON = "specialized_weapon"
 TWO_WAY_THREAT = "two_way_threat"
 TRANSITION_SPECIALIST = "transition_specialist"
 SHOOTING_SPECTRUM = "shooting_spectrum"
 PLAYMAKING = "playmaking"
 DEFENSE_SPECIALIST = "defense_specialist"
 ENERGY_ROLE_PLAYER = "energy_role_player"
 MODERN_EVOLUTION = "modern_evolution"
 PHYSICAL_PHENOM = "physical_phenom"
# Expert NBA/WNBA Archetype Map v3.0 - Comprehensive Basketball Intelligence
EXPERT_ARCHETYPE_MAP = {
 # Offensive Initiators - Primary ball handlers and offensive orchestrators
 0: {
 "name": "Olympian Vanguard",
 "traits": ["high_usage", "clutch_scoring", "leadership", "offensive_engine"],
 "deity": "Zeus",
 "category": ArchetypeCategory.OFFENSIVE_INITIATOR,
 "basketball_profile": "Elite primary option with exceptional clutch gene and leadership qualities",
 "betting_value": "High variance, clutch performance correlation, late-game prop value",
 "examples": ["LeBron James", "Giannis Antetokounmpo", "Luka Dončić"]
 },
 1: {
 "name": "Hermes' Courier", 
 "traits": ["pace", "transition", "speed", "fast_break_excellence"],
 "deity": "Hermes",
 "category": ArchetypeCategory.TRANSITION_SPECIALIST,
 "basketball_profile": "Exceptional transition player who thrives in up-tempo games",
 "betting_value": "Pace-dependent, assists props, steals correlation",
 "examples": ["Russell Westbrook", "De'Aaron Fox", "Ja Morant"]
 },
 2: {
 "name": "Athena's Strategist",
 "traits": ["pnr_mastery", "assist_quality", "basketball_iq", "court_vision"],
 "deity": "Athena",
 "category": ArchetypeCategory.PLAYMAKING,
 "basketball_profile": "Master floor general with exceptional court vision and decision-making",
 "betting_value": "Assists props, team performance correlation, low turnover rate",
 "examples": ["Chris Paul", "Steve Nash", "John Stockton"]
 },
 # Scoring Specialists - Elite shot creators and bucket getters
 3: {
 "name": "Apollo's Archer",
 "traits": ["three_level_scoring", "midrange", "shot_creation", "volume_scoring"],
 "deity": "Apollo",
 "category": ArchetypeCategory.SCORING_SPECIALIST,
 "basketball_profile": "Elite scorer across all three levels with exceptional shot-making ability",
 "betting_value": "Points props, shooting percentage variance, clutch scoring",
 "examples": ["Kevin Durant", "Stephen Curry", "Damian Lillard"]
 },
 4: {
 "name": "Artemis' Huntress",
 "traits": ["off_ball_movement", "catch_shoot", "three_point_specialist", "floor_spacing"],
 "deity": "Artemis",
 "category": ArchetypeCategory.SHOOTING_SPECTRUM,
 "basketball_profile": "Elite off-ball movement and three-point shooting specialist",
 "betting_value": "Three-point made props, hot streak potential, team spacing value",
 "examples": ["Klay Thompson", "Ray Allen", "Reggie Miller"]
 },
 5: {
 "name": "Hephaestus' Anvil",
 "traits": ["post_ups", "physical_scoring", "paint_dominance", "strength"],
 "deity": "Hephaestus",
 "category": ArchetypeCategory.BIG_MAN_SPECTRUM,
 "basketball_profile": "Physical post presence with dominant paint scoring ability",
 "betting_value": "Points props in paint, rebounding correlation, foul drawing",
 "examples": ["Shaquille O'Neal", "Joel Embiid", "Nikola Jokić"]
 },
 # Big Man Spectrum - Centers and power forwards
 6: {
 "name": "Atlas' Pillar",
 "traits": ["rim_protection", "screen_assists", "defensive_anchor", "paint_protection"],
 "deity": "Atlas",
 "category": ArchetypeCategory.DEFENSIVE_ARCHITECT,
 "basketball_profile": "Defensive anchor who protects the rim and sets foundation",
 "betting_value": "Blocks props, defensive rating correlation, opponent shooting impact",
 "examples": ["Rudy Gobert", "Dwight Howard", "Ben Wallace"]
 },
 7: {
 "name": "Hestia's Hearth",
 "traits": ["paint_scoring", "offensive_rebounds", "second_chance_points", "hustle"],
 "deity": "Hestia",
 "category": ArchetypeCategory.ENERGY_ROLE_PLAYER,
 "basketball_profile": "Energy big who excels at second-chance opportunities and hustle plays",
 "betting_value": "Offensive rebounds props, second-chance points, energy metrics",
 "examples": ["Dennis Rodman", "Montrezl Harrell", "Clint Capela"]
 },
 8: {
 "name": "Demeter's Cultivator",
 "traits": ["stretch_five", "floor_spacing", "perimeter_shooting", "versatility"],
 "deity": "Demeter",
 "category": ArchetypeCategory.MODERN_EVOLUTION,
 "basketball_profile": "Modern stretch big who opens up floor with perimeter shooting",
 "betting_value": "Three-point props for bigs, spacing value, matchup advantages",
 "examples": ["Karl-Anthony Towns", "Kristaps Porziņģis", "Brook Lopez"]
 }, # Defensive Architects - Elite defensive specialists and anchors
 9: {
 "name": "Ares' Bulwark",
 "traits": ["versatile_defense", "defensive_IQ", "perimeter_defense", "switchability"],
 "deity": "Ares",
 "category": ArchetypeCategory.DEFENSIVE_ARCHITECT,
 "basketball_profile": "Elite perimeter defender who can guard multiple positions",
 "betting_value": "Steals props, defensive rating impact, opponent shooting suppression",
 "examples": ["Kawhi Leonard", "Jrue Holiday", "Marcus Smart"]
 },
 10: {
 "name": "Hades' Shadow",
 "traits": ["steals", "deflections", "passing_lanes", "anticipation"],
 "deity": "Hades",
 "category": ArchetypeCategory.DEFENSE_SPECIALIST,
 "basketball_profile": "Elite ball hawk who disrupts offensive flow with steals and deflections",
 "betting_value": "Steals props, deflections correlation, turnover generation",
 "examples": ["Gary Payton", "Chris Paul", "Fred VanVleet"]
 },
 11: {
 "name": "Themis' Scales",
 "traits": ["help_defense", "rotations", "team_defense", "communication"],
 "deity": "Themis",
 "category": ArchetypeCategory.DEFENSIVE_ARCHITECT,
 "basketball_profile": "Defensive quarterback who orchestrates team defense with perfect rotations",
 "betting_value": "Team defensive rating correlation, opponent field goal impact",
 "examples": ["Draymond Green", "Al Horford", "Tim Duncan"]
 },
 # Hybrid Roles - Multi-positional players with diverse skill sets
 12: {
 "name": "Prometheus' Visionary",
 "traits": ["jumbo_playmaking", "passing", "size_advantage", "court_vision"],
 "deity": "Prometheus",
 "category": ArchetypeCategory.HYBRID_ROLE,
 "basketball_profile": "Oversized playmaker who creates matchup problems with size and passing",
 "betting_value": "Assists props for bigs, unique matchup advantages, versatility value",
 "examples": ["Ben Simmons", "Nikola Jokić", "Luka Dončić"]
 },
 13: {
 "name": "Hecate's Triad",
 "traits": ["3d_plus", "versatility", "shooting", "defense", "playmaking"],
 "deity": "Hecate",
 "category": ArchetypeCategory.HYBRID_ROLE,
 "basketball_profile": "Three-and-D player plus additional skills creating modern versatility",
 "betting_value": "Multiple category props, lineup versatility, matchup flexibility",
 "examples": ["Jimmy Butler", "Jayson Tatum", "Scottie Barnes"]
 },
 # Specialized Weapons - Unique offensive talents
 14: {
 "name": "Eros' Arrow",
 "traits": ["gravity", "spacing", "attention_drawing", "off_ball_threat"],
 "deity": "Eros",
 "category": ArchetypeCategory.SPECIALIZED_WEAPON,
 "basketball_profile": "Player whose mere presence creates spacing and gravity for teammates",
 "betting_value": "Team offensive rating correlation, teammate assist props",
 "examples": ["Stephen Curry", "Dame Lillard", "Trae Young"]
 },
 15: {
 "name": "Hebe's Chalice",
 "traits": ["bench_scoring", "efficiency", "instant_offense", "microwave"],
 "deity": "Hebe",
 "category": ArchetypeCategory.SPECIALIZED_WEAPON,
 "basketball_profile": "Instant offense off the bench with high efficiency in limited minutes",
 "betting_value": "Points per minute, bench scoring props, efficiency metrics",
 "examples": ["Lou Williams", "Jordan Clarkson", "Tyler Herro"]
 },
 # Two-Way Threats - Elite on both ends of the floor
 16: {
 "name": "Heracles' Might",
 "traits": ["two_way_impact", "physicality", "elite_offense", "elite_defense"],
 "deity": "Heracles",
 "category": ArchetypeCategory.TWO_WAY_THREAT,
 "basketball_profile": "Physical force who dominates both ends with elite two-way impact",
 "betting_value": "Multiple category props, high usage correlation, impact metrics",
 "examples": ["Giannis Antetokounmpo", "Anthony Davis", "Joel Embiid"]
 },
 17: {
 "name": "Nike's Champion",
 "traits": ["clutch_performance", "closing", "late_game_excellence", "pressure_performer"],
 "deity": "Nike",
 "category": ArchetypeCategory.TWO_WAY_THREAT,
 "basketball_profile": "Elite performer who elevates in clutch moments and pressure situations",
 "betting_value": "Clutch stats props, fourth quarter performance, pressure correlation",
 "examples": ["Kawhi Leonard", "Kyrie Irving", "Kevin Durant"]
 },
 # Transition Specialists - Masters of pace and tempo
 18: {
 "name": "Boreas' Gale",
 "traits": ["fast_break", "speed", "transition_offense", "pace_pushing"],
 "deity": "Boreas",
 "category": ArchetypeCategory.TRANSITION_SPECIALIST,
 "basketball_profile": "Lightning-fast transition player who excels in up-tempo situations",
 "betting_value": "Fast break points, transition assists, pace-dependent props",
 "examples": ["Russell Westbrook", "De'Aaron Fox", "John Wall"]
 },
 19: {
 "name": "Zephyrus' Breeze",
 "traits": ["secondary_transition", "decision_making", "tempo_control", "smart_running"],
 "deity": "Zephyrus",
 "category": ArchetypeCategory.TRANSITION_SPECIALIST,
 "basketball_profile": "Smart transition player who makes excellent decisions in semi-transition",
 "betting_value": "Transition assists, decision-making metrics, team pace correlation",
 "examples": ["Chris Paul", "Kyle Lowry", "Rajon Rondo"]
 },
 # Shooting Spectrum - Elite marksmen with different specialties
 20: {
 "name": "Helios' Ray",
 "traits": ["corner_3s", "efficient_shooting", "high_percentage", "role_player_shooting"],
 "deity": "Helios",
 "category": ArchetypeCategory.SHOOTING_SPECTRUM,
 "basketball_profile": "Elite role player shooter who maximizes efficiency from specific spots",
 "betting_value": "Three-point percentage, corner three props, efficiency metrics",
 "examples": ["P.J. Tucker", "Danny Green", "Joe Harris"]
 },
 21: {
 "name": "Selene's Crescent",
 "traits": ["movement_3s", "off_screen", "dynamic_shooting", "shot_creation"],
 "deity": "Selene",
 "category": ArchetypeCategory.SHOOTING_SPECTRUM,
 "basketball_profile": "Dynamic shooter who creates looks through movement and screens",
 "betting_value": "Three-point makes, movement shooting, screen assists correlation",
 "examples": ["Klay Thompson", "J.J. Redick", "Duncan Robinson"]
 },
 # Playmaking - Different styles of facilitation
 22: {
 "name": "Metis' Cunning",
 "traits": ["ast_to_ratio", "low_turnovers", "smart_passing", "calculated_risks"],
 "deity": "Metis",
 "category": ArchetypeCategory.PLAYMAKING,
 "basketball_profile": "Cerebral playmaker who maximizes assist-to-turnover ratio",
 "betting_value": "Assist-to-turnover props, low turnover correlation, smart play metrics",
 "examples": ["Chris Paul", "Mike Conley", "Ricky Rubio"]
 },
 23: {
 "name": "Tyche's Gambit",
 "traits": ["high_risk_passes", "highlight_assists", "creative_passing", "flashy_plays"],
 "deity": "Tyche",
 "category": ArchetypeCategory.PLAYMAKING,
 "basketball_profile": "Creative playmaker who attempts high-risk, high-reward passes",
 "betting_value": "Highlight assists, creative passing metrics, high variance props",
 "examples": ["Russell Westbrook", "Ja Morant", "Lonzo Ball"]
 },
 # Defense Specialists - Unique defensive skill sets
 24: {
 "name": "Perseus' Shield",
 "traits": ["charge_taking", "draw_fouls", "positioning", "sacrifice_plays"],
 "deity": "Perseus",
 "category": ArchetypeCategory.DEFENSE_SPECIALIST,
 "basketball_profile": "Defensive specialist who excels at drawing charges and sacrifice plays",
 "betting_value": "Charges drawn, defensive fouls drawn, hustle play metrics",
 "examples": ["Kyle Lowry", "Ersan Ilyasova", "P.J. Tucker"]
 },
 25: {
 "name": "Medusa's Gaze",
 "traits": ["defensive_versatility", "switchability", "position_flexibility", "scheme_fit"],
 "deity": "Medusa",
 "category": ArchetypeCategory.DEFENSE_SPECIALIST,
 "basketball_profile": "Versatile defender who can guard multiple positions and fit any scheme",
 "betting_value": "Matchup versatility, scheme-dependent value, switchability metrics",
 "examples": ["Draymond Green", "Robert Covington", "Mikal Bridges"]
 },
 # Energy/Role Players - High-motor contributors
 26: {
 "name": "Hygieia's Sustenance",
 "traits": ["hustle", "energy", "motor", "effort_plays"],
 "deity": "Hygieia",
 "category": ArchetypeCategory.ENERGY_ROLE_PLAYER,
 "basketball_profile": "High-energy player who impacts winning through hustle and effort",
 "betting_value": "Hustle stats, energy metrics, plus-minus correlation",
 "examples": ["Pat Beverley", "Marcus Smart", "Montrezl Harrell"]
 },
 27: {
 "name": "Plutus' Bounty",
 "traits": ["rebounding", "putbacks", "second_chance", "glass_cleaning"],
 "deity": "Plutus",
 "category": ArchetypeCategory.ENERGY_ROLE_PLAYER,
 "basketball_profile": "Elite rebounder who generates second-chance opportunities",
 "betting_value": "Rebounding props, second-chance points, offensive glass correlation",
 "examples": ["Dennis Rodman", "Andre Drummond", "Steven Adams"]
 },
 # Modern Evolutions - New-age basketball archetypes
 28: {
 "name": "Proteus' Shifter",
 "traits": ["positionless", "versatility", "adaptability", "multiple_roles"],
 "deity": "Proteus",
 "category": ArchetypeCategory.MODERN_EVOLUTION,
 "basketball_profile": "Positionless player who can fulfill multiple roles within a system",
 "betting_value": "Positional versatility, role flexibility, matchup advantages",
 "examples": ["Draymond Green", "Bam Adebayo", "Scottie Barnes"]
 },
 29: {
 "name": "Chronos' Moment",
 "traits": ["micro_moments", "ato_execution", "situational_excellence", "timing"],
 "deity": "Chronos",
 "category": ArchetypeCategory.MODERN_EVOLUTION,
 "basketball_profile": "Player who excels in specific game situations and micro-moments",
 "betting_value": "Situational props, clutch execution, specific scenario correlation",
 "examples": ["Robert Horry", "Ray Allen", "Steve Kerr"]
 },
 # Physical Phenoms - Elite athleticism and physical gifts
 30: {
 "name": "Hephaestus' Forge",
 "traits": ["physical_presence", "durability", "strength", "intimidation"],
 "deity": "Hephaestus",
 "category": ArchetypeCategory.PHYSICAL_PHENOM,
 "basketball_profile": "Physical force who impacts games through sheer presence and strength",
 "betting_value": "Physical domination metrics, durability correlation, intimidation factor",
 "examples": ["Shaquille O'Neal", "Charles Barkley", "Zion Williamson"]
 },
 31: {
 "name": "Iris' Spectrum",
 "traits": ["athleticism", "vertical", "explosive_plays", "highlight_reel"],
 "deity": "Iris",
 "category": ArchetypeCategory.PHYSICAL_PHENOM,
 "basketball_profile": "Elite athlete who creates highlights through explosive athleticism",
 "betting_value": "Highlight plays, athletic metrics, explosive play correlation",
 "examples": ["Vince Carter", "Aaron Gordon", "Zach LaVine"]
 },
}

class ExpertArchetypeAssigner:
    """Expert-level archetype assignment with basketball intelligence and Oracle Focus integration"""
    
    def __init__(self, messaging_orchestrator = None):
        # Use singleton to avoid creating multiple orchestrator instances
        if messaging_orchestrator is None and ExpertMessagingOrchestrator:
            try:
                self.messaging_orchestrator = get_messaging_orchestrator_sync()
            except Exception as e:
                logger.warning(f"Failed to get messaging orchestrator: {e}")
                self.messaging_orchestrator = None
        else:
            self.messaging_orchestrator = messaging_orchestrator
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.metrics = get_expert_metrics() if EXPERT_INTEGRATION_AVAILABLE else None
        
        # Performance tracking        self.assignment_count = 0
        self.error_count = 0
        
        self.logger.info("🏀 MEDUSA VAULT: ExpertArchetypeAssigner initialized with full Oracle integration")
    
    @oracle_focus
    async def assign_heroic_identity_expert(
        self, 
        probabilities: np.ndarray, 
        hero_id: str,
        confidence_threshold: float = 0.1,
        include_basketball_intelligence: bool = True
    ) -> ArchetypeAnalysis:
        """
        Expert archetype assignment with comprehensive basketball intelligence
        
        Args:
            probabilities: Model output probabilities for each archetype
            hero_id: Unique player identifier 
            confidence_threshold: Minimum confidence for archetype inclusion
            include_basketball_intelligence: Whether to calculate basketball IQ score
        
        Returns:
            ArchetypeAnalysis with complete expert-level assignment
        """
        # Start performance tracking
        start_time = datetime.now()
        
        try:
            # Track assignment attempt
            self.assignment_count += 1
            if self.metrics:
                self.metrics.get_metric('memory_entries').set(self.assignment_count)
            
            # Validate input dimensions
            if probabilities.shape[0] != len(EXPERT_ARCHETYPE_MAP):
                self.logger.error(f"Probability shape mismatch: {probabilities.shape[0]} vs {len(EXPERT_ARCHETYPE_MAP)}")
                raise ValueError(
                    f"Probability array shape {probabilities.shape[0]} does not match archetype count {len(EXPERT_ARCHETYPE_MAP)}"
                )
            
            # Normalize probabilities if needed
            if not np.isclose(probabilities.sum(), 1.0, rtol=1e-3):
                self.logger.warning(f"Normalizing probabilities for player {hero_id}")
                probabilities = probabilities / probabilities.sum()
            
            # Sort indices by probability (highest first)
            sorted_indices = np.argsort(probabilities)[::-1]
            
            # Filter by confidence threshold and ensure valid indices
            valid_indices = [
                i for i in sorted_indices 
                if i in EXPERT_ARCHETYPE_MAP and probabilities[i] >= confidence_threshold
            ]
            
            if len(valid_indices) < 3:
                self.logger.warning(f"Only {len(valid_indices)} valid archetypes found for player {hero_id}")
                # Fallback: take top 3 regardless of threshold
                valid_indices = [i for i in sorted_indices[:3] if i in EXPERT_ARCHETYPE_MAP]
            
            if len(valid_indices) < 3:
                raise ValueError(f"Not enough valid archetypes found for player {hero_id}")
            
            # Format primary, secondary, tertiary archetypes
            primary = self._format_expert_archetype(valid_indices[0], probabilities[valid_indices[0]])
            secondary = self._format_expert_archetype(valid_indices[1], probabilities[valid_indices[1]])
            tertiary = self._format_expert_archetype(valid_indices[2], probabilities[valid_indices[2]])
            
            # Build confidence matrix
            confidence_matrix = {
                EXPERT_ARCHETYPE_MAP[i]["name"]: float(probabilities[i])
                for i in valid_indices[:10] # Top 10 for analysis
            }
            
            # Calculate basketball intelligence score
            basketball_iq = self._calculate_basketball_intelligence(
                probabilities, valid_indices
            ) if include_basketball_intelligence else 0.0
            
            # Generate quantum signature for verification
            quantum_signature = self._generate_quantum_signature(
                hero_id, primary, secondary, tertiary, confidence_matrix
            )
            
            # Create expert analysis
            analysis = ArchetypeAnalysis(
                hero_id=hero_id,
                primary_archetype=primary,
                secondary_archetype=secondary,
                tertiary_archetype=tertiary,
                confidence_matrix=confidence_matrix,
                basketball_intelligence_score=basketball_iq,
                quantum_signature=quantum_signature,
                analysis_timestamp=datetime.now()
            )
            
            # Track successful assignment performance
            duration = (datetime.now() - start_time).total_seconds()
            if self.metrics:
                self.metrics.get_metric('memory_load_time').observe(duration)
            
            # Expert messaging if available
            if self.messaging_orchestrator:
                await self._send_archetype_alert(analysis)
            
            self.logger.info(f"🏀 Expert archetype analysis completed for player {hero_id} in {duration:.3f}s")
            return analysis
        
        except Exception as e:
            # Track error
            self.error_count += 1
            if self.metrics:
                self.metrics.get_metric('memory_errors').labels(type='assignment_error').inc()
            
            self.logger.error(f"Expert archetype assignment failed for player {hero_id}: {str(e)}")
            if self.messaging_orchestrator:
                await self.messaging_orchestrator.send_basketball_alert(
                    "ArchetypeAssignment", 
                    f"Assignment failed for player {hero_id}: {str(e)}",
                    priority="HIGH"
                )
            raise
    
    def _format_expert_archetype(self, index: int, confidence: float) -> Dict[str, Any]:
        """Enhanced archetype formatting with basketball intelligence"""
        if index not in EXPERT_ARCHETYPE_MAP:
            raise ValueError(f"Invalid archetype index: {index}")
        
        archetype_data = EXPERT_ARCHETYPE_MAP[index]
        
        return {
            "name": archetype_data["name"],
            "deity": archetype_data["deity"],
            "traits": archetype_data["traits"],
            "category": archetype_data["category"].value,
            "basketball_profile": archetype_data["basketball_profile"],
            "betting_value": archetype_data["betting_value"],
            "examples": archetype_data["examples"],
            "confidence": float(confidence),
            "archetype_index": index
        }
    
    def _calculate_basketball_intelligence(
        self, 
        probabilities: np.ndarray, 
        valid_indices: List[int]
    ) -> float:
        """Calculate basketball intelligence score based on archetype distribution"""
        try:
            # Weight by archetype sophistication and distribution entropy
            sophistication_weights = {
                ArchetypeCategory.PLAYMAKING: 1.0,
                ArchetypeCategory.HYBRID_ROLE: 0.9,
                ArchetypeCategory.TWO_WAY_THREAT: 0.8,
                ArchetypeCategory.DEFENSIVE_ARCHITECT: 0.7,
                ArchetypeCategory.MODERN_EVOLUTION: 0.6,
                ArchetypeCategory.OFFENSIVE_INITIATOR: 0.5,
                ArchetypeCategory.SCORING_SPECIALIST: 0.4,
                ArchetypeCategory.SHOOTING_SPECTRUM: 0.3,
                ArchetypeCategory.SPECIALIZED_WEAPON: 0.2,
                ArchetypeCategory.ENERGY_ROLE_PLAYER: 0.1,
            }
            
            # Calculate weighted sophistication
            weighted_score = 0.0
            total_weight = 0.0
            
            for idx in valid_indices[:5]: # Top 5 archetypes
                if idx in EXPERT_ARCHETYPE_MAP:
                    category = EXPERT_ARCHETYPE_MAP[idx]["category"]
                    weight = sophistication_weights.get(category, 0.0)
                    weighted_score += probabilities[idx] * weight
                    total_weight += probabilities[idx]
            
            base_score = weighted_score / total_weight if total_weight > 0 else 0.0
              # Entropy bonus for well-rounded players
            entropy = -np.sum(probabilities * np.log(probabilities + 1e-10))
            entropy_normalized = entropy / np.log(len(EXPERT_ARCHETYPE_MAP))
              # Final basketball intelligence score (0-100)
            final_score = (base_score * 0.7 + entropy_normalized * 0.3) * 100
            
            return min(100.0, max(0.0, final_score))
        
        except Exception as e:
            self.logger.warning(f"Basketball intelligence calculation failed: {str(e)}")
            return 0.0
    
    def _generate_quantum_signature(
        self, 
        hero_id: str, 
        primary: Dict[str, Any], 
        secondary: Dict[str, Any], 
        tertiary: Dict[str, Any],
        confidence_matrix: Dict[str, float]
    ) -> str:
        """Generate quantum signature for archetype verification"""
        try:
            hmac_secret = os.getenv("QUANTUM_HMAC_SECRET")
            if not hmac_secret:
                self.logger.warning(" TITAN WARNING: QUANTUM_HMAC_SECRET not set, using fallback signature")
                return f"fallback_signature_{hero_id}_{int(datetime.now().timestamp())}"
            
            signature_data = {
                "hero_id": hero_id,
                "primary": primary["name"],
                "secondary": secondary["name"], 
                "tertiary": tertiary["name"],
                "confidence_sum": sum(confidence_matrix.values()),
                "timestamp": datetime.now().isoformat()
            }
            
            signature_string = json.dumps(signature_data, sort_keys=True)
            identity_hash = hashlib.sha3_256(signature_string.encode()).digest()
            
            quantum_signature = hmac.new(
                hmac_secret.encode(),
                identity_hash,
                hashlib.sha3_256
            ).hexdigest()
            
            return quantum_signature
        
        except Exception as e:
            self.logger.error(f"Quantum signature generation failed: {str(e)}")
            return f"signature_error_{hero_id}_{int(datetime.now().timestamp())}"
    
    async def _send_archetype_alert(self, analysis: ArchetypeAnalysis) -> None:
        """Send expert archetype analysis alert"""
        try:
            if not self.messaging_orchestrator:
                return
            
            alert_title = f"Expert Archetype Analysis: {analysis.hero_id}"
            alert_body = (
                f"Primary: {analysis.primary_archetype['name']} ({analysis.primary_archetype['confidence']:.3f})\n"
                f"Basketball IQ: {analysis.basketball_intelligence_score:.1f}/100\n"
                f"Category: {analysis.primary_archetype['category']}"
            )
            
            await self.messaging_orchestrator.send_basketball_alert(
                alert_type="ArchetypeAnalysis",
                title=alert_title,
                body=alert_body,
                hero_id=analysis.hero_id,
                priority="MEDIUM"
            )
        
        except Exception as e:
            self.logger.warning(f" TITAN PROCESSING FAILED: send archetype alert: {str(e)}")
    
    @oracle_focus
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics for archetype assignment"""
        return {
            "total_assignments": self.assignment_count,
            "total_errors": self.error_count,
            "success_rate": (self.assignment_count - self.error_count) / max(1, self.assignment_count),
            "integration_status": EXPERT_INTEGRATION_AVAILABLE,
            "metrics_available": self.metrics is not None,
            "messaging_available": self.messaging_orchestrator is not None
        }
    
    @oracle_focus
    def validate_archetype_map(self) -> bool:
        """Validate the integrity of the archetype map"""
        try:
            # Check all required fields are present
            required_fields = ["name", "traits", "deity", "category", "basketball_profile", "betting_value", "examples"]
            
            for idx, archetype in EXPERT_ARCHETYPE_MAP.items():
                for field in required_fields:
                    if field not in archetype:
                        self.logger.error(f"Missing field '{field}' in archetype {idx}")
                        return False
                
                # Validate category is enum
                if not isinstance(archetype["category"], ArchetypeCategory):
                    self.logger.error(f"Invalid category type for archetype {idx}")
                    return False
            
            self.logger.info(f"🏀 Archetype map validation successful: {len(EXPERT_ARCHETYPE_MAP)} archetypes")
            return True
            
        except Exception as e:
            self.logger.error(f"Archetype map validation failed: {str(e)}")
            return False


# Legacy compatibility function for existing code
@oracle_focus
def assign_heroic_identity(probabilities: np.ndarray) -> dict:
    """
    Legacy compatibility function - use ExpertArchetypeAssigner.assign_heroic_identity_expert() for new code
    """
    try:
        # Basic validation
        if probabilities.shape[0] != len(EXPERT_ARCHETYPE_MAP):
            raise ValueError(
                f"Probability array shape {probabilities.shape[0]} does not match archetype count {len(EXPERT_ARCHETYPE_MAP)}"
            )

        sorted_indices = np.argsort(probabilities)[::-1]

        # Ensure indices are within bounds of EXPERT_ARCHETYPE_MAP
        valid_indices = [i for i in sorted_indices if i in EXPERT_ARCHETYPE_MAP]
        if len(valid_indices) < 3:
            raise ValueError("Not enough valid archetypes found for assignment.")

        identity = {
            "primary": _format_archetype_legacy(valid_indices[0]),
            "secondary": _format_archetype_legacy(valid_indices[1]),
            "tertiary": _format_archetype_legacy(valid_indices[2]),
            "confidence_matrix": {
                EXPERT_ARCHETYPE_MAP[i]["name"]: float(probabilities[i])
                for i in valid_indices[:10] # Top 10 for legacy compatibility
            },
        }

        # Generate a signature for the assigned identity
        try:
            hmac_secret = os.getenv("QUANTUM_HMAC_SECRET")
            if hmac_secret:
                identity_hash = hashlib.sha3_256(
                    json.dumps(identity, sort_keys=True).encode()
                ).digest()
                identity["quantum_signature"] = hmac.new(
                    hmac_secret.encode(), identity_hash, hashlib.sha3_256
                ).hexdigest()
            else:
                identity["quantum_signature"] = "legacy_signature_missing_secret"
        except Exception as e:
            identity["quantum_signature"] = "signature_generation_failed"
            logging.warning(f"Quantum signature generation failed: {str(e)}")

        return identity
    
    except Exception as e:
        logging.error(f"Legacy archetype assignment failed: {str(e)}")
        raise


def _format_archetype_legacy(index: int) -> dict:
    """Legacy helper to format archetype details"""
    if index not in EXPERT_ARCHETYPE_MAP:
        raise ValueError(f"Invalid archetype index: {index}")
    
    archetype_data = EXPERT_ARCHETYPE_MAP[index]
    return {
        "name": archetype_data["name"],
        "deity": archetype_data["deity"],
        "traits": archetype_data["traits"],
    }


# Module initialization and validation
def _initialize_archetype_system():
    """Initialize and validate the archetype assignment system"""
    try:
        # Validate archetype map
        expert_assigner = ExpertArchetypeAssigner()
        if expert_assigner.validate_archetype_map():
            logger.info("🏀 MEDUSA VAULT: ArchetypeAssigner system fully initialized and validated")
            return True
        else:
            logger.error("🏀 TITAN ERROR: ArchetypeAssigner validation failed")
            return False
    except Exception as e:
        logger.error(f"🏀 TITAN ERROR: ArchetypeAssigner initialization failed: {str(e)}")
        return False


# Initialize on import if in production mode
if __name__ != "__main__":
    _initialize_archetype_system()


# Example usage and testing (optional, for testing)
if __name__ == "__main__":
    
    async def test_archetype_assignment():
        """Test the archetype assignment system"""
        logger.info("🏀 Testing ArchetypeAssigner system...")
        
        # Example probabilities (replace with actual model output)
        example_probabilities = np.random.rand(32)
        example_probabilities /= example_probabilities.sum() # Normalize to sum to 1
        
        try:
            # Test expert assignment
            expert_assigner = ExpertArchetypeAssigner()
            analysis = await expert_assigner.assign_heroic_identity_expert(
                example_probabilities, 
                "test_player_123"
            )
            
            
            # Test legacy assignment
            legacy_identity = assign_heroic_identity(example_probabilities)
            
            # Get performance metrics
            metrics = expert_assigner.get_performance_metrics()
            
        except (ValueError, Exception) as e:
            print(f"Test failed: {e}")

    # Run test if executed directly
    asyncio.run(test_archetype_assignment())
