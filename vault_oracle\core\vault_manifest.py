# vault_oracle/core/vault_manifest.py

# DIGITAL FINGERPRINT: UUID=aa1e2b3c-4d5e-6f7a-8b9c-0d1e2f3a4b5c | DATE=2025-06-26
"""
(C) 2025 Hyper Medusa Neural Vault, All Rights Reserved.
PROPRIETARY & CONFIDENTIAL — UNAUTHORIZED USE, COPYING, OR DISTRIBUTION IS STRICTLY PROHIBITED.

This file contains proprietary algorithms, business logic, and intellectual property of Hyper Medusa Neural Vault.
Use, reproduction, or disclosure by any means without explicit written permission is a violation of applicable law.
"""
"""
HYPER MEDUSA NEURAL VAULT MANIFEST v2.1.5
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
System identity and declarative metadata for the Hyper Medusa Neural Vault.
Defines core identity, hardware capabilities, feature catalog, API conduits,
data pathways, web integrations, aesthetic configurations, and cosmic parameters.
"""

import sys
import os
import hashlib
import platform
from typing import ClassVar
from enum import Enum
import functools
from functools import lru_cache
from pathlib import Path
from typing import Dict, List, Any, Optional, Literal
import shutil
import logging
from pydantic import (
    BaseModel,
    Field,
    SecretStr,
    field_validator,
    ValidationError,
    ValidationInfo,
    model_validator,
)

# Setup import path to allow importing local modules from the project root
PROJECT_ROOT = Path(__file__).resolve().parent.parents[2]
if str(PROJECT_ROOT) not in sys.path:
    sys.path.insert(0, str(PROJECT_ROOT))


# --- Mock/Placeholder Dependencies ---
# Define mock classes/functions for imported modules/classes that are not part of this file
# These mocks allow the script to run standalone without requiring the actual files
# to be present or fully implemented yet.

# Assuming oracle_focus is defined elsewhere (using the provided immersive)
try: from vault_oracle.core.oracle_focus import oracle_focus
except ImportError:
    def oracle_focus(func=None, *args, **kwargs):
        if func is None:
            return lambda f: f

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)

        return wrapper


# Mock external libraries used for hardware detection
try: import cpuinfo
except ImportError:
    # Define a mock cpuinfo object with a get_cpu_info method that returns a dictionary with an empty 'flags' list
    cpuinfo = type("MockCpuInfo", (object,), {"get_cpu_info": lambda: {"flags": []}})()

try: import qiskit
except ImportError:
    qiskit = None


# --- End Mock/Placeholder Dependencies ---


# --- Core System Parameters ---
# This dictionary defines system-wide constants used by various modules.
# It is expected to be imported by other parts of the Vault.
COSMIC_PARAMETERS: Dict[str, Any] = {
    "FATE_CEILING": 100.0,
    "TITAN_INFLUENCE": 0.05,
    "WEAVE_CADENCE": 3600,
    "FATE_THREADS": 50,
    "HUBRIS_THRESHOLD": 0.2,
    "ORACLE_MEMORY_TTL_DAYS": 30,
    "ARCHIVE_RETENTION_YEARS": 5,
    # Additional Cosmic Parameters
    "DEFAULT_MODEL_RETRAINING_INTERVAL_HOURS": 24, # How often models should be considered for retraining
    "DATA_PURGE_FREQUENCY_DAYS": 90, # How often old data should be purged from archives
    "MAX_HISTORICAL_GAME_DAYS": 365 * 3, # Max days of historical game data to retain
    "MIN_PLAYER_GAME_THRESHOLD": 5, # Minimum games played for player stats analysis
    "PREDICTION_HORIZON_DAYS": 7, # Default look-ahead period for predictions
}


class VaultIdentity(BaseModel):
    """𓃰 Quantum-Secure Neural Identity Matrix 𓀙

    Hardware-aware identity system with cross-platform optimization.
    Defines core vault metadata and hardware capabilities.
    """

    # Core Identity Matrix (Class Variables - defined once at class load time)
    NAME: ClassVar[str] = "Hyper Medusa Neural Vault (NBA Betting)"
    SHORTCODE: ClassVar[str] = "HMNV-NBA"
    VERSION: ClassVar[str] = "Serpent's Coil v2.1.5 (NBA Edition)"

    # Cryptographic Identity (derived from system info)
    CRYPTIC_SEAL: ClassVar[str] = hashlib.sha3_256(
        f"{platform.machine()}-{sys.version}".encode("utf-8")
    ).hexdigest()[:16]

    class HardwareProfile(BaseModel):
        """𓃄 System Capability Report 𓀙
        Details about the hardware capabilities of the system."""

        cpu: str = Field(default=platform.processor(), description="CPU information")
        cores: Optional[int] = Field(
            default=os.cpu_count(), description="Number of CPU cores"
        )
        crypto: str = Field(
            "Software Crypto",
            description="Cryptography acceleration status (e.g., 'AES-NI Available', 'Software Crypto')",
        )
        quantum_sim: str = Field(
            "None",
            description="Quantum simulation capability status (e.g., 'Qiskit', 'None')",
        )
        gpu_accel: str = Field(
            "Integrated",
            description="Graphics acceleration status (e.g., 'NVIDIA CUDA', 'Integrated')",
        )
        total_ram_gb: Optional[float] = Field(
            None, description="Total system RAM in GB (approximate)"
        )
        available_disk_gb: Optional[float] = Field(
            None, description="Available disk space in GB (approximate)"
        )

        @classmethod
        @lru_cache(maxsize=1)
        @oracle_focus
        def hardware_profile(cls) -> "VaultIdentity.HardwareProfile":
            """
            Generates a cached report of the system's hardware capabilities by checking for features.

            Returns:
            An instance of HardwareProfile detailing system capabilities.
            """
            crypto_status = "AES-NI Available" if cls._has_aes_ni() else "Software Crypto"
            quantum_status = "Qiskit" if cls._has_qiskit() else "None"
            gpu_status = "NVIDIA CUDA" if cls._has_cuda() else "Integrated"

            # Basic estimates for RAM and disk for demonstration, or could be detected with external libs
            # For a full integration, you would use a library like `psutil` here.
            try:

                total_disk, _, free_disk = shutil.disk_usage("/")
                available_disk_gb = round(free_disk / (1024**3), 2)
            except (ImportError, AttributeError):
                available_disk_gb = (
                    None # Fallback if shutil.disk_usage is not available or errors
                )

            # Estimating total RAM is more OS-dependent without `psutil`. Default to None.
            total_ram_gb = None
            # Example for Linux (requires `os.sysconf` which is not cross-platform)
            if hasattr(os, "sysconf"):
                try:
                    page_size = os.sysconf("SC_PAGE_SIZE")
                    phys_pages = os.sysconf("SC_PHYS_PAGES")
                    total_ram_gb = round((page_size * phys_pages) / (1024**3), 2)
                except Exception:
                    total_ram_gb = None

            return cls.HardwareProfile(
                cpu=platform.processor(),
                cores=os.cpu_count(),
                crypto=crypto_status,
                quantum_sim=quantum_status,
                gpu_accel=gpu_status,
                total_ram_gb=total_ram_gb,
                available_disk_gb=available_disk_gb,
            )

        @staticmethod
        @oracle_focus
        def _has_aes_ni() -> bool:
            """
            Detects if AES-NI instruction set is available on the CPU.
            Requires the 'py-cpuinfo' package. Returns False on error or if not detected.
            """
            try:
                info = cpuinfo.get_cpu_info()
                return "aes" in info.get("flags", [])
            except Exception:
                return False

        @staticmethod
        @oracle_focus
        def _has_qiskit() -> bool:
            """
            Checks for the presence of the Qiskit library and a minimum core count
            as a proxy for quantum simulation capability. Returns False on error or if not met.
            """
            try:
                if qiskit is None:
                    return False
                min_cores_for_qsim = 8
                return (os.cpu_count() or 0) > min_cores_for_qsim
            except Exception:
                return False

        @staticmethod
        @oracle_focus
        def _has_cuda() -> bool:
            """
            Attempts to detect NVIDIA GPUs by checking platform information.
            This is a basic check; more robust detection might involve checking torch.cuda or specific libraries.
            Returns False on error or if not detected.
            """
            try:
                uname_string = " ".join(platform.uname()).lower()
                return "nvidia" in uname_string or ("cuda" in uname_string)
            except Exception:
                return False


class DivineRealm(Enum):
    """Olympian destiny realms for models (MLflow stages).
    Matches the DivineRealm enum used in fateSelector.py for model staging."""

    OLYMPUS = "Production"
    ELEUSIS = "Staging"
    MORTAL_PLAIN = "None"
    TARTARUS = "Archived"

    @oracle_focus
    def __new__(cls, value: str):
        """Creates enum members with the specified string value."""
        obj = object.__new__(cls)
        obj._value_ = value
        return obj

    @oracle_focus
    def __init__(self, value: str):
        """Initializes instance-specific properties (value is already set by __new__)."""
        pass


class FeatureCatalog(Enum):
    """𓃰 Neural Strand Catalog 𓀙

    Enumerates system capabilities and provides descriptions.
    These represent different functionalities or analyses the Vault can perform.
    """

    PROPHETIC_PULSE = "SHAP value explanations for player impact"
    GORGON_STARE = "Live NBA game line freeze detection"
    AMPHISBAENA_MODE = "Multi-model consensus for game outcome prediction"
    MIDAS_TOUCH = "Win streak optimization algorithm for betting strategies"
    NYX_VEIL = "Dark pool betting market liquidity analysis"
    CHIMERA_ENSEMBLE = "Hybrid model blending for diverse prediction features"
    PLAYER_FATIGUE_ANALYSIS = "Predict player performance decline due to fatigue"
    INJURY_IMPACT_PREDICTION = "Forecast game outcome shifts based on player injuries"
    BETTING_LINE_ARBITRAGE = (
        "Detect profitable arbitrage opportunities across sportsbooks"
    )
    LIVE_ODDS_MONITORING = "Real-time tracking and analysis of fluctuating betting odds"
    DFS_OPTIMIZATION_ENGINE = "Optimize daily fantasy sports lineups for maximum score"
    HISTORICAL_PERFORMANCE_ANALYTICS = "Analyze past team and player performance trends"
    SEASONAL_TREND_IDENTIFICATION = (
        "Identify recurring patterns and trends across NBA seasons"
    )

    @property
    def description(self) -> str:
        """Returns the description of the feature (which is the enum value)."""
        return self.value

    @classmethod
    def list_features(cls) -> List[str]:
        """Returns a list of all feature names (enum member names)."""
        return [member.name for member in cls]


class RetryPolicy(BaseModel):
    """Configuration for API request retry behavior."""

    max_retries: int = Field(3, ge=0, description="Maximum number of retry attempts")
    backoff_factor: float = Field(
        0.5, ge=0.0, description="Factor by which to multiply delay between retries"
    )
    status_forcelist: List[int] = Field(
        default_factory=lambda: [500, 502, 503, 504],
        description="HTTP status codes to retry on",
    )


class PropheticSources(BaseModel):
    """𓃰 Adaptive API Conduit System 𓀙
    Configuration for external API integrations with hardware-aware authentication."""

    # Class variable for predefined simple URL routes (mapping name to path)
    ROUTES: ClassVar[Dict[str, str]] = {
        "HOOPS_PANTHEON_STATS_API": "/basketball/nba/stats",
        "HOOPS_PANTHEON_SCORES_API": "/basketball/nba/scores/live",
        "ODDS_API": "/sports/basketball/nba/odds",
        "PLAYER_ROSTER_API": "/basketball/nba/players/roster",
        "INJURY_REPORT_API": "/sports/nba/injuries",
        "BETTING_TRENDS_API": "/sports/nba/betting_trends",
        "NEWS_FEED_API": "/sports/nba/news",
        "PLAYER_STATS_API": "/basketball/nba/player_stats_advanced", # New detailed player stats route
    }

    class EndpointConfig(BaseModel):
        """𓃄 Secure API Gateway Configuration
        Configuration for a single external API endpoint."""

        base_url: str = Field(..., description="Base URL for the API endpoint")
        rate_limit: float = Field(
            1.0,
            ge=0.1,
            le=10.0,
            description="Rate limit for the endpoint (requests per second)",
        )
        auth_mode: Literal["hmac-sha3", "aes-ocb", "future-proof", "api-key"] = Field(
            ..., description="Authentication mode for the endpoint"
        )
        api_key: Optional[SecretStr] = Field(
            None,
            exclude=True,
            description="API key for authentication (if auth_mode is 'api-key')",
        )
        failover_endpoints: List[str] = Field(
            default_factory=list, description="List of failover endpoint URLs"
        )
        hardware_profile: Literal["consumer", "enterprise", "quantum-sim"] = Field(
            "consumer", description="Required hardware profile for the endpoint"
        )
        retry_policy: RetryPolicy = Field(
            default_factory=RetryPolicy, description="Retry policy for the endpoint"
        )

        @model_validator(mode="after")
        @oracle_focus
        def check_quantum_auth_consistency(self) -> "PropheticSources.EndpointConfig":
            """
            Validates that 'future-proof' authentication mode is only used
            when the required hardware_profile is 'quantum-sim'.
            """
            if (
                self.auth_mode == "future-proof"
                and self.hardware_profile != "quantum-sim"
            ):
                raise ValueError(
                    "Future-proof auth requires 'quantum-sim' hardware profile"
                )
            if self.auth_mode == "api-key" and not self.api_key:
                raise ValueError("api-key auth_mode requires 'api_key' to be provided.")
            return self

    ENDPOINT_CONFIGS: ClassVar[Dict[str, EndpointConfig]] = {
        "HOOPS_PANTHEON_STATS_API": EndpointConfig(
            base_url="https://api.example-stats.com/nba/v1",
            auth_mode="api-key",
            api_key=SecretStr("your_nba_stats_api_key_here"),
            rate_limit=2.0,
        ),
        "HOOPS_PANTHEON_SCORES_API": EndpointConfig(
            base_url="https://api.example-live-scores.com/nba/scores/realtime",
            auth_mode="hmac-sha3",
            rate_limit=5.0,
            failover_endpoints=[
                "https://backup.example-live-scores.com/nba/scores/realtime"
            ],
        ),
        "ODDS_API": EndpointConfig(
            base_url="https://api.example-odds.com/v2/nba",
            auth_mode="api-key",
            api_key=SecretStr("your_odds_api_key_here"),
            rate_limit=1.0,
            retry_policy=RetryPolicy(
                max_retries=5, backoff_factor=1.0
            ), # More aggressive retry
        ),
        "QUANTUM_NBA_MODEL": EndpointConfig(
            base_url="https://api.quantum-nba-predictor.com/v1",
            auth_mode="future-proof",
            hardware_profile="quantum-sim",
            rate_limit=0.1,
        ),
        "PLAYER_STATS_API": EndpointConfig( # New endpoint for advanced player stats
            base_url="https://api.example-player-stats.com/nba/v3",
            auth_mode="api-key",
            api_key=SecretStr("your_player_stats_api_key_here"),
            rate_limit=0.5,
            retry_policy=RetryPolicy(max_retries=2),
        ),
    }

    @classmethod
    @oracle_focus
    def get_route(cls, name: str) -> str:
        """
        Retrieve simple URL route path by name from the predefined ROUTES.

        Args:
        name: The name of the route (e.g., "HOOPS_PANTHEON_STATS_API").

        Returns:
        The corresponding URL route string.

        Raises:
        ValueError: If the route name is unknown.
        """
        route = cls.ROUTES.get(name)
        if route is None:
            raise ValueError(f"Unknown route: {name}")
        return route

    @classmethod
    def get_endpoint_config(cls, name: str) -> EndpointConfig:
        """
        Retrieve a full EndpointConfig object by name from ENDPOINT_CONFIGS.

        Args:
        name: The name of the endpoint config (e.g., "HOOPS_PANTHEON_STATS_API").

        Returns:
        The corresponding EndpointConfig instance.

        Raises:
        ValueError: If the endpoint config name is unknown.
        """
        config = cls.ENDPOINT_CONFIGS.get(name)
        if config is None:
            raise ValueError(f"Unknown endpoint config: {name}")
        return config

    @classmethod
    @oracle_focus
    def get_endpoint(cls, name: str) -> EndpointConfig:
        """
        Hardware-aware endpoint selector.

        Retrieves the EndpointConfig for a given name and adjusts
        authentication mode and hardware profile if the required hardware capability
        (e.g., 'quantum-sim') is not available on the current system.
        Returns a *copy* of the configuration.

        Args:
        name: The name of the endpoint (e.g., "HOOPS_PANTHEON_STATS_API").

        Returns:
        A potentially modified EndpointConfig instance (a copy).

        Raises:
        ValueError: If the endpoint name is unknown in ENDPOINT_CONFIGS.
        """
        config = cls.get_endpoint_config(name)
        config_copy = config.model_copy()

        if config_copy.hardware_profile == "quantum-sim":
            system_profile = VaultIdentity.hardware_profile()
            has_quantum_sim_capability = system_profile.quantum_sim != "None"

            if not has_quantum_sim_capability:
                config_copy.auth_mode = "aes-ocb"
                config_copy.hardware_profile = "consumer"
        return config_copy


class VaultPaths(BaseModel):
    """𓃰 Sacred Data Pathways 𓀙

    Defines standard data paths relative to the project root.
    Ensures necessary directories exist on initialization.
    """

    ROOT: ClassVar[Path] = Path(__file__).resolve().parent.parents[2]

    PATHS: ClassVar[Dict[str, Path]] = {
        "ORACLE_MEMORY": ROOT / "data/hierophant_nba.db",
        "PROPHETIC_MODELS": ROOT / "models/nba_prediction_models",
        "ICHOR_LOGS": ROOT / "logs/nba_neural_ichor",
        "GAME_DATA": ROOT / "data/nba_game_data",
        "PLAYER_STATS": ROOT / "data/nba_player_stats",
        "ODDS_HISTORY": ROOT / "data/nba_odds_history",
        "TRAINING_DATA": ROOT / "data/nba_training_sets",
        "EVALUATION_REPORTS": ROOT / "reports/nba_evaluations",
        "USER_PREFERENCES": ROOT / "config/user_prefs",
        "CACHE_DIR": ROOT / "temp/cache", # New: Directory for temporary cache files
        "MODEL_ARTIFACTS": ROOT
        / "models/nba_model_artifacts", # New: Directory for model artifacts like ONNX, TF SavedModels
        "RAW_DATA_INGESTION": ROOT
        / "data/raw_ingestion", # New: Directory for raw, uncleaned data
    }

    @classmethod
    @oracle_focus
    def get_path(cls, name: str) -> Path:
        """
        Retrieve a sacred data path by name from the predefined PATHS.

        Args:
        name: The name of the path (e.g., "ORACLE_MEMORY", "PROPHETIC_MODELS").

        Returns:
        The corresponding Path object.

        Raises:
        ValueError: If the path name is unknown.
        """
        path = cls.PATHS.get(name)
        if path is None:
            raise ValueError(f"Unknown vault path: {name}")
        return path

    @classmethod
    def ensure_directories_exist(cls):
        """
        Ensures that all directory paths defined in PATHS exist by creating them if necessary.
        Note: This only creates directories, not the parent directories of file paths (like ORACLE_MEMORY).
        """
        for name, path in cls.PATHS.items():
            if path.suffix:
                dir_to_create = path.parent
            else:
                dir_to_create = path

            try:
                if dir_to_create and not dir_to_create.exists():
                    dir_to_create.mkdir(parents=True, exist_ok=True)
            except Exception:
                pass


class WebConduits(BaseModel):
    """𓃰 Quantum-Entangled Integration Nexus 𓀙

    Configuration for external web integrations like Firebase and WebSockets.
    Includes secure credential handling and hardware-aware optimization potential.
    """

    class FirebaseConfig(BaseModel):
        """ Medusa's Eternal Flame Configuration
        Configuration for Firebase integration."""

        auth_domain: str = Field(
            "nba-medusa.firebaseapp.com", description="Firebase authentication domain"
        )
        project_id: str = Field(
            "nba-neural-vault-123", description="Firebase project ID"
        )
        credentials: SecretStr = Field(
            ...,
            exclude=True,
            description="Firebase service account credentials (JSON string or path)",
        )
        max_connections: int = Field(
            default=10,
            ge=1,
            le=100,
            description="Maximum number of Firebase connections",
        )
        database_url: str = Field(
            "https://nba-medusa-db.firebaseio.com",
            description="Firebase Realtime Database URL",
        )
        storage_bucket: str = Field(
            "nba-medusa.appspot.com", description="Firebase Storage bucket"
        )

        @field_validator("credentials")
        @classmethod
        @oracle_focus
        def validate_credentials(cls, v: SecretStr) -> SecretStr:
            """Validates that Firebase credentials (SecretStr) are provided and not empty."""
            if not v.get_secret_value():
                raise ValueError("Missing Firebase credentials")
            return v

    class WebsocketConfig(BaseModel):
        """𓃰 Real-Time Data Streams
        Configuration for WebSocket connections."""

        url: str = Field(
            "wss://nba-realtime.example.com/live_updates",
            description="URL for the WebSocket connection",
        )
        reconnect_attempts: int = Field(
            5, ge=1, le=20, description="Maximum number of WebSocket reconnect attempts"
        )
        crypto_mode: Literal["aes128", "chacha20"] = Field(
            "aes128", description="Cryptography mode for WebSocket communication"
        )
        ping_interval_seconds: float = Field(
            30.0, ge=5.0, description="Interval in seconds for sending ping frames"
        )
        connection_timeout_seconds: float = Field(
            60.0,
            ge=10.0,
            description="Maximum time in seconds to wait for a connection to establish",
        )

    firebase: "WebConduits.FirebaseConfig" = Field(
        ..., description="Firebase integration configuration"
    )
    websocket: "WebConduits.WebsocketConfig" = Field(
        default_factory=lambda: WebConduits.WebsocketConfig(),
        description="WebSocket connection configuration",
    )


class MythicElements(BaseModel):
    """𓃰 Reality Distortion Aesthetics 𓀙

    Configuration for visual elements and styling, potentially with GPU acceleration.
    """

    class VisualStyle(BaseModel):
        """🧬 Dynamic Visual Genome
        Configuration for visual styling aspects."""

        rune_opacity: float = Field(
            0.15, ge=0.0, le=1.0, description="Opacity level for visual runes"        )
        texture_map: Dict[str, str] = Field(
            default_factory=dict,
            description="Mapping of texture names to URLs or paths",
        )
        gpu_acceleration: bool = Field(
            default=False, description="Enable GPU acceleration for visual effects"
        )
        font_family: str = Field(
            "'Inter', sans-serif", description="Default font family for UI text"
        )
        animation_speed: Literal["slow", "normal", "fast"] = Field(
            "normal", description="Overall animation speed setting"
        )
        
        @field_validator("gpu_acceleration")
        @classmethod
        @oracle_focus
        def enable_gpu_effects(cls, v: bool) -> bool:
            """
            Validates that GPU acceleration is only enabled if CUDA capability is detected.
            """
            if v:
                try:
                    system_has_cuda = cls.HardwareProfile._has_cuda()
                except Exception:
                    # If we can't detect CUDA, assume it's not available
                    system_has_cuda = False
                if not system_has_cuda:
                    return False
            return v

    COLORS: ClassVar[Dict[str, str]] = {
        "lakers_purple": "#552583",
        "lakers_gold": "#FDB927",
        "celtics_green": "#007A33",
        "celtics_white": "#FFFFFF",
        "bulls_red": "#CE1141",
        "bulls_black": "#000000",
        "heat_red": "#98002E",
        "heat_black": "#000000",
        "oracle_gray": "#DAD9D7",
        "ambrosia_violet": "#A250C0",
        "knicks_orange": "#F58426", # New: Knicks Orange
        "knicks_blue": "#006BB6", # New: Knicks Blue
    }

    GRADIENTS: ClassVar[Dict[str, List[str]]] = {
        "court_lines_gradient": ["#F5F5F5", "#D3D3D3"],
        "team_aura_gradient": ["#FDB927", "#552583"],
        "ichor": ["#1BC26E", "#0D8E4D"],
        "twilight": ["#A250C0", "#6A2C8E"],
        "neural_glow_gradient": [
            "#8C00FF",
            "#00FF8C",
        ], # New: Example for a UI glow effect
    }

    STYLES: ClassVar[Dict[str, Any]] = {
        "visual": VisualStyle(
            rune_opacity=0.20,
            texture_map={
                "basketball_court_wood": "url(/assets/textures/nba_court_wood.png)",
                "team_logo_pattern": "url(/assets/textures/team_logo_tile.png)",
                "scoreboard_digital": "url(/assets/textures/digital_led_texture.png)",
            },
            gpu_acceleration=True,
            font_family="'NBA-Pythia', sans-serif", # Using a custom font
            animation_speed="fast", # Faster animations
        ),
        "typography": {
            "primary": "'Inter', sans-serif",
            "titles": "'NBA-Pythia', serif",
            "body": "'Roboto', sans-serif", # Added a body font
        },
    }


class EnvironmentConfig(BaseModel):
    """𓃰 Environmental Adaptability Matrix 𓀙

    Configuration for different deployment environments (development, staging, production).
    """

    environment: Literal["development", "staging", "production", "testing"] = Field(
        "development", description="Current operating environment"
    )
    debug_mode: bool = Field(True, description="Enable or disable debug features")
    api_rate_limit_multiplier: float = Field(
        1.0,
        gt=0,
        description="Multiplier for API rate limits based on environment (e.g., lower in dev)",
    )
    enable_mock_data: bool = Field(
        False, description="Use mock data sources instead of live APIs"
    )
    feature_flags: Dict[str, bool] = Field(
        default_factory=dict,
        description="Dictionary of feature flags specific to the environment",
    )


class DatabaseConfig(BaseModel):
    """𓃰 Data Vault Connection Parameters 𓀙

    Detailed configuration for database connections (e.g., PostgreSQL, SQLite).
    """

    db_type: Literal["sqlite", "postgresql", "mysql"] = Field(
        "sqlite", description="Type of database system"
    )
    host: Optional[str] = Field(None, description="Database host (if not SQLite)")
    port: Optional[int] = Field(None, description="Database port (if not SQLite)")
    username: Optional[str] = Field(
        None, description="Database username (if not SQLite)"
    )
    password: Optional[SecretStr] = Field(
        None, exclude=True, description="Database password (if not SQLite)"
    )
    database: str = Field(..., description="Database name or SQLite file path")
    pool_size: int = Field(10, ge=1, description="Connection pool size")
    timeout_seconds: int = Field(30, ge=5, description="Connection timeout in seconds")

    @model_validator(mode="after")
    @oracle_focus
    def validate_db_settings(self) -> "DatabaseConfig":
        """Validates that host, port, username, password are provided for non-SQLite databases."""
        if self.db_type != "sqlite":
            if not self.host:
                raise ValueError("Host is required for non-SQLite databases.")
            if not self.port:
                raise ValueError("Port is required for non-SQLite databases.")
            if not self.username:
                raise ValueError("Username is required for non-SQLite databases.")
            if not self.password:
                raise ValueError("Password is required for non-SQLite databases.")
        return self


class LoggingConfig(BaseModel):
    """𓃰 Ichor Flow Monitoring 𓀙

    Configuration for application logging.
    """

    level: Literal["MEDUSA_DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"] = Field(
        "INFO", description="Minimum logging level"
    )
    log_file_path: Optional[Path] = Field(
        None, description="Path to the log file (if logging to file)"
    )
    enable_console_logging: bool = Field(
        True, description="Enable or disable logging to console"
    )
    rotation_bytes: Optional[int] = Field(
        10 * 1024 * 1024, ge=0, description="Log file size before rotation (in bytes)"
    ) # 10 MB
    backup_count: Optional[int] = Field(
        5, ge=0, description="Number of backup log files to keep"
    )
    system_level: Literal["DEV", "PROD", "TEST"] = Field(
        "DEV", description="System-level environment for logging context."
    )


class ModelManagementConfig(BaseModel):
    """𓃰 Oracle's Forge: Model Management Parameters 𓀙

    Configuration for machine learning model lifecycle, including MLflow.
    """

    mlflow_tracking_uri: str = Field(
        "http://localhost:5000", description="MLflow tracking server URI"
    )
    mlflow_registry_uri: Optional[str] = Field(
        None, description="MLflow model registry URI (defaults to tracking URI if None)"
    )
    default_training_epochs: int = Field(
        10, ge=1, description="Default number of training epochs for new models"
    )
    default_batch_size: int = Field(
        32, ge=1, description="Default batch size for model training"
    )
    evaluation_metrics: List[str] = Field(
        default_factory=lambda: ["accuracy", "precision", "recall", "f1_score", "rmse"],
        description="Standard metrics to track for model evaluation",
    )
    model_retention_days: int = Field(
        365,
        ge=30,
        description="Number of days to retain old model versions in the registry",
    )
    model_retrain_threshold_f1: float = Field(
        0.75,
        ge=0.0,
        le=1.0,
        description="F1 score threshold below which a model should be considered for retraining",
    )


class SecurityConfig(BaseModel):
    """𓃰 Aegis Shield: Core Security Parameters 𓀙

    Application-wide security configurations.
    """

    secret_key_rotation_days: int = Field(
        90, ge=30, description="Frequency in days for rotating application secret keys"
    )
    tls_version: Literal["TLSv1.2", "TLSv1.3"] = Field(
        "TLSv1.3", description="Minimum TLS version for secure communication"
    )
    data_encryption_algorithm: Literal["AES256", "ChaCha20"] = Field(
        "AES256", description="Preferred algorithm for internal data encryption"
    )
    enable_https_redirection: bool = Field(
        True, description="Enforce HTTPS redirection for all web traffic"
    )
    csp_policy: Optional[str] = Field(
        None, description="Content Security Policy string for web applications"
    )


class NotificationConfig(BaseModel):
    """𓃰 Hermes's Whispers: Notification System Parameters 𓀙

    Configuration for various notification channels and alerts.
    """

    enable_email_alerts: bool = Field(
        False, description="Enable email notifications for critical events"
    )
    email_recipients: List[str] = Field(
        default_factory=list, description="List of email addresses for alert recipients"
    )
    email_sender: str = Field(
        "<EMAIL>", description="Sender email address"
    )
    enable_slack_alerts: bool = Field(
        False, description="Enable Slack notifications for critical events"
    )
    slack_webhook_url: Optional[SecretStr] = Field(
        None, exclude=True, description="Slack webhook URL for posting messages"
    )
    alert_threshold_cpu_usage: float = Field(
        0.80,
        ge=0.0,
        le=1.0,
        description="CPU usage threshold (as a percentage) for triggering alerts",
    )
    alert_threshold_memory_usage: float = Field(
        0.75,
        ge=0.0,
        le=1.0,
        description="Memory usage threshold (as a percentage) for triggering alerts",
    )


class PerformanceTuningConfig(BaseModel):
    """𓃰 Olympian Efficiency: Performance Tuning Parameters 𓀙

    Configuration for optimizing system performance.
    """

    max_thread_pool_workers: int = Field(
        32, ge=1, description="Maximum number of workers in the default thread pool"
    )
    db_operation_timeout_seconds: int = Field(
        10, ge=1, description="Timeout for database operations in seconds"
    )
    api_request_timeout_seconds: int = Field(
        15, ge=1, description="Timeout for external API requests in seconds"
    )
    cache_ttl_seconds: int = Field(
        300, ge=0, description="Default Time-To-Live (TTL) for cached data in seconds"
    )
    max_memory_usage_gb: Optional[float] = Field(
        None,
        ge=0,
        description="Maximum allowed memory usage in GB before warning/throttling",
    )


class DataGovernanceConfig(BaseModel):
    """𓃰 Pantheon's Decree: Data Governance Parameters 𓀙

    Configuration for data retention, privacy, and compliance.
    """

    data_retention_days: int = Field(
        365 * 5,
        ge=30,
        description="Default data retention period in days (e.g., 5 years)",
    )
    enable_gdpr_compliance: bool = Field(
        True,
        description="Enable GDPR compliance features (e.g., data anonymization, right to be forgotten)",
    )
    data_anonymization_strategy: Literal["none", "hashing", "k-anonymity"] = Field(
        "none", description="Strategy for data anonymization"
    )
    data_deletion_frequency_days: int = Field(
        30,
        ge=1,
        description="Frequency in days for performing data deletion/purging tasks",
    )
    audit_log_retention_days: int = Field(
        365 * 7,
        ge=30,
        description="Retention period for audit logs in days (e.g., 7 years)",
    )


# Example Usage (for standalone testing)
if __name__ == "__main__":

    # Initial setup for basic console logging to see tests run
    logging.basicConfig(
        level=logging.DEBUG, format="%(asctime)s - %(levelname)s - %(message)s"
    )
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.DEBUG)

    def test_all_components():
        """
        Runs comprehensive tests for all components of the Vault Manifest.
        """
        logger.info("MEDUSA VAULT: Testing Vault Manifest components started.")

        # --- Test COSMIC_PARAMETERS ---
        for key, value in COSMIC_PARAMETERS.items():
            logger.debug(f"COSMIC_PARAMETER {key}: {value}")
        logger.info("MEDUSA VAULT: COSMIC_PARAMETERS tested.")

        # --- Test VaultIdentity ---

        try:
            hardware_info = VaultIdentity.hardware_profile()
            logger.info("MEDUSA VAULT: Vault Identity hardware profile tested.")
        except Exception as e:
            logger.error(
                f"Error testing VaultIdentity.hardware_profile: {e}", exc_info=True
            )

        # --- Test DivineRealm Enum ---
        try:
            DivineRealm("NonExistentStage")
        except ValueError as e:
            logger.debug(f"Expected ValueError for invalid DivineRealm: {e}")
        logger.info("MEDUSA VAULT: DivineRealm Enum tested.")

        # --- Test FeatureCatalog ---
        for feature in FeatureCatalog:
            logger.debug(f"Feature: {feature.name} - {feature.value}")
        logger.info("MEDUSA VAULT: Feature Catalog tested.")

        # --- Test PropheticSources ---
        try:
            nba_stats_route = PropheticSources.get_route("HOOPS_PANTHEON_STATS_API")
            try:
                PropheticSources.get_route("UNKNOWN_ROUTE")
            except ValueError as e:
                logger.debug(f"Expected ValueError for unknown route: {e}")

            nba_scores_config = PropheticSources.get_endpoint_config("HOOPS_PANTHEON_SCORES_API")

            try:
                quantum_nba_endpoint = PropheticSources.get_endpoint(
                    "QUANTUM_NBA_MODEL"
                )
            except ValueError as e:
                logger.warning(
                    f" Error getting QUANTUM_NBA_MODEL: {e}. It might not be defined in ENDPOINT_CONFIGS."
                )
            except Exception as e:
                logger.error(
                    f"An unexpected error occurred getting QUANTUM_NBA_MODEL: {e}",
                    exc_info=True,
                )

            # Test the new PLAYER_STATS_API endpoint
            player_stats_endpoint = PropheticSources.get_endpoint("PLAYER_STATS_API")
            logger.info(
                f" API Key provided (is SecretStr and not empty): {isinstance(player_stats_endpoint.api_key, SecretStr) and bool(player_stats_endpoint.api_key.get_secret_value())}"
            )
            logger.info(
                f" Backoff Factor: {player_stats_endpoint.retry_policy.backoff_factor}"
            )
            logger.info(
                f" Status Forcelist: {player_stats_endpoint.retry_policy.status_forcelist}"
            )
            logger.info("MEDUSA VAULT: Prophetic Sources tested.")

        except ValueError as e:
            logger.error(f"Error testing PropheticSources: {e}")
        except Exception as e:
            logger.error(
                f"An unexpected error occurred testing PropheticSources: {e}",
                exc_info=True,
            )

        # --- Test VaultPaths ---
        try:
            game_data_path = VaultPaths.get_path("GAME_DATA")
            cache_dir_path = VaultPaths.get_path("CACHE_DIR")

            try:
                VaultPaths.get_path("UNKNOWN_PATH")
            except ValueError as e:
                logger.debug(f"Expected ValueError for unknown path: {e}")

            VaultPaths.ensure_directories_exist()
            logger.info("MEDUSA VAULT: Vault Paths tested.")

        except ValueError as e:
            logger.error(f"Error testing VaultPaths: {e}")

        # --- Test WebConduits ---
        try:
            dummy_firebase_credentials = SecretStr("nba_firebase_secret_key_12345")
            web_conduits_config = WebConduits(
                firebase=WebConduits.FirebaseConfig(
                    credentials=dummy_firebase_credentials
                ),
                websocket=WebConduits.WebsocketConfig(
                    url="wss://nba-live-feed.example.com/scores"
                ),
            )
            logger.info(
                f"Firebase storage_bucket: {web_conduits_config.firebase.storage_bucket}"
            )
            logger.info(
                f"WebSocket ping_interval_seconds: {web_conduits_config.websocket.ping_interval_seconds}"
            )
            logger.info(
                f"WebSocket connection_timeout_seconds: {web_conduits_config.websocket.connection_timeout_seconds}"
            )
            logger.info("MEDUSA VAULT: WebConduits instantiated and fields accessed.")

            try:
                invalid_firebase_config = WebConduits.FirebaseConfig(
                    credentials=SecretStr("")
                )
                WebConduits(firebase=invalid_firebase_config)
            except ValidationError as e:
                logger.debug(f"Expected ValidationError for invalid config: {e}")
            except Exception as e:
                logger.warning(
                    f" Caught unexpected error during validation test: {type(e).__name__}: {e}"
                )

            logger.info("MEDUSA VAULT: WebConduits tested.")

        except ValidationError as e:
            logger.error(
                f"Validation MEDUSA ERROR:stantiating WebConduits: {e.errors()}",
                exc_info=True,
            )
        except Exception as e:
            logger.error(
                f"An unexpected error occurred testing WebConduits: {e}", exc_info=True
            )

        # --- Test MythicElements ---
        try:

            visual_style = MythicElements.STYLES["visual"]
            logger.info(
                f" GPU Acceleration Enabled (after validation): {visual_style.gpu_acceleration}"
            )

            typography_style = MythicElements.STYLES["typography"]

            logger.info("MEDUSA VAULT: Mythic Elements tested.")

        except Exception as e:
            logger.error(
                f"An unexpected error occurred testing MythicElements: {e}",
                exc_info=True,
            )

        # --- Test EnvironmentConfig ---
        try:
            dev_env = EnvironmentConfig(
                environment="development", debug_mode=True, enable_mock_data=True
            )

            prod_env = EnvironmentConfig(
                environment="production",
                debug_mode=False,
                api_rate_limit_multiplier=0.5,
            )
            logger.info("MEDUSA VAULT: EnvironmentConfig tested successfully.")
        except Exception as e:
            logger.error(f"Error testing EnvironmentConfig: {e}", exc_info=True)

        # --- Test DatabaseConfig ---
        try:
            sqlite_db = DatabaseConfig(database="hierophant_nba.db")

            pg_db = DatabaseConfig(
                db_type="postgresql",
                host="localhost",
                port=5432,
                username="vault_user",
                password=SecretStr("supersecurepassword"),
                database="nba_vault",
            )
            logger.info(
                f"PostgreSQL Password (accessed via get_secret_value()): {pg_db.password.get_secret_value()}"
            )

            logger.info(
                "\nTesting DatabaseConfig validation failure (missing host for PostgreSQL):"
            )
            try:
                DatabaseConfig(db_type="postgresql", database="test_db")
            except ValidationError as e:
                logger.debug(f"Expected ValidationError for missing host: {e}")

            logger.info("MEDUSA VAULT: DatabaseConfig tested successfully.")
        except Exception as e:
            logger.error(f"Error testing DatabaseConfig: {e}", exc_info=True)

        # --- Test LoggingConfig ---
        try:
            basic_logging = LoggingConfig()

            file_logging = LoggingConfig(
                level="MEDUSA_DEBUG",
                log_file_path=VaultPaths.get_path("ICHOR_LOGS") / "app.log",
                enable_console_logging=False,
                rotation_bytes=5 * 1024 * 1024, # 5 MB
                backup_count=3,
                system_level="PROD",
            )
            logger.info("MEDUSA VAULT: LoggingConfig tested successfully.")
        except Exception as e:
            logger.error(f"Error testing LoggingConfig: {e}", exc_info=True)

        # --- Test ModelManagementConfig ---
        try:
            model_config = ModelManagementConfig()

            custom_model_config = ModelManagementConfig(
                mlflow_tracking_uri="https://mlflow.yourdomain.com",
                default_training_epochs=50,
                evaluation_metrics=["accuracy", "f1_score"],
            )
            logger.info(
                f"Custom Model Config: {custom_model_config.model_dump_json(indent=2)}"
            )
            logger.info("MEDUSA VAULT: ModelManagementConfig tested successfully.")
        except Exception as e:
            logger.error(f"Error testing ModelManagementConfig: {e}", exc_info=True)

        # --- Test SecurityConfig ---
        try:
            security_config = SecurityConfig()
            logger.info(
                f"Default Security Config: {security_config.model_dump_json(indent=2)}"
            )

            strict_security_config = SecurityConfig(
                secret_key_rotation_days=30,
                tls_version="TLSv1.3",
                data_encryption_algorithm="ChaCha20",
                enable_https_redirection=True,
                csp_policy="default-src 'self'; script-src 'self' 'unsafe-inline';",
            )
            logger.info(
                f"Strict Security Config: {strict_security_config.model_dump_json(indent=2)}"
            )
            logger.info("MEDUSA VAULT: SecurityConfig tested successfully.")
        except Exception as e:
            logger.error(f"Error testing SecurityConfig: {e}", exc_info=True)

        # --- Test NotificationConfig ---
        try:
            default_notification_config = NotificationConfig()
            logger.info(
                f"Default Notification Config: {default_notification_config.model_dump_json(indent=2)}"
            )

            custom_notification_config = NotificationConfig(
                email_recipients=["<EMAIL>"],
                slack_webhook_url=SecretStr(
                    "*****************************************************************************"
                ),
                alert_threshold_cpu_usage=0.9,
                alert_threshold_memory_usage=0.85,
            )
            logger.info(
                f"Custom Notification Config: {custom_notification_config.model_dump_json(indent=2)}"
            )
            logger.info("MEDUSA VAULT: NotificationConfig tested successfully.")
        except Exception as e:
            logger.error(f"Error testing NotificationConfig: {e}", exc_info=True)

        # --- Test PerformanceTuningConfig ---
        try:
            default_perf_config = PerformanceTuningConfig()
            logger.info(
                f"Default Performance Config: {default_perf_config.model_dump_json(indent=2)}"
            )

            optimized_perf_config = PerformanceTuningConfig(
                max_thread_pool_workers=64,
                db_operation_timeout_seconds=5,
                api_request_timeout_seconds=10,
                max_memory_usage_gb=128,
            )
            logger.info(
                f"Optimized Performance Config: {optimized_perf_config.model_dump_json(indent=2)}"
            )
            logger.info("MEDUSA VAULT: PerformanceTuningConfig tested successfully.")
        except Exception as e:
            logger.error(f"Error testing PerformanceTuningConfig: {e}", exc_info=True)

        # --- Test DataGovernanceConfig ---
        try:
            default_data_gov_config = DataGovernanceConfig()
            logger.info(
                f"Default Data Governance Config: {default_data_gov_config.model_dump_json(indent=2)}"
            )

            strict_data_gov_config = DataGovernanceConfig(
                data_retention_days=365 * 7, # 7 years
                enable_gdpr_compliance=True,
                data_anonymization_strategy="k-anonymity",
                data_deletion_frequency_days=7,
            )
            logger.info(
                f"Strict Data Governance Config: {strict_data_gov_config.model_dump_json(indent=2)}"
            )
            logger.info("MEDUSA VAULT: DataGovernanceConfig tested successfully.")
        except Exception as e:
            logger.error(f"Error testing DataGovernanceConfig: {e}", exc_info=True)

        logger.info("MEDUSA VAULT: Vault Manifest testing finished.")

    if __name__ == "__main__":
        test_all_components()
