# vault_oracle/core/vault_config.py

#!/usr/bin/env python3
# DIGITAL FINGERPRINT: UUID=dd4b5c6d-7e8f-9a0b-1c2d-3e4f5a6b7c8d | DATE=2025-06-26
"""
(C) 2025 Hyper Medusa Neural Vault, All Rights Reserved.
PROPRIETARY & CONFIDENTIAL — UNAUTHORIZED USE, COPYING, OR DISTRIBUTION IS STRICTLY PROHIBITED.

This file contains proprietary algorithms, business logic, and intellectual property of Hyper Medusa Neural Vault.
Use, reproduction, or disclosure by any means without explicit written permission is a violation of applicable law.
"""

"""
VAULT_CONFIG.py
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
Defines the Pydantic models for the Hyper Medusa Neural Vault's configuration schema.
This schema is used by vault_loader.py to validate loaded TOML configuration files
and environment variables.
"""

"""
HYPER MEDUSA NEURAL VAULT - Module Business Value Documentation
================================================================

vault_config.py
---------------
Defines the Pydantic models for the Vault's configuration schema. Used by vault_loader.py to validate loaded configuration files and environment variables.

Business Value:
- Ensures robust, type-safe, and validated configuration for the entire platform.
- Reduces runtime errors and misconfiguration risk.
- Supports compliance, auditability, and secure operations for all business-critical services.

For further details, see module-level docstrings and architecture documentation.
"""


import sys
import os
import hmac
import copy
import numpy as np # Used for anomaly detection mock
from pathlib import Path # Used for Path type hints
from typing import Dict, Any, List, Optional, Union, Tuple
from typing import Literal
from typing_extensions import Literal
from enum import Enum # Used for Enum types
import re # Used for regular expressions in validators
import logging # MOVED: Ensure logging is imported at the top
from cryptography.fernet import Fernet # <-- Add this import for Fernet
from vault_oracle.core.oracle_focus import oracle_focus
from vault_oracle.ai.temporal_models import detect_anomalies
from pydantic import (
    BaseModel,
    SecretStr,
    AnyUrl,
    Field,
    field_validator,
    ValidationError,
    model_validator,
    ValidationInfo,
)
from pydantic.types import PositiveInt  # <-- Add this import for PositiveInt
from pydantic import ConfigDict  # <-- Add this import for ConfigDict
import base64
from cryptography.fernet import Fernet
import re

try:
    from pydantic_settings import BaseSettings, SettingsConfigDict
except ImportError:
    # If pydantic_settings is not installed, raise an informative error
    raise ImportError(
        "pydantic_settings is required for environment variable settings. Please install it with 'pip install pydantic-settings'."
    )


# --- Custom Exception Classes (Defined early to ensure availability) ---
class RealmSecurityError(ValueError):
    """Custom exception for security-related issues in realms."""

    pass


class InvalidFernetKeyError(ValueError):
    """Custom exception raised when a Fernet key is invalid or improperly formatted."""

    pass


# --- End Custom Exception Classes ---

# Configure logger for this module (after basic imports, before complex logic)
logger = logging.getLogger(__name__)
# Basic logging configuration (will set up handlers if none exist)
if not logger.handlers:
    handler = logging.StreamHandler(sys.stdout)
    formatter = logging.Formatter(
        "𓀀 %(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)
    logger.propagate = False # Prevent duplicate logging


# --- Pydantic Settings Model for Environment Variables ---
# This model specifically loads configuration from environment variables
class ConfigValidator(BaseSettings):
    """
    Loads and validates configuration from environment variables
    with the 'HYPER_MEDUSA_' prefix using pydantic-settings.
    """

    # Define environment variables expected by the system
    # Use SecretStr for sensitive keys/passwords so they are not easily printed
    # CORRECTED: Removed 'HYPER_MEDUSA_' prefix from field names.
    MEDUSA_ODDS_KEY: Optional[SecretStr] = Field(
        None, description="API Key for Medusa Odds API."
    )
    BALLDONTLIE_KEY: Optional[SecretStr] = Field(
        None, description="API Key for Ballislife (Ball Don't Lie) API."
    )
    AEGIS_KEY: Optional[SecretStr] = Field(
        None, description="Primary encryption key for Aegis defense systems."
    )
    AMBROSIA_KEY: Optional[SecretStr] = Field(
        None,
        description="Encryption key for Ambrosia data channels (e.g., Fernet key).",
    )
    ATLANTIS_KEY: Optional[SecretStr] = Field(
        None, description="Encryption key for Atlantis realm communications."
    )
    ELYSIUM_KEY: Optional[SecretStr] = Field(
        None, description="Encryption key for Elysium realm communications."
    )
    MNEMOSYNE_KEY: Optional[SecretStr] = Field(
        None, description="Encryption key for Mnemosyne Archive (e.g., Fernet key)."
    )
    CONFIG_WEBHOOK: Optional[AnyUrl] = Field(
        None, description="Webhook URL for configuration change notifications."
    )
    SENTRY_DSN: Optional[AnyUrl] = Field(
        None, description="DSN for Sentry error monitoring."
    )
    FIREBASE_KEY: Optional[SecretStr] = Field(
        None, description="Credentials/API Key for Firebase integration."
    )
    VAULT_ENCRYPTION_KEY: Optional[SecretStr] = Field(
        None, description="Master encryption key for the Vault itself."
    )
    PROPHECY_SIGNING_KEY: Optional[SecretStr] = Field(
        None, description="Signing key for verifying prophecy authenticity."
    )
    BUILD_NUMBER: Optional[str] = Field(
        None, description="Build number of the current Vault deployment."
    )

    # Pydantic V2+ configuration for BaseSettings
    model_config = SettingsConfigDict(
        env_prefix="HYPER_MEDUSA_", # Look for env vars starting with this prefix
        env_file=".env", # Load from a .env file by default
        env_file_encoding="utf-8", # Encoding for the .env file
        extra="ignore", # Allow extra environment variables not defined in the model
    )

    # Optional: Add validators for specific environment variables if needed
    # Example validator for AEGIS_KEY length
    @field_validator("AEGIS_KEY") # Corrected field name
    @classmethod
    @oracle_focus # Apply decorator if available
    def validate_aegis_key_length(cls, v: Optional[SecretStr]) -> Optional[SecretStr]:
        if v is not None:
            # Ensure key is at least 32 characters or bytes
            # Fernet keys are 32 bytes (44 chars in URL-safe Base64)
            key_value = v.get_secret_value()
            if len(key_value) < 32: # Example minimum length for a general key
                logger.warning(
                    "AEGIS_KEY seems shorter than recommended (min 32 chars)."
                ) # Corrected field name
                raise ValueError("AEGIS_KEY is too short.") # Corrected field name
        return v

    @field_validator("AMBROSIA_KEY")
    @classmethod
    @oracle_focus
    def validate_ambrosia_key_length(cls, v: Optional[SecretStr]) -> Optional[SecretStr]:
        """Validate Ambrosia key length for quantum encryption compatibility."""
        if v is not None:
            key_value = v.get_secret_value()
            if len(key_value) < 44: # Fernet key length in base64
                logger.warning(" TITAN WARNING: AMBROSIA_KEY should be at least 44 characters (Fernet compatible).")
                raise ValueError("AMBROSIA_KEY is too short for quantum encryption.")
        return v
    
    @field_validator("MNEMOSYNE_KEY")
    @classmethod
    @oracle_focus
    def validate_mnemosyne_key_length(cls, v: Optional[SecretStr]) -> Optional[SecretStr]:
        """Validate Mnemosyne Archive key length for secure archival encryption."""
        if v is not None:
            key_value = v.get_secret_value()
            if len(key_value) < 44: # Fernet key length in base64
                logger.warning(" TITAN WARNING: MNEMOSYNE_KEY should be at least 44 characters (Fernet compatible).")
                raise ValueError("MNEMOSYNE_KEY is too short for secure archival.")
        return v
    
    @field_validator("VAULT_ENCRYPTION_KEY")
    @classmethod
    @oracle_focus
    def validate_vault_encryption_key(cls, v: Optional[SecretStr]) -> Optional[SecretStr]:
        """Validate master vault encryption key for maximum security."""
        if v is not None:
            key_value = v.get_secret_value()
            if len(key_value) < 64: # Higher security requirement for master key
                logger.warning(" TITAN WARNING: VAULT_ENCRYPTION_KEY should be at least 64 characters.")
                raise ValueError("VAULT_ENCRYPTION_KEY is too short for master vault security.")
            
            # Check for valid Fernet key format if it's base64
            try:
                base64.urlsafe_b64decode(key_value.encode() + b'==') # Add padding if needed
                Fernet(key_value.encode()) # Validate as Fernet key
            except Exception:
                logger.info(" MEDUSA VAULT: VAULT_ENCRYPTION_KEY is not in Fernet format, treating as raw key.")
        return v
    
    @field_validator("BUILD_NUMBER")
    @classmethod
    @oracle_focus
    def validate_build_number_format(cls, v: Optional[str]) -> Optional[str]:
        """Validate build number follows semantic versioning."""
        if v is not None:
            # Allow formats like: 2.1.5, v2.1.5-beta, 1.0.0-alpha.1, etc.
            pattern = r'^v?(\d+\.){1,3}\d+(-[a-zA-Z0-9\.\-]+)?$'
            if not re.match(pattern, v):
                logger.warning(f"BUILD_NUMBER '{v}' doesn't follow semantic versioning.")
        return v


# --- Expert Environment Validation Extensions ---
class ExpertEnvironmentValidator:
    """Expert-level environment validation and configuration checks."""
    
    @staticmethod
    @oracle_focus
    def validate_production_readiness(config_validator: ConfigValidator) -> Dict[str, bool]:
        """Validate that all critical keys are present for production deployment."""
        checks = {
            "aegis_defense_ready": config_validator.AEGIS_KEY is not None,
            "ambrosia_channels_secure": config_validator.AMBROSIA_KEY is not None,
            "mnemosyne_archive_encrypted": config_validator.MNEMOSYNE_KEY is not None,
            "vault_encryption_active": config_validator.VAULT_ENCRYPTION_KEY is not None,
            "monitoring_configured": config_validator.SENTRY_DSN is not None,
            "firebase_integration_ready": config_validator.FIREBASE_KEY is not None,
            "prophecy_verification_enabled": config_validator.PROPHECY_SIGNING_KEY is not None,
        }
        
        missing_critical = [k for k, v in checks.items() if not v]
        if missing_critical:
            logger.warning(f"Production readiness check failed. Missing: {missing_critical}")
        else:
            logger.info(" MEDUSA VAULT: All critical environment variables present for production.")
        
        return checks
    
    @staticmethod
    @oracle_focus
    def validate_basketball_intelligence_readiness(config_validator: ConfigValidator) -> bool:
        """Validate environment is ready for basketball intelligence features."""
        api_keys_present = bool(
            config_validator.MEDUSA_ODDS_KEY and 
            config_validator.BALLDONTLIE_KEY
        )
        
        if api_keys_present:
            logger.info(" MEDUSA VAULT: Basketball intelligence API keys configured.")
        else:
            logger.warning(" Basketball intelligence APIs not fully configured.")
        
        return api_keys_present


# --- Pydantic Models for TOML Configuration Structure ---
# Define nested models corresponding to sections in the TOML file (e.g., sacred_config.prod.toml)


class PropheticSourcesConfig(BaseModel):
    """Configuration for external Prophetic Sources (APIs)."""

    class EndpointConfig(BaseModel):
        """Configuration for a single API endpoint."""

        base_url: AnyUrl = Field(***, description="Base URL for the API endpoint.")
        rate_limit: float = Field(
            1.0,
            ge=0.1,
            le=1000.0,
            description="Rate limit for the endpoint (requests per second).",
        ) # Added upper bound
        # Use Literal for allowed authentication modes
        auth_mode: Literal[
            "hmac-sha3", "aes-ocb", "future-proof", "api-key", "oauth2"
        ] = Field(***, description="Authentication mode for the endpoint.")
        failover_endpoints: List[AnyUrl] = Field(
            default_factory=list, description="List of failover endpoint URLs."
        ) # Changed to List[AnyUrl]
        # Use Literal for allowed hardware profiles required for this endpoint
        hardware_profile: Literal["consumer", "enterprise", "quantum-sim"] = Field(
            "consumer", description="Required hardware profile for the endpoint."
        )

        # Add a validator linking auth_mode and hardware_profile if 'future-proof' requires 'quantum-sim'
        @field_validator("auth_mode")
        @classmethod
        @oracle_focus # Apply decorator if available
        def validate_auth_mode_hardware(cls, v: str, info: ValidationInfo) -> str:
            hardware_profile = info.data.get("hardware_profile")
            if v == "future-proof" and hardware_profile != "quantum-sim":
                raise ValueError(
                    "Future-proof auth requires 'quantum-sim' hardware profile"
                )
            return v

    # Define expected endpoints as attributes of this model
    stone_gaze: EndpointConfig = Field(
        ***, description="Configuration for the Stone Gaze API endpoint."
    )
    serpent_path: EndpointConfig = Field(
        ***, description="Configuration for the Serpent Path API endpoint."
    )
    # Add other endpoint configurations here as needed
    # Example:
    # market_gaze: EndpointConfig = Field(***, description="Configuration for the Market Gaze API endpoint.")


class VaultPathsConfig(BaseModel):
    """Configuration for standard Vault data pathways."""

    # Define paths relative to the project root
    # Use Path type hint; Pydantic handles string to Path conversion
    ROOT: Path = Field(***, description="Project root directory.") # Required
    ORACLE_MEMORY: Path = Field(
        ***, description="Path to the Oracle Memory database file."
    )
    PROPHETIC_MODELS: Path = Field(
        ***, description="Directory for saving prophetic models."
    )
    ICHOR_LOGS: Path = Field(***, description="Directory for neural ichor logs.")
    RESIDUAL_ECHOES: Path = Field(
        ***, description="Directory for residual data and evaluation echoes."
    )
    TREASURY_TRACKS: Path = Field(
        ***, description="Directory for user-specific vaults/data."
    )
    # Add other standard vault data paths here

    # Optional: Add validators to ensure paths exist or are valid types
    @field_validator("*") # Apply validator to all fields
    @classmethod
    @oracle_focus # Apply decorator if available
    def resolve_paths(cls, v: Path) -> Path:
        """Resolves Path objects to their absolute path."""
        if isinstance(v, Path):
            # Ensure the path is treated relative to the project root if it's relative
            # This validator is called on the Path object itself after Pydantic parsing the string.
            # Path.resolve() makes it absolute.
            return v.resolve()
        return v # Return value as is if not a Path (shouldn't happen with type hints)


class WebConduitsConfig(BaseModel):
    """Configuration for external Web Integrations (Firebase, WebSockets)."""

    class FirebaseConfig(BaseModel):
        """Configuration for Firebase integration."""

        auth_domain: str = Field(
            "hyper-medusa.firebaseapp.com",
            description="Firebase authentication domain.",
        ) # Default value
        project_id: str = Field(
            "medusa-neural-vault", description="Firebase project ID."
        ) # Default value
        # Credentials field - should be provided securely, possibly via env var loaded by ConfigValidator
        # If loading directly via TOML, it would be a string placeholder expanded by vault_loader.
        # Let's define it as a SecretStr here, which means the loaded value must be compatible.
        # If loaded via env var by ConfigValidator, it would be accessed there.
        # If loaded via TOML after expansion, it must be a string representation.
        # A flexible approach is to accept str or SecretStr. However, SecretStr is safer.
        # Let's assume it's loaded as a SecretStr.
        credentials: SecretStr = Field(
            ***,
            exclude=True,
            description="Firebase service account credentials (JSON string).",
        ) # Required, excluded from representation
        max_connections: int = Field(
            10, ge=1, le=100, description="Maximum number of Firebase connections."
        ) # Default, range check

        # Validator to ensure credentials are not empty
        @field_validator("credentials")
        @classmethod
        @oracle_focus # Apply decorator if available
        def validate_credentials_not_empty(cls, v: SecretStr) -> SecretStr:
            if not v.get_secret_value():
                raise ValueError("Firebase credentials cannot be empty.")
            return v

    class WebsocketConfig(BaseModel):
        """Configuration for WebSocket connections."""

        url: AnyUrl = Field(
            "wss://vault-engine.com/live",
            description="URL for the WebSocket connection.",
        ) # Default URL
        reconnect_attempts: int = Field(
            5,
            ge=1,
            le=20,
            description="Maximum number of WebSocket reconnect attempts.",
        ) # Default, range check
        # Use Literal for allowed crypto modes
        crypto_mode: Literal["aes128", "chacha20", "tls"] = Field(
            "aes128", description="Cryptography mode for WebSocket communication."
        ) # Default, added 'tls'

    # Define attributes for this model
    firebase: FirebaseConfig = Field(
        ***, description="Firebase integration configuration."
    ) # Required nested config
    websocket: WebsocketConfig = Field(
        default_factory=WebsocketConfig,
        description="WebSocket connection configuration.",
    ) # Optional nested config with default
    # Example flat field from mock - ensure it matches the model
    # "WEBSOCKET_URL": "wss://realtime.example.com/updates_flat"


class SecurityConfig(BaseModel):
    """Configuration for various security aspects."""

    retry_rituals: int = Field(
        3, ge=1, le=10, description="Number of retry attempts for transient operations."
    ) # Default, range check
    minotaur_gate: bool = Field(
        True, description="Enable Minotaur Gate (example security feature)."
    ) # Default boolean
    harpy_shield: int = Field(
        1024, ge=64, description="Harpy Shield strength (example security parameter)."
    ) # Default, min value
    gorgon_heartbeat: int = Field(
        60, ge=10, description="Interval (in seconds) for Gorgon heartbeat checks."
    ) # Default, min value
    # Add other general security parameters here


class SystemConfig(BaseModel):
    """General system and logging configuration."""

    log_level: Literal["MEDUSA_DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"] = Field(
        "INFO", description="Minimum logging level."
    ) # Default log level
    environment: Literal["development", "staging", "production", "mortal"] = Field(
        "development", description="Deployment environment."
    ) # Default environment
    max_workers: int = Field(
        os.cpu_count() or 1,
        ge=1,
        description="Maximum number of worker processes/threads.",
    ) # Default to CPU count or 1
    whisper_level: Literal["SILENT", "VERBOSE"] = Field(
        "VERBOSE", description="Verbosity level for system whispers (example)."
    ) # Default verbosity
    # Add other system parameters here


class AmbrosiaGatesConfig(BaseModel):
    """Configuration for Ambrosia Data Gates."""

    threshold: float = Field(
        5.0, description="Threshold value for passing Ambrosia gates."
    ) # Example float field from loader dummy TOML
    # Add other ambrosia gates parameters here


class ClusterRealmConfig(BaseModel):
    """Configuration for a single Cluster Realm endpoint (based on previous TOML snippet)."""

    # Fields directly from the [cluster_realms.realms.REALM_NAME] TOML structure
    latency_weight: float = Field(
        ***,
        ge=0.0,
        le=1.0,
        description="Weight assigned to latency for routing decisions.",
    )
    endpoint: AnyUrl = Field(
        ***, description="Base URL for the Cluster Realm endpoint."
    )
    failover_priority: int = Field(
        ***,
        ge=1,
        description="Priority for failover (lower number is higher priority).",
    )
    # Encryption key for the realm - assuming loaded as SecretStr
    encryption_key: SecretStr = Field(
        ***, description="Encryption key for communication with this realm."
    )
    timeout: float = Field(
        ***, ge=0.1, description="Request timeout (in seconds) for this realm."
    )
    health_check_endpoint: Optional[AnyUrl] = Field(
        None, description="Optional health check endpoint URL for this realm."
    )
    # Add other realm-specific parameters here


class RoutingConfig(BaseModel):
    """Configuration for Moirai's routing strategy and Cluster Realms (matches [cluster_realms] TOML section)."""

    # Fields directly from the [cluster_realms] TOML structure (top level)
    routing_strategy: Literal[
        "hybrid", "latency", "priority", "round-robin", "least_connections"
    ] = Field(
        "hybrid", description="Strategy for routing requests."
    ) # Added other literal options
    max_retries: int = Field(
        5, ge=0, description="Maximum number of retries for routing requests."
    )
    health_check_interval: float = Field(
        3.0, ge=0.1, description="Interval (in seconds) for health checks of realms."
    )

    # This dictionary holds configurations for individual realms
    # Keys are realm names (strings), values are ClusterRealmConfig instances
    realms: Dict[str, ClusterRealmConfig] = Field(
        default_factory=dict, description="Configuration for individual cluster realms."
    )


class NeuralIchorConfig(BaseModel):
    """Configuration for Neural Ichor processing."""

    activation_threshold: float = Field(
        0.8, ge=0.0, le=1.0, description="Activation threshold for neural ichor flow."
    ) # Default, range check
    # Add other neural ichor related parameters here


class TemporalConfig(BaseModel):
    """Configuration for temporal processes and rifts."""

    sync_interval_sec: int = Field(
        60, ge=1, description="Default synchronization interval in seconds."
    ) # Default, min value
    prophecy_refresh: int = Field(
        30, ge=1, description="Interval (in seconds) for refreshing prophecy data."
    ) # Example timeout from loader dummy TOML
    # Add other temporal or timeout related parameters here


class MnemosyneCoreConfig(BaseModel):
    """Configuration for Mnemosyne Archive Core."""

    capacity_gb: int = Field(
        200, ge=1, description="Maximum capacity of the archive in GB."
    ) # Example from loader dummy TOML
    retention_days: int = Field(
        365 * 5, ge=30, description="Data retention period in days."
    ) # Example, 5 years
    # Add other archive related parameters here


class MonitoringConfig(BaseModel):
    """Configuration for Celestial Monitoring systems."""

    alert_threshold: float = Field(
        0.9, ge=0.0, le=1.0, description="Threshold for triggering celestial alerts."
    ) # Default, range check
    # Add other monitoring related parameters here


# --- Language Configuration Models ---


class LanguageConfig(BaseModel):
    """Quantum linguistic configuration for divine response generation (Nested within LinguisticSanctum)."""

    lexicon_path: Path = (
        Field( # Changed to Path based on vault_loader mock and type consistency
            default="vault_oracle/sacred_scrolls/quantum_lexicon.json",
            description="Path to the quantum language lexicon.",
        )
    )
    response_templates: List[str] = Field(
        default_factory=lambda: [
            "The stars reveal {prophecy}.",
            "In the weave of fate, {prophecy} emerges.",
            "The Oracle speaks: {prophecy}.",
            "Celestial signs favor {prophecy}.",
        ],
        description="Default response templates for prophecy generation.",
    )
    fallback_template: str = Field(
        default=" The visions are unclear, but something stirs in the ether.",
        description="Fallback response template if prophecy context is unclear.",
    )
    # Add other language generation parameters here


class LinguisticSanctum(BaseModel):
    """Sacred configuration for Olympian communication protocols and language settings (matches [linguistic_sanctum] TOML section)."""

    olympian_tongue: Literal["en", "el", "la"] = Field(
        default="en",
        description="Primary divine language (ISO 639-1): en=English, el=Greek, la=Latin",
    )

    mortal_dialects: List[
        Literal["en", "es", "fr", "de", "zh", "it", "pt", "ru", "ja", "ko"]
    ] = Field( # Added more literal options
        default_factory=lambda: ["en"],
        description="Allowed mortal vernaculars for prophecy delivery",
    )

    hermes_translation: bool = Field(
        default=False, description="Enable Hermes' gift of tongues (auto-translation)"
    )

    # Validator for language codes - applies to both olympian_tongue and mortal_dialects
    @field_validator("olympian_tongue", "mortal_dialects")
    @classmethod
    @oracle_focus # Apply decorator if available
    def validate_language_codes(
        cls, value: Union[str, List[str]]
    ) -> Union[str, List[str]]: # Added type hint for validator value
        """Verify language codes conform to a predefined set of allowed codes."""
        # Combined set of all allowed language codes
        allowed_codes = {
            "en",
            "el",
            "la",
            "es",
            "fr",
            "de",
            "zh",
            "it",
            "pt",
            "ru",
            "ja",
            "ko",
        }

        if isinstance(value, list):
            # Validate each code in the list of mortal dialects
            invalid_dialects = [lang for lang in value if lang not in allowed_codes]
            if invalid_dialects:
                # Optional: Add logging
                # import logging; logger = logging.getLogger(__name__); logger.error(f"Invalid mortal dialects found: {invalid_dialects}")
                raise ValueError(f"Forbidden dialects: {invalid_dialects}")
        elif isinstance(value, str):
            # Validate the single olympian_tongue code
            if value not in allowed_codes:
                # Optional: Add logging
                # logger.error(f"Divine tongue '{value}' not recognized by Pantheon.")
                raise ValueError(f"Divine tongue not recognized by Pantheon: {value}")
        # Note: If value is neither list nor str, Pydantic's type checking might catch it earlier.
        return value # Return the validated value(s)

    # Method to check if a dialect is supported
    @oracle_focus # Apply decorator if available
    def is_mortal_supported(self, dialect: str) -> bool:
        """Check if a mortal dialect is sanctioned by the Fates (configured)."""
        return dialect in self.mortal_dialects

    # Nest the LanguageConfig model within LinguisticSanctum
    # This represents the [linguistic_sanctum.generation_settings] section in TOML
    generation_settings: LanguageConfig = Field(
        default_factory=LanguageConfig,
        description="Configuration for prophecy generation language.",
    )

    # Example JSON Schema Extra (kept from user's snippet)
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "olympian_tongue": "en",
                "mortal_dialects": ["en", "es"],
                "hermes_translation": True,
                # Include example for the nested generation_settings
                "generation_settings": {
                    "lexicon_path": "vault_oracle/sacred_scrolls/quantum_lexicon.json",
                    "response_templates": ["Template 1", "Template 2"],
                    "fallback_template": "Fallback",
                },
            }
        }
    )


# --- Neural Network Configuration Model ---


class NeuralConfig(BaseModel):
    """Configuration for Neural Network settings (matches [neural_settings] TOML section)."""

    model_registry: Dict[str, Path] = Field(
        ***, description="Mapping of model names to their file paths."
    ) # Changed value type to Path
    # Use PositiveInt and add validation for power of 2
    ichor_dimensions: PositiveInt = Field(
        ***,
        description="Dimension size for neural ichor tensors (must be a power of 2).",
    )
    temporal_weights: List[float] = Field(
        ***,
        description="Temporal weighting factors for model inputs (must sum to approx 1.0).",
    )

    # Validator for ichor_dimensions: must be a power of 2
    @field_validator("ichor_dimensions")
    @classmethod
    @oracle_focus
    def validate_power_of_two(cls, v: PositiveInt) -> PositiveInt:
        """
        Validates that ichor_dimensions is a positive integer and a power of 2.
        """
        # The condition for a positive integer being a power of 2 is v > 0 AND (v & (v - 1)) == 0.
        # Pydantic's PositiveInt already ensures v > 0.
        if not (v & (v - 1) == 0): # Bitwise check for power of 2
            # Optional: Add logging
            # import logging; logger = logging.getLogger(__name__); logger.error(f"ichor_dimensions {v} is not a power of 2.")
            raise ValueError("ichor_dimensions must be a power of 2")
        return v # Validator must return the validated value

    # Validator for temporal_weights: must sum to approximately 1.0
    @field_validator("temporal_weights")
    @classmethod
    @oracle_focus
    def validate_weight_sum(cls, v: List[float]) -> List[float]:
        """
        Validates that the sum of temporal weights is approximately 1.0.
        """
        if (
            not v
        ): # Handle empty list case - should probably have a min_length or not_empty constraint elsewhere or here
            # Optional: Add logging
            # import logging; logger = logging.getLogger(__name__); logger.error(" MEDUSA ERROR: Temporal weights list is empty.")
            raise ValueError("Temporal weights list cannot be empty")

        total_sum = sum(v)
        # Define a small tolerance for floating-point comparison
        tolerance = 1e-3 # Allow for a difference of up to 0.001 (adjust as needed)

        if abs(total_sum - 1.0) > tolerance:
            # Optional: Add logging
            # logger.error(f"Temporal weights sum {total_sum:.4f} is not approximately 1.0.")
            raise ValueError(
                f"Temporal weights must sum to approximately 1.0 (sum is {total_sum:.4f})"
            )

        return v # Validator must return the validated value


# --- Timeout Configuration Model and Enum ---


class ChronosPatienceConfig(BaseModel):
    """Olympian temporal thresholds for mortal interactions (matches [chronos_patience] TOML section)."""

    oracle_response: float = Field(
        default=10.0,
        ge=0.1,
        le=60.0,
        description="Max seconds before Delphi's Oracle withdraws her voice",
    )

    celestial_streams: float = Field(
        default=5.0,
        ge=0.1,
        le=30.0,
        description="Starlight channel persistence before Hermes recalls his wings",
    )

    phoenix_dispatch: float = Field(
        default=7.0,
        ge=0.1,
        le=15.0,
        description="Ashes-to-flame resurrection window for message rebirth",
    )

    # Validator for individual timeouts - ensures they are not too short based on their default values
    @field_validator("oracle_response", "celestial_streams", "phoenix_dispatch")
    @classmethod
    @oracle_focus
    def validate_time_hierarchy(
        cls, v: float, info: ValidationInfo
    ) -> float: # Added type hints
        """Ensure timeouts follow divine order (minimum thresholds relative to defaults)."""
        # Replicating original logic using hardcoded thresholds from snippet example, assuming they relate to defaults
        thresholds = {
            "oracle_response": 5.0, # Example: Was 10.0 * 0.5 in original logic
            "celestial_streams": 2.5, # Example: Was 5.0 * 0.5 in original logic
            "phoenix_dispatch": 3.5, # Example: Was 7.0 * 0.5 in original logic
        }
        field_name = info.field_name # Corrected access to field name
        if field_name in thresholds and v < thresholds[field_name]:
            raise ValueError(
                f"{field_name} ({v:.2f}s) too impatient for divine workings (must be >= {thresholds[field_name]:.2f}s)."
            )

        return v # Validator must return the validated value

    # Model validator for cross-field checks (like phoenix_dispatch vs oracle_response)
    @model_validator(mode="after")
    @oracle_focus
    def validate_chronos_relationships(self) -> "ChronosPatienceConfig":
        """
        Performs cross-field validation for ChronosPatienceConfig values.
        Ensures phoenix_dispatch is less than oracle_response.
        """
        if self.phoenix_dispatch >= self.oracle_response:
            # Optional: Add logging
            # import logging; logger = logging.getLogger(__name__); logger.error(f"Phoenix dispatch ({self.phoenix_dispatch:.2f}s) must be less than Oracle response ({self.oracle_response:.2f}s).")
            raise ValueError(
                f"Phoenix dispatch ({self.phoenix_dispatch:.2f}s) must be less than Oracle response ({self.oracle_response:.2f}s)"
            )
        # Add other cross-field validation logic here if needed
        return self # Return the validated model instance

    @oracle_focus # Apply decorator if available
    def get_chronos_limits(self) -> dict:
        """Return timeouts as Moirai's measured threads."""
        return {
            "prophecy": self.oracle_response,
            "starlight": self.celestial_streams,
            "rebirth": self.phoenix_dispatch,
        }

    # Example JSON Schema Extra (kept from user's snippet) - applies to the Enum itself
    # Pydantic's schema generation for Enums is usually just a list of allowed values.
    # model_config = ConfigDict( # Use model_config for Pydantic v2+
    # json_schema_extra = {
    # "example": {
    # "MORTAL_HASTE": [0.1, 5.0],
    # "HEROIC_PATIENCE": [5.1, 15.0],
    # "TITANIC_FORBEARANCE": [15.1, 60.0]
    # }
    # }
    # )


# --- Live Configuration Model ---


class LiveConfig(BaseModel):
    """Configuration for Live data processing."""

    class ObservablesConfig(BaseModel):
        interval: int = Field(
            10, ge=1, description="Interval (in seconds) for observing live data."
        )

    observables: ObservablesConfig = Field(
        default_factory=ObservablesConfig,
        description="Configuration for live observable data.",
    )
    # Add other live config parameters here


class UiConfig(BaseModel):
    """Configuration for User Interface aesthetics and constraints."""

    class BreakpointsConfig(BaseModel):
        small: int = Field(480, ge=0, description="Small screen breakpoint width.")
        medium: int = Field(768, ge=0, description="Medium screen breakpoint width.")
        large: int = Field(1024, ge=0, description="Large screen breakpoint width.")
        xlarge: int = Field(
            1280, ge=0, description="Extra large screen breakpoint width."
        )

    class ConstraintsConfig(BaseModel):
        max_width: float = Field(
            1440.0, ge=0.0, description="Maximum width for UI content."
        )

    breakpoints: BreakpointsConfig = Field(
        default_factory=BreakpointsConfig, description="UI responsive breakpoints."
    )
    constraints: ConstraintsConfig = Field(
        default_factory=ConstraintsConfig, description="UI layout constraints."
    )
    # Add other UI config parameters here


class VaultIdentityConfig(BaseModel):
    """Configuration for Vault identity."""

    name: str = Field(***, description="Name of this specific Vault instance.")
    # Add other identity parameters here


# --- Olympian Guard Security Configuration Model and Enum ---
# MOVED: Defined before VaultConfig
class OlympianGuardConfig(BaseModel):
    """Hephaestus-forged protections for divine vaults (matches [hephaestus_security] TOML section)."""

    chaos_key: SecretStr = Field(
        ***,
        min_length=44,
        max_length=44,
        description="Primordial encryption key (Fernet 32-byte base64)",
    )

    hermes_token: SecretStr = Field(
        ***, min_length=32, description="Hermes' message verification sigil (32+ chars)"
    )

    ambrosia_secret: SecretStr = Field(
        ***, min_length=64, description="Nectar of divine authentication (64+ chars)"
    )

    cyclops_lockdown: int = Field(
        default=3,
        ge=3,
        le=7,
        description="Cyclops strikes before Tartarus seals (3-7 attempts)",
    )

    # Validator for chaos_key: Fernet key structure and length
    @field_validator("chaos_key")
    @classmethod
    @oracle_focus
    def validate_chaos_pattern(cls, v: SecretStr) -> SecretStr: # Added type hint
        """Verify Fernet key structure (URL-safe base64) and exact length validation."""
        key = v.get_secret_value()
        # Fernet keys are URL-safe base64 encoded 32-byte keys.
        # A 32-byte key encoded in base64 is exactly 44 characters long.
        # A simple regex check for URL-safe characters and exact length validation.
        if not re.fullmatch(r"^[A-Za-z0-9\-_]*={0,2}$", key) or len(key) != 44:
            # Corrected regex to match URL-safe base64 characters and updated length check to 44
            # Added logging for clarity on failure
            # import logging; logger = logging.getLogger(__name__); logger.error(f"Chaos key validation failed: invalid format or length ({len(key)}).")
            raise ValueError(
                "Chaos key must be a valid 44-character URL-safe base64 Fernet key."
            )

        # Optional: Could also try to decode using Fernet.
        # try:
        # Fernet(key.encode('utf-8'))
        # except Exception:
        # raise ValueError("Chaos key is not a valid Fernet key.")

        return v # Validator must return the validated value

    # Validator for hermes_token: complexity checks
    @field_validator("hermes_token")
    @classmethod
    @oracle_focus
    def validate_hermes_sigil(cls, v: SecretStr) -> SecretStr: # Added type hint
        """Ensure token complexity (requires uppercase and symbols)."""
        token = v.get_secret_value()
        # Original symbols: "!@#$%^&*" - let's use a more general symbol check
        symbol_pattern = re.compile(
            r'[!@#$%^&*()_+=\-[\]{};\':"\\|,.<>/?`~]'
        ) # Example symbol set

        if not any(c.isupper() for c in token):
            # Optional: Add logging
            # import logging; logger = logging.getLogger(__name__); logger.error(" MEDUSA ERROR: Hermes token missing uppercase characters.")
            raise ValueError("Hermes' sigil requires at least one uppercase letter.")
        if not bool(symbol_pattern.search(token)): # Check if any symbol is present
            # Optional: Add logging
            # import logging; logger = logging.getLogger(__name__); logger.error(" MEDUSA ERROR: Hermes token missing divine symbols.")
            raise ValueError(
                "Sigil needs at least one divine symbol (!@#$%^&*()_+=-[]{}|;':\"\\,.<>/?`~)."
            ) # Include the set of symbols

        return v # Validator must return the validated value

    # Validator for cyclops_lockdown: must be an odd number
    @field_validator("cyclops_lockdown")
    @classmethod
    @oracle_focus
    def validate_cyclops_rage(cls, v: int) -> int: # Added type hint
        """Enforce Hephaestus' safety ratios (must be an odd number)."""
        if v % 2 == 0:
            # Optional: Add logging
            # import logging; logger = logging.getLogger(__name__); logger.error(f"Cyclops lockdown value {v} is even.")
            raise ValueError(
                "Cyclops prefers odd-numbered wrath (cyclops_lockdown must be odd)."
            )
        return v # Validator must return the validated value

    @oracle_focus # Apply decorator if available
    def generate_fernet(self) -> "Fernet":
        """Forge encryption from primordial chaos."""
        # The chaos_key SecretStr is already validated, get its secret value (string)
        # Fernet requires the key to be bytes, so encode the string using utf-8
        try:
            fernet_key_bytes = self.chaos_key.get_secret_value().encode("utf-8")
            return Fernet(fernet_key_bytes)
        except Exception as e:
            # Optional: Log error
            # import logging; logger = logging.getLogger(__name__); logger.error(f" TITAN PROCESSING FAILED: create Fernet instance from chaos_key: {e}", exc_info=True)
            raise ValueError(
                f" TITAN PROCESSING FAILED: create Fernet instance from chaos_key: {e}"
            ) # Re-raise as a ValueError

    # Example JSON Schema Extra (kept from user's snippet)
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                # Note: Fernet.generate_key() returns bytes, need to decode for JSON string
                "chaos_key": Fernet.generate_key().decode(),
                "hermes_token": "H3rm3s@Tr1ck3ry$2025",
                "ambrosia_secret": "AmbrosiaTastesLike$*********************************",
                "cyclops_lockdown": 5,
            }
        }
    )


# Enum for classifying security tiers
# MOVED: Defined before VaultConfig
class SecurityTier(Enum):
    """Divine protection classifications based on key strength (bit length)."""

    TITANIC = 256 # Key length in bits
    OLYMPIAN = 192
    MORTAL = 128

    @classmethod
    def get_tier(cls, key_length: int) -> str:
        """
        Classify security by key strength (bit length).

        Args:
        key_length: The key length in bits (integer).

        Returns:
        The name of the highest SecurityTier member whose value (bit length)
        is less than or equal to the provided key_length. Returns "CHAOS"
        if the key length is less than the lowest defined tier.
        """
        for tier in sorted(cls, key=lambda t: t.value, reverse=True):
            if tier.value <= key_length:
                return tier.name
        return "CHAOS"

    # Example JSON Schema Extra (kept from user's snippet) - applies to the Enum itself
    # Note: JSON Schema Extra for Enums might have limited use compared to Models.
    # Pydantic's schema generation for Enums is usually just a list of allowed values.
    # model_config = ConfigDict( # Use model_config for Pydantic v2+
    # json_schema_extra = {
    # "example": {
    # "MORTAL_HASTE": [0.1, 5.0],
    # "HEROIC_PATIENCE": [5.1, 15.0],
    # "TITANIC_FORBEARANCE": [15.1, 60.0]
    # }
    # }
    # )


# --- Features Configuration Model ---
# MOVED: Defined before VaultConfig
class FeaturesConfig(BaseModel):
    """
    Configuration for enabling/disabling various features in the Vault.
    Each field represents a feature that can be turned on (True) or off (False).
    """

    enable_shap_analysis: bool = Field(
        False,
        description="Enable SHAP (SHapley Additive exPlanations) analysis for model interpretability.",
    )
    enable_realtime_alerts: bool = Field(
        False, description="Enable real-time alerting system for critical events."
    )
    enable_quantum_recalibration: bool = Field(
        False,
        description="Enable quantum recalibration protocols for advanced neural network tuning.",
    )
    enable_backup_gateways: bool = Field(
        False,
        description="Enable redundant data gateways for enhanced fault tolerance.",
    )
    enable_advanced_logging: bool = Field(
        False,
        description="Enable verbose and detailed logging for debugging and audit trails.",
    )
    enable_ichor_flow: bool = Field(
        False,
        description="Control the activation of ichor flow, managing vital neural data streams.",
    )
    enable_ichor_purge: bool = Field(
        False,
        description="Allow the system to purge stale or corrupt ichor data, optimizing memory.",
    )
    enable_ichor_recovery: bool = Field(
        False,
        description="Activate ichor recovery protocols to restore lost or damaged neural data.",
    )
    enable_ichor_rebalance: bool = Field(
        False,
        description="Enable ichor rebalancing across neural nodes for optimal load distribution.",
    )
    enable_ichor_reclaim: bool = Field(
        False,
        description="Permit the system to reclaim dormant ichor resources, enhancing efficiency.",
    )
    enable_ichor_recycle: bool = Field(
        False,
        description="Initiate ichor recycling, transforming discarded data into new neural potential.",
    )
    enable_ichor_reuse: bool = Field(
        False,
        description="Enable the reuse of ichor fragments, reducing computational overhead.",
    )
    enable_ichor_retain: bool = Field(
        False,
        description="Specify if ichor data should be retained for historical analysis or training.",
    )
    enable_ichor_retain_all: bool = Field(
        False,
        description="Force retention of all ichor data, overriding individual retention policies.",
    )

    # Add other feature flags as boolean fields here


# --- Pantheon Routing Configuration Model ---
# MOVED: Defined before VaultConfig
class PantheonRouting(BaseModel):
    """
    Configuration for Pantheon-level cosmic message routing, including celestial pathways.
    This manages how messages are routed to different divine realms or services.
    """

    class CelestialPathConfig(BaseModel):
        """
        Configuration for a single celestial pathway within the Pantheon routing.
        """

        celestial_gate: AnyUrl = Field(
            ***, description="The URL of the celestial gateway endpoint."
        )
        fate_weight: float = Field(
            ***,
            ge=0.0,
            le=1.0,
            description="The weight assigned to this pathway for traffic distribution (0.0 to 1.0).",
        )
        divine_priority: int = Field(
            ***,
            ge=1,
            description="The priority of this pathway for failover (lower number is higher priority).",
        )

        @model_validator(mode="after")
        @oracle_focus
        def check_positive_priority_if_active(self):
            """Ensure active paths have a positive divine_priority."""
            if self.fate_weight > 0 and self.divine_priority < 1:
                raise ValueError(
                    "Active celestial paths (fate_weight > 0) must have a divine_priority >= 1."
                )
            return self

    weaving_strategy: Literal[
        "hybrid", "latency", "priority", "round-robin", "least_connections"
    ] = Field(
        "hybrid",
        description="The strategy used for weaving celestial messages (e.g., hybrid, latency, priority).",
    )
    phoenix_retries: int = Field(
        3,
        ge=0,
        description="Number of times to retry sending a message if a celestial path fails.",
    )
    celestial_paths: Dict[str, CelestialPathConfig] = Field(
        default_factory=dict,
        description="A dictionary mapping celestial pathway names to their configurations.",
    )

    @model_validator(mode="after")
    @oracle_focus
    def validate_total_fate_weight(self) -> "PantheonRouting":
        """
        Ensures that the sum of 'fate_weight' for all celestial paths is approximately 1.0
        if there are any paths defined.
        """
        if not self.celestial_paths:
            # If no paths are defined, this validation is skipped for weight sum.
            # You might want to enforce a minimum number of paths if necessary.
            return self

        total_weight = sum(path.fate_weight for path in self.celestial_paths.values())
        tolerance = 1e-3
        if abs(total_weight - 1.0) > tolerance:
            raise ValueError(
                f"Total fate_weight for all celestial paths must sum to approximately 1.0, but got {total_weight:.4f}."
            )
        return self

    @model_validator(mode="after")
    @oracle_focus
    def validate_unique_divine_priorities(self) -> "PantheonRouting":
        """
        Ensures that all divine_priority values across celestial paths are unique.
        """
        if not self.celestial_paths:
            return self

        priorities = [path.divine_priority for path in self.celestial_paths.values()]
        if len(priorities) != len(set(priorities)):
            raise ValueError("Divine priorities for celestial paths must be unique.")
        return self

    @oracle_focus
    def get_traffic_distribution(self) -> Dict[str, float]:
        """
        Calculates the traffic distribution based on fate_weight for each celestial path.
        """
        if not self.celestial_paths:
            return {}
        return {name: path.fate_weight for name, path in self.celestial_paths.items()}

    @oracle_focus
    def get_failover_sequence(self) -> List[str]:
        """
        Returns the celestial pathway names ordered by their divine_priority for failover.
        Lower priority number means higher failover precedence.
        """
        return [
            name
            for name, path in sorted(
                self.celestial_paths.items(), key=lambda item: item[1].divine_priority
            )
        ]


# --- VitalityConfig Model for OracleMemory ---
class VitalityConfig(BaseModel):
    memory_path: str = Field(***, description="Path to the Oracle memory database.")
    encryption_key: str = Field(***, description="Encryption key for Oracle memory.")


# --- Main Vault Configuration Model ---
# This is the top-level schema for the entire TOML configuration file
class VaultConfig(BaseModel):
    """
    The comprehensive configuration schema for the Hyper Medusa Neural Vault.
    Matches the structure of the sacred_config.prod.toml file.
    """

    # Each attribute corresponds to a top-level section in the TOML file
    prophetic_sources: PropheticSourcesConfig = Field(
        ***, description="Configuration for external API data sources."
    )
    vault_paths: VaultPathsConfig = Field(
        ***, description="Configuration for data storage paths."
    )
    hermes_conduits: WebConduitsConfig = Field(
        ***, description="Configuration for web integrations (Firebase, WebSockets)."
    )
    aegis_defense: SecurityConfig = Field(
        ***, description="General security settings."
    ) # General security settings
    system_config: SystemConfig = Field(
        ***, description="General system and logging configuration."
    )
    ambrosia_gates: AmbrosiaGatesConfig = Field(
        ***, description="Configuration for Ambrosia data gates."
    )
    # This field corresponds to the [cluster_realms] TOML section and uses the RoutingConfig model
    moirai_routing: RoutingConfig = Field(
        ***, description="Configuration for Moirai's routing strategy and realms."
    )
    neural_ichor: NeuralIchorConfig = Field(
        ***, description="Configuration for Neural Ichor processing."
    )
    temporal_rifts: TemporalConfig = Field(
        ***, description="Configuration for temporal processes and rifts."
    )
    mnemosyne_core: MnemosyneCoreConfig = Field(
        ***, description="Configuration for Mnemosyne Archive core."
    )
    # This field corresponds to the [linguistic_sanctum] TOML section and uses the LinguisticSanctum model
    linguistic_sanctum: LinguisticSanctum = Field(
        ***,
        description="Configuration for the Linguistic Sanctum and language protocols.",
    )
    ichor_flow: NeuralIchorConfig = Field(
        ***, description="Configuration for Ichor Flow processing."
    ) # Assuming this is distinct or uses the same model as neural_ichor
    celestial_monitoring: MonitoringConfig = Field(
        ***, description="Configuration for Celestial Monitoring systems."
    )
    # Update this field to use the new OlympianGuardConfig model for Hephaestus' specific security
    hephaestus_security: OlympianGuardConfig = Field(
        ***,
        description="Configuration for Hephaestus' forged security credentials and parameters.",
    )
    features: FeaturesConfig = Field(
        ***, description="Enabled/disabled status for various Vault features."
    )
    timeouts: TemporalConfig = Field(
        ***, description="Specific timeout configurations."
    ) # Assuming this is distinct or uses the same model as temporal_rifts
    live_config: LiveConfig = Field(
        ***, description="Configuration for live data ingestion and processing."
    )
    ui_config: UiConfig = Field(***, description="Configuration for User Interface.")
    divine_identity: VaultIdentityConfig = Field(
        ***, description="Configuration for this Vault instance's identity."
    )

    # Add the new NeuralConfig model as a field in VaultConfig
    neural_settings: NeuralConfig = Field(
        ***, description="Configuration for neural network settings."
    )

    # Add the new ChronosPatienceConfig model as a field in VaultConfig
    chronos_patience: ChronosPatienceConfig = Field(
        ***, description="Configuration for specific Chronos patience (timeouts)."
    )

    # Add the new PantheonRouting model as a field in VaultConfig
    pantheon_routing: PantheonRouting = Field(
        ***, description="Configuration for Pantheon-level cosmic message routing."
    ) # Add the new VitalityConfig model as a field in VaultConfig
    vitality_config: VitalityConfig = Field(
        ***,
        description="Configuration for OracleMemory vitality (memory path, encryption key, etc.)",
    )

    # Dictionary-like compatibility methods for backward compatibility
    def get(self, key: str, default=None):
        """Dictionary-like get method for backward compatibility."""
        try:
            return getattr(self, key, default)
        except AttributeError:
            return default
    
    def __getitem__(self, key):
        """Dictionary-like access for backward compatibility."""
        try:
            return getattr(self, key)
        except AttributeError:
            raise KeyError(f"'{key}' not found in VaultConfig")

    # Optional: Add a model_validator for cross-field validation if needed
    # @model_validator(mode='after')
    # @oracle_focus # Apply decorator if available
    # def check_related_configs(self) -> 'VaultConfig':
    # # Example: Ensure log_level from system_config is compatible with whisper_level
    # # This requires access to logging setup, which might not be available here.
    # # Cross-field validation better suited for logical dependencies within the config itself.
    # if self.system_config.log_level == 'SILENT' and self.system_config.whisper_level == 'VERBOSE':
    # logger.warning(" TITAN WARNING: System log level is SILENT but whisper level is VERBOSE. May cause unexpected behavior.")
    # # Add other cross-field validation logic here
    # return self


# Example Usage (for standalone testing of Pydantic models)
if __name__ == "__main__":
    # This example demonstrates instantiating the Pydantic models directly with dictionaries.
    # vault_loader.py is responsible for loading this data from a TOML file.

    # import logging # REMOVED: Already imported at the top
    # Configure basic logging for standalone execution if no handlers exist
    if not logging.getLogger().hasHandlers():
        logging.basicConfig(
            level=logging.INFO, # Corrected: MEDUSA_DEBUG is not a standard logging level
            format="%(asctime)s - %(levelname)s - %(message)s"
        )
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.DEBUG) # Changed to DEBUG for example output

    logger.info(" MEDUSA VAULT: Testing Vault Config Pydantic models started.")

    # Example dictionary simulating loaded configuration data (matching the schema)
    # This data structure must match the VaultConfig model hierarchy.
    simulated_config_data: Dict[str, Any] = {
        "prophetic_sources": {
            "stone_gaze": {
                "base_url": "https://api.example.com/stone/v1",
                "rate_limit": 5.0,
                "auth_mode": "api-key",
            },
            "serpent_path": {
                "base_url": "https://api.example.com/serpent/v2",
                "rate_limit": 1.0,
                "auth_mode": "oauth2",
            },
        },
        "vault_paths": {
            # Use strings here which Pydantic will convert to Path objects
            "ROOT": "./",
            "ORACLE_MEMORY": "./data/hierophant.db",
            "PROPHETIC_MODELS": "./models",
            "ICHOR_LOGS": "./logs/ichor",
            "RESIDUAL_ECHOES": "./data/echoes",
            "TREASURY_TRACKS": "./user_data",
        },
        "hermes_conduits": {
            "firebase": {
                "auth_domain": "my-vault.firebaseapp.com",
                "project_id": "my-vault-123",
                "credentials": SecretStr(
                    '{"type": "service_account", "project_id": "test", "private_key_id": "***", "private_key": "-----BEGIN PRIVATE KEY-----\n***\n-----END PRIVATE KEY-----\n", "client_email": "***", "client_id": "***", "auth_uri": "***", "token_uri": "***", "auth_provider_x509_cert_url": "***", "client_x509_cert_url": "***", "universe_domain": "***"}'
                ), # Pass SecretStr directly or string
                "max_connections": 50,
            },
            "websocket": {
                "url": "wss://realtime.example.com/updates",
                "reconnect_attempts": 10,
                "crypto_mode": "tls",
            },
            # Example flat field from mock - ensure it matches the model
            # "WEBSOCKET_URL": "wss://realtime.example.com/updates_flat"
        },
        "aegis_defense": { # This uses the general SecurityConfig model
            "retry_rituals": 3,
            "minotaur_gate": True,
            "harpy_shield": 2048,
            "gorgon_heartbeat": 30,
        },
        "system_config": {
            "log_level": "INFO", # Corrected: MEDUSA_DEBUG is not a standard logging level
            "environment": "development",
            "max_workers": 16,
            "whisper_level": "VERBOSE",
        },
        "ambrosia_gates": {"threshold": 7.5},
        "moirai_routing": { # This field corresponds to the [cluster_realms] TOML section
            "routing_strategy": "latency",
            "max_retries": 3,
            "health_check_interval": 5.0,
            "realms": {
                "ATLANTIS": {
                    "latency_weight": 0.7, # FIXED: must be <= 1.0
                    "endpoint": "https://atlantis.prophecy-engine.com",
                    "failover_priority": 1,
                    "encryption_key": SecretStr(
                        "TRQa5_gCb05uwsq6BvlnyiYIdU0O_n8CS-sQTJ24Jaw="
                    ),
                    "timeout": 2.5,
                    "health_check_endpoint": "https://atlantis-status.prophecy-engine.com/health",
                },
                "ELYSIUM": {
                    "latency_weight": 0.3,
                    "endpoint": "https://elysium.prophecy-engine.com",
                    "failover_priority": 2,
                    "encryption_key": SecretStr(
                        "gH3_Zlw6vPR4l7oLohgOIkm2338SxsWfaYLdCBLnueQ="
                    ),
                    "timeout": 3.0,
                },
                # Add other realms here
            },
        },
        "neural_ichor": {"activation_threshold": 0.9},
        "temporal_rifts": {
            "sync_interval_sec": 120,
            "prophecy_refresh": 60, # Example timeout field
        },
        "mnemosyne_core": {
            "capacity_gb": 1024, # Example capacity field
            "retention_days": 365, # Example retention field
        },
        "linguistic_sanctum": { # This field corresponds to the [linguistic_sanctum] TOML section
            "olympian_tongue": "el", # Example language code
            "mortal_dialects": ["en", "es", "fr"], # Example list of language codes
            "hermes_translation": True, # Example boolean
            "generation_settings": { # This nested dictionary corresponds to [linguistic_sanctum.generation_settings]
                # Use strings here which Pydantic will convert to Path objects
                "lexicon_path": "vault_oracle/sacred_scrolls/my_lexicon.json",
                "response_templates": [
                    "Template A: {prophecy}",
                    "Template B: {prophecy}",
                ],
                "fallback_template": "Visions are obscured.",
            },
        },
        "ichor_flow": { # Assuming same model as neural_ichor
            "activation_threshold": 0.88
        },
        "celestial_monitoring": {"alert_threshold": 0.99},
        "hephaestus_security": { # Updated to use OlympianGuardConfig model
            # Re-generated to ensure 44 characters and valid base64
            "chaos_key": SecretStr(Fernet.generate_key().decode()),
            "hermes_token": SecretStr(
                "SomeComplexT0ken!@#$%^&*MoreThan32Chars"
            ), # Example valid token
            "ambrosia_secret": SecretStr(
                "ThisIsASecretForAmbrosiaThatMustBeAtLeast64CharactersLongABCDEFG"
            ), # Extended to 65 characters
            "cyclops_lockdown": 5, # Example valid odd number
        },
        "features": { # Updated to use the new FeaturesConfig model
            "enable_shap_analysis": True,
            "enable_realtime_alerts": True,
            "enable_quantum_recalibration": False, # Example turned off
            "enable_backup_gateways": True,
            "enable_advanced_logging": True,
            "enable_ichor_flow": True,
            "enable_ichor_purge": True,
            "enable_ichor_recovery": True,
            "enable_ichor_rebalance": True,
            "enable_ichor_reclaim": True,
            "enable_ichor_recycle": True,
            "enable_ichor_reuse": True,
            "enable_ichor_retain": True,
            "enable_ichor_retain_all": False, # Example turned off
        },
        "timeouts": { # Assuming same model as temporal_rifts
            "sync_interval_sec": 300,
            "prophecy_refresh": 180, # Example timeout field
        },
        "live_config": {"observables": {"interval": 15}}, # Example nested field
        "ui_config": {
            "breakpoints": {"small": 480, "medium": 768, "large": 1024, "xlarge": 1280},
            "constraints": {"max_width": 1920.0},
        },
        "divine_identity": {"name": "OracleUnitBeta"},
        "neural_settings": { # New field for NeuralConfig
            "model_registry": { # Use strings which Pydantic converts to Path
                "model_v1": "./models/model_v1.pth",
                "model_v2": "./models/model_v2.pth",
            },
            "ichor_dimensions": 128, # Example power of 2
            "temporal_weights": [0.5, 0.3, 0.2], # Example weights summing to 1.0
        },
        "chronos_patience": { # New field for ChronosPatienceConfig
            "oracle_response": 15.0, # Example valid value (>= 5.0)
            "celestial_streams": 6.0, # Example valid value (>= 2.5)
            "phoenix_dispatch": 8.0, # Example valid value (>= 3.5) and (< 15.0)
        },
        "pantheon_routing": { # New field for PantheonRouting
            "weaving_strategy": "hybrid", # Example strategy name
            "phoenix_retries": 3, # Example retry count
            "celestial_paths": { # Dictionary of pathways
                "pathway_alpha": {
                    "celestial_gate": "https://alpha.example.com/route", # Example URL
                    "fate_weight": 0.6, # FIXED: sum is 1.0
                    "divine_priority": 1, # Example priority
                },
                "pathway_beta": {
                    "celestial_gate": "https://beta.example.com/route", # Example URL
                    "fate_weight": 0.4, # FIXED: sum is 1.0
                    "divine_priority": 2, # FIXED: unique priority
                },
            },
        },
        "vitality_config": {
            "memory_path": "./data/oracle_memory.db",
            "encryption_key": "dummy_encryption_key_123456789012345678901234567890", # Example key meeting length check
        },
    }

    # Example dictionary with invalid data to test validation
    simulated_invalid_config_data = copy.deepcopy(simulated_config_data)
    # Create mutable copies of nested dicts for invalid data testing
    simulated_invalid_config_data["system_config"] = simulated_invalid_config_data[
        "system_config"
    ].copy()
    simulated_invalid_config_data["moirai_routing"] = simulated_invalid_config_data[
        "moirai_routing"
    ].copy()
    simulated_invalid_config_data["moirai_routing"]["realms"] = (
        simulated_invalid_config_data["moirai_routing"]["realms"].copy()
    )
    simulated_invalid_config_data["linguistic_sanctum"] = simulated_invalid_config_data[
        "linguistic_sanctum"
    ].copy()
    simulated_invalid_config_data["hermes_conduits"] = simulated_invalid_config_data[
        "hermes_conduits"
    ].copy()
    simulated_invalid_config_data["hermes_conduits"]["firebase"] = (
        simulated_invalid_config_data["hermes_conduits"]["firebase"].copy()
    )
    simulated_invalid_config_data["neural_settings"] = simulated_invalid_config_data[
        "neural_settings"
    ].copy()
    simulated_invalid_config_data["neural_settings"]["model_registry"] = (
        simulated_invalid_config_data["neural_settings"]["model_registry"].copy()
    )
    simulated_invalid_config_data["chronos_patience"] = simulated_invalid_config_data[
        "chronos_patience"
    ].copy()
    simulated_invalid_config_data["hephaestus_security"] = (
        simulated_invalid_config_data["hephaestus_security"].copy()
    )
    simulated_invalid_config_data["pantheon_routing"] = simulated_invalid_config_data[
        "pantheon_routing"
    ].copy()
    simulated_invalid_config_data["pantheon_routing"]["celestial_paths"] = (
        simulated_invalid_config_data["pantheon_routing"]["celestial_paths"].copy()
    )
    simulated_invalid_config_data["features"] = simulated_invalid_config_data[
        "features"
    ].copy() # Make features mutable

    # Introduce invalid values
    simulated_invalid_config_data["system_config"]["log_level"] = "INVALID_LEVEL"
    simulated_invalid_config_data["moirai_routing"]["realms"]["ATLANTIS"][
        "latency_weight"
    ] = 1.5 # Out of range (0.0 to 1.0)
    simulated_invalid_config_data["linguistic_sanctum"]["mortal_dialects"] = [
        "en",
        "invalid_lang",
    ] # Invalid language code
    simulated_invalid_config_data["hermes_conduits"]["firebase"]["credentials"] = (
        SecretStr("")
    ) # Empty SecretStr
    # Introduce invalid values for neural_settings
    simulated_invalid_config_data["neural_settings"][
        "ichor_dimensions"
    ] = 100 # Not a power of 2
    simulated_invalid_config_data["neural_settings"]["temporal_weights"] = [
        0.6,
        0.3,
        0.05,
    ] # Sum is 0.95 (outside default tolerance)
    simulated_invalid_config_data["neural_settings"]["model_registry"][
        "invalid_path"
    ] = "not a path" # Example - Pydantic will try to make it a Path
    # Introduce invalid values for chronos_patience
    simulated_invalid_config_data["chronos_patience"][
        "oracle_response"
    ] = 4.0 # Too impatient (< 5.0)
    simulated_invalid_config_data["chronos_patience"][
        "phoenix_dispatch"
    ] = 16.0 # Too long (> 15.0) and violates cross-field check (> oracle_response default 10.0)
    # Introduce invalid values for hephaestus_security
    simulated_invalid_config_data["hephaestus_security"]["chaos_key"] = SecretStr(
        "too_short_key"
    ) # Invalid format and length
    simulated_invalid_config_data["hephaestus_security"]["hermes_token"] = SecretStr(
        "noupperorsymbols"
    ) # Lacks uppercase and symbols
    simulated_invalid_config_data["hephaestus_security"]["ambrosia_secret"] = SecretStr(
        "short_secret"
    ) # Too short
    simulated_invalid_config_data["hephaestus_security"][
        "cyclops_lockdown"
    ] = 4 # Even number
    # Introduce invalid values for pantheon_routing
    simulated_invalid_config_data["pantheon_routing"]["celestial_paths"][
        "pathway_alpha"
    ][
        "fate_weight"
    ] = 0.5 # Change sum of weights (0.5 + 0.4 = 0.9)
    simulated_invalid_config_data["pantheon_routing"]["celestial_paths"][
        "pathway_beta"
    ][
        "divine_priority"
    ] = 1 # Duplicate priority
    # Introduce invalid values for features (though FeaturesConfig doesn't have complex validators yet)
    # simulated_invalid_config_data["features"]["enable_non_existent_feature"] = True # Example of adding unexpected field

    try:
        # Instantiate the main VaultConfig model with the simulated data
        # Pydantic performs validation automatically upon instantiation
        config_instance = VaultConfig(**simulated_config_data)

        print("\n VaultConfig model instantiated and validated successfully with valid data.")
        logger.info(" MEDUSA VAULT: VaultConfig model instantiation test successful with valid data.")

        # Example of accessing loaded configuration values
        print(f"Stone Gaze Base URL: {config_instance.prophetic_sources.stone_gaze.base_url}")
        print(f"Atlantis Endpoint: {config_instance.moirai_routing.realms['ATLANTIS'].endpoint}")
        # Accessing SecretStr value requires .get_secret_value() (avoid printing sensitive values in production logs)
        print(f"Firebase Project ID: {config_instance.hermes_conduits.firebase.project_id}")
        print(f"Root Path (resolved): {config_instance.vault_paths.ROOT}") # Resolved by validator
        print(f"Is SHAP Analysis Feature Enabled? {config_instance.features.enable_shap_analysis}") # Accessing a new feature flag
        print(f"Is Quantum Recalibration Feature Enabled? {config_instance.features.enable_quantum_recalibration}") # Accessing a new feature flag
        print(f"Supported Mortal Dialects: {config_instance.linguistic_sanctum.mortal_dialects}")
        print(f"Linguistic Lexicon Path (resolved): {config_instance.linguistic_sanctum.generation_settings.lexicon_path}") # Nested path resolved
        # Accessing neural_settings values
        print(f"Neural Ichor Dimensions: {config_instance.neural_settings.ichor_dimensions}")
        print(f"Neural Temporal Weights: {config_instance.neural_settings.temporal_weights}")
        print(f"Neural Model Registry: {config_instance.neural_settings.model_registry}")
        # Accessing chronos_patience values
        print(f"Chronos Oracle Response Timeout: {config_instance.chronos_patience.oracle_response}")
        print(f"Chronos Celestial Streams Timeout: {config_instance.chronos_patience.celestial_streams}")
        print(f"Chronos Phoenix Dispatch Timeout: {config_instance.chronos_patience.phoenix_dispatch}")
        print(f"Hephaestus Cyclops Lockdown: {config_instance.hephaestus_security.cyclops_lockdown}")
        
        # Accessing pantheon_routing values
        print(f"Pantheon Routing Strategy: {config_instance.pantheon_routing.weaving_strategy}") # Accessing Enum value
        print(f"Pantheon Routing Phoenix Retries: {config_instance.pantheon_routing.phoenix_retries}")
        print(f"Pantheon Routing Celestial Paths: {config_instance.pantheon_routing.celestial_paths}")
        # Test methods on PantheonRouting
        print(f"Pantheon Traffic Distribution: {config_instance.pantheon_routing.get_traffic_distribution()}")
        print(f"Pantheon Failover Sequence: {config_instance.pantheon_routing.get_failover_sequence()}")

        # Test the generate_fernet method
        try:
            fernet_instance = config_instance.hephaestus_security.generate_fernet()
            print(f"Hephaestus Fernet Instance Created: {type(fernet_instance).__name__}")
        except Exception as e:
            logger.error(f"Error creating Fernet instance: {e}", exc_info=True)

        # Test the get_chronos_limits method
        print(f"Chronos Limits Dict: {config_instance.chronos_patience.get_chronos_limits()}")
        # Test the TimeoutTier Enum get_tier method - REMOVED, as TimeoutTier is not defined here.

        # Test the SecurityTier Enum get_tier method

        # Test the is_mortal_supported method
        print(f"Is 'es' dialect supported? {config_instance.linguistic_sanctum.is_mortal_supported('es')}")
        print(f"Is 'zh' dialect supported? {config_instance.linguistic_sanctum.is_mortal_supported('zh')}")

    except ValidationError as e:
        # Print validation errors in a readable format
        logger.error(
            "VaultConfig model validation failed unexpectedly with valid data.",
            exc_info=True,
        )
        logger.error(e.errors()) # Log errors specifically

    except Exception as e:
        logger.critical(
            "An unexpected error occurred during valid data testing.", exc_info=True
        )

    try:
        # Attempt to instantiate with invalid data - should raise ValidationError
        invalid_config_instance = VaultConfig(**simulated_invalid_config_data)
        # If we reach here, validation failed unexpectedly
        print("\n VaultConfig model instantiation with invalid data DID NOT raise ValidationError.")
        print("Unexpectedly validated invalid data:",
              invalid_config_instance.model_dump_json(indent=2),
        ) # Show the invalid instance
        logger.error(
            "VaultConfig model instantiation with invalid data DID NOT raise ValidationError."
        )

    except ValidationError as e:
        # This is the expected outcome for invalid data
        print("\n Caught expected ValidationError during instantiation with invalid data.")
        logger.info(" MEDUSA VAULT: Successfully caught expected ValidationError with invalid data.")

    except Exception as e:
        logger.critical(
            "An unexpected error occurred during invalid data testing.", exc_info=True
        )
    # This part tests the BaseSettings model for loading env vars.
    # Note: This requires actual environment variables to be set (e.g., via .env file or OS).
    # Set some dummy environment variables for testing ConfigValidator
    os.environ["HYPER_MEDUSA_AEGIS_KEY"] = (
        "dummy_aegis_key_env_123456789012345678901234567890"
    ) # Example key meeting length check

    try:
        env_settings = ConfigValidator(
            _settings_read_config=SettingsConfigDict(env_file=None)
        )
        print("\n ConfigValidator instantiated and environment variables loaded successfully.")
        if env_settings.SENTRY_DSN:
            print(f"Loaded SENTRY_DSN (string value): {env_settings.SENTRY_DSN.unicode_string() if hasattr(env_settings.SENTRY_DSN, 'unicode_string') else str(env_settings.SENTRY_DSN)}")
        print(f"Loaded BUILD_NUMBER: {env_settings.BUILD_NUMBER}")
        # Accessing SecretStr env var - use .get_secret_value() (avoid printing sensitive data)

        # Test validation failure for a validator (e.g., AEGIS_KEY length)
        os.environ["HYPER_MEDUSA_AEGIS_KEY"] = "short_key" # Set a short key
        try:
            # Explicitly set env_file=None for this test.
            invalid_env_settings = ConfigValidator(
                _settings_read_config=SettingsConfigDict(env_file=None)
            ) # This should trigger the validator warning/error
            print(" ConfigValidator did NOT raise warning/error for short key as expected.")
            # If it didn't raise, check the validated value and log warning
            if (
                invalid_env_settings.AEGIS_KEY
                and len(invalid_env_settings.AEGIS_KEY.get_secret_value()) < 32
            ): # Corrected field name
                logger.warning(
                    "ConfigValidator accepted short AEGIS_KEY but validator logged a warning."
                ) # Corrected field name

        except ValidationError as e: # BaseSettings raises ValidationError on failure
            print(" Caught expected ValidationError for short AEGIS_KEY.") # Corrected field name

        except Exception as e:
            logger.error(f"Error testing ConfigValidator: {e}", exc_info=True)

    finally:
        # Clean up dummy environment variables
        for var in [
            "HYPER_MEDUSA_AEGIS_KEY",
            "HYPER_MEDUSA_SENTRY_DSN",
            "HYPER_MEDUSA_BUILD_NUMBER",
            "HYPER_MEDUSA_FIREBASE_KEY",
        ]:
            if var in os.environ:
                del os.environ[var]
    logger.info(" MEDUSA VAULT: Vault Config Pydantic models testing finished.")
