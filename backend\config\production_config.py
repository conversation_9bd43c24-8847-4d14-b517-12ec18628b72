import os
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass

#!/usr/bin/env python3
"""
Production Configuration System
Provides production-ready configuration management
"""


logger = logging.getLogger("production_config")

@dataclass
class DatabaseConfig:
    """Database configuration settings"""
    url: str = os.getenv("DATABASE_URL", "postgresql://localhost/hyper_medusa")
    pool_size: int = int(os.getenv("DB_POOL_SIZE", "20"))
    max_overflow: int = int(os.getenv("DB_MAX_OVERFLOW", "30"))
    pool_timeout: int = int(os.getenv("DB_POOL_TIMEOUT", "30"))
    pool_recycle: int = int(os.getenv("DB_POOL_RECYCLE", "3600"))
    echo: bool = os.getenv("DB_ECHO", "false").lower() == "true"

@dataclass
class RedisConfig:
    """Redis configuration settings"""
    url: str = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    max_connections: int = int(os.getenv("REDIS_MAX_CONNECTIONS", "20"))
    socket_timeout: int = int(os.getenv("REDIS_SOCKET_TIMEOUT", "5"))
    socket_connect_timeout: int = int(os.getenv("REDIS_CONNECT_TIMEOUT", "5"))
    retry_on_timeout: bool = os.getenv("REDIS_RETRY_ON_TIMEOUT", "true").lower() == "true"

@dataclass
class ProductionConfig:
    """Production configuration settings"""
    
    # Database settings
    database_url: str = os.getenv("DATABASE_URL", "postgresql://localhost/hyper_medusa")
    database_pool_size: int = int(os.getenv("DB_POOL_SIZE", "20"))
    database_max_overflow: int = int(os.getenv("DB_MAX_OVERFLOW", "30"))
    
    # Redis settings
    redis_url: str = os.getenv("REDIS_URL", "redis://localhost:6379")
    redis_max_connections: int = int(os.getenv("REDIS_MAX_CONNECTIONS", "100"))
    
    # API settings
    api_host: str = os.getenv("API_HOST", "0.0.0.0")
    api_port: int = int(os.getenv("API_PORT", "8000"))
    api_workers: int = int(os.getenv("API_WORKERS", "4"))
    
    # Security settings
    secret_key: str = os.getenv("SECRET_KEY", "your-secret-key-here")
    jwt_algorithm: str = os.getenv("JWT_ALGORITHM", "HS256")
    jwt_expiration: int = int(os.getenv("JWT_EXPIRATION", "3600"))
    
    # ML/AI settings
    model_cache_size: int = int(os.getenv("MODEL_CACHE_SIZE", "1000"))
    prediction_batch_size: int = int(os.getenv("PREDICTION_BATCH_SIZE", "100"))
    
    # Monitoring settings
    enable_metrics: bool = os.getenv("ENABLE_METRICS", "true").lower() == "true"
    metrics_port: int = int(os.getenv("METRICS_PORT", "9090"))
    
    # Environment
    environment: str = os.getenv("ENVIRONMENT", "development")
    debug: bool = os.getenv("DEBUG", "false").lower() == "true"

class ProductionConfigManager:
    """Production configuration manager"""
    
    def __init__(self):
        self.config = ProductionConfig()
        self._validate_config()
    
    def _validate_config(self):
        """Validate production configuration"""
        if self.config.environment == "production":
            if self.config.secret_key == "your-secret-key-here":
                raise ValueError("Production secret key must be set")
            if self.config.debug:
                logger.warning("Debug mode enabled in production")
    
    def get_config(self) -> ProductionConfig:
        """Get production configuration"""
        return self.config
    
    def is_production(self) -> bool:
        """Check if running in production"""
        return self.config.environment == "production"
    
    def get_database_config(self) -> Dict[str, Any]:
        """Get database configuration"""
        return {
            "url": self.config.database_url,
            "pool_size": self.config.database_pool_size,
            "max_overflow": self.config.database_max_overflow
        }
    
    def get_redis_config(self) -> Dict[str, Any]:
        """Get Redis configuration"""
        return {
            "url": self.config.redis_url,
            "max_connections": self.config.redis_max_connections
        }

# Global production config manager
production_config_manager = ProductionConfigManager()

def get_production_config() -> ProductionConfig:
    """Get production configuration"""
    return production_config_manager.get_config()

def is_production_environment() -> bool:
    """Check if running in production environment"""
    return production_config_manager.is_production()
