import sqlite3
import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Any, Tuple
import hashlib
from datetime import datetime

#!/usr/bin/env python3
"""
Data Deduplication Analysis
Check for and remove duplicate data before ML training to ensure valid results
"""


# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataDeduplicationAnalysis:
    """Analyze and remove duplicate data from our comprehensive dataset"""
    
    def __init__(self, db_path: str = "medusa_master.db"):
        self.db_path = db_path
        
    def analyze_duplicates(self) -> Dict[str, Any]:
        """Comprehensive duplicate analysis"""
        logger.info("🔍 ANALYZING DATASET FOR DUPLICATES")
        logger.info("=" * 50)
        
        conn = sqlite3.connect(self.db_path)
        
        # Get basic dataset info
        total_query = "SELECT COUNT(*) FROM unified_nba_wnba_data WHERE league_name = 'WNBA'"
        total_records = pd.read_sql_query(total_query, conn).iloc[0, 0]
        
        logger.info(f"📊 Total WNBA records: {total_records:,}")
        
        # Check for exact duplicates
        exact_duplicates = self._check_exact_duplicates(conn)
        
        # Check for semantic duplicates (same player, same stat, same value)
        semantic_duplicates = self._check_semantic_duplicates(conn)
        
        # Check for source file duplicates
        source_duplicates = self._check_source_file_duplicates(conn)
        
        # Check for player game log duplicates
        gamelog_duplicates = self._check_gamelog_duplicates(conn)
        
        conn.close()
        
        return {
            'total_records': total_records,
            'exact_duplicates': exact_duplicates,
            'semantic_duplicates': semantic_duplicates,
            'source_duplicates': source_duplicates,
            'gamelog_duplicates': gamelog_duplicates
        }
    
    def _check_exact_duplicates(self, conn: sqlite3.Connection) -> Dict[str, Any]:
        """Check for exact row duplicates"""
        logger.info("🔍 Checking for exact duplicates...")
        
        # Check exact duplicates across all key fields
        query = """
            SELECT 
                player_name, season, stat_category, stat_value, 
                data_category, game_date, team_abbreviation,
                COUNT(*) as duplicate_count
            FROM unified_nba_wnba_data 
            WHERE league_name = 'WNBA'
            GROUP BY player_name, season, stat_category, stat_value, 
                     data_category, game_date, team_abbreviation
            HAVING COUNT(*) > 1
            ORDER BY duplicate_count DESC
        """
        
        duplicates_df = pd.read_sql_query(query, conn)
        
        if not duplicates_df.empty:
            total_duplicate_records = duplicates_df['duplicate_count'].sum() - len(duplicates_df)
            logger.warning(f"⚠️ Found {len(duplicates_df)} groups of exact duplicates")
            logger.warning(f"⚠️ Total duplicate records: {total_duplicate_records:,}")
            
            # Show top duplicates
            logger.info("📋 Top duplicate groups:")
            for _, row in duplicates_df.head(5).iterrows():
                logger.info(f"   • {row['player_name']}: {row['stat_category']} = {row['stat_value']} ({row['duplicate_count']} copies)")
        else:
            logger.info("✅ No exact duplicates found")
        
        return {
            'duplicate_groups': len(duplicates_df),
            'total_duplicate_records': duplicates_df['duplicate_count'].sum() - len(duplicates_df) if not duplicates_df.empty else 0,
            'sample_duplicates': duplicates_df.head(10).to_dict('records') if not duplicates_df.empty else []
        }
    
    def _check_semantic_duplicates(self, conn: sqlite3.Connection) -> Dict[str, Any]:
        """Check for semantic duplicates (same meaning, different source)"""
        logger.info("🔍 Checking for semantic duplicates...")
        
        # Check for same player stats from different sources
        query = """
            SELECT 
                player_name, season, stat_value,
                GROUP_CONCAT(DISTINCT source_file) as sources,
                GROUP_CONCAT(DISTINCT data_category) as categories,
                COUNT(DISTINCT source_file) as source_count,
                COUNT(*) as total_records
            FROM unified_nba_wnba_data 
            WHERE league_name = 'WNBA'
            AND stat_value IS NOT NULL
            GROUP BY player_name, season, stat_value
            HAVING COUNT(DISTINCT source_file) > 1 AND COUNT(*) > 2
            ORDER BY total_records DESC
        """
        
        semantic_df = pd.read_sql_query(query, conn)
        
        if not semantic_df.empty:
            logger.warning(f"⚠️ Found {len(semantic_df)} potential semantic duplicates")
            logger.info("📋 Sample semantic duplicates:")
            for _, row in semantic_df.head(3).iterrows():
                logger.info(f"   • {row['player_name']} ({row['season']}): {row['stat_value']} from {row['source_count']} sources")
        else:
            logger.info("✅ No obvious semantic duplicates found")
        
        return {
            'semantic_groups': len(semantic_df),
            'sample_semantics': semantic_df.head(10).to_dict('records') if not semantic_df.empty else []
        }
    
    def _check_source_file_duplicates(self, conn: sqlite3.Connection) -> Dict[str, Any]:
        """Check for duplicates from same source files"""
        logger.info("🔍 Checking source file patterns...")
        
        # Analyze source file distribution
        query = """
            SELECT 
                source_file,
                data_type,
                COUNT(*) as record_count,
                COUNT(DISTINCT player_name) as unique_players
            FROM unified_nba_wnba_data 
            WHERE league_name = 'WNBA'
            GROUP BY source_file, data_type
            ORDER BY record_count DESC
        """
        
        source_df = pd.read_sql_query(query, conn)
        
        logger.info("📊 Top source files by record count:")
        for _, row in source_df.head(10).iterrows():
            logger.info(f"   • {row['source_file']}: {row['record_count']:,} records, {row['unique_players']} players")
        
        # Check for suspicious patterns
        suspicious_sources = source_df[source_df['record_count'] > 50000]
        
        return {
            'total_source_files': len(source_df),
            'suspicious_large_sources': len(suspicious_sources),
            'source_distribution': source_df.head(20).to_dict('records')
        }
    
    def _check_gamelog_duplicates(self, conn: sqlite3.Connection) -> Dict[str, Any]:
        """Check for duplicates in player game logs specifically"""
        logger.info("🔍 Checking player game log duplicates...")
        
        # Check game log specific duplicates
        query = """
            SELECT 
                player_name,
                game_date,
                COUNT(*) as log_count
            FROM unified_nba_wnba_data 
            WHERE league_name = 'WNBA'
            AND data_type = 'player_gamelogs'
            AND game_date IS NOT NULL
            GROUP BY player_name, game_date
            HAVING COUNT(*) > 1
            ORDER BY log_count DESC
        """
        
        gamelog_dupes = pd.read_sql_query(query, conn)
        
        if not gamelog_dupes.empty:
            logger.warning(f"⚠️ Found {len(gamelog_dupes)} player-date combinations with multiple game logs")
            logger.info("📋 Sample game log duplicates:")
            for _, row in gamelog_dupes.head(5).iterrows():
                logger.info(f"   • {row['player_name']} on {row['game_date']}: {row['log_count']} logs")
        else:
            logger.info("✅ No game log duplicates found")
        
        return {
            'duplicate_game_logs': len(gamelog_dupes),
            'sample_gamelog_dupes': gamelog_dupes.head(10).to_dict('records') if not gamelog_dupes.empty else []
        }
    
    def create_deduplicated_dataset(self) -> Dict[str, Any]:
        """Create a clean, deduplicated dataset for ML training"""
        logger.info("🧹 CREATING DEDUPLICATED DATASET")
        logger.info("=" * 40)
        
        conn = sqlite3.connect(self.db_path)
        
        # Create deduplicated table
        logger.info("📊 Creating deduplicated training dataset...")
        
        # Step 1: Remove exact duplicates, keep first occurrence
        dedup_query = """
            CREATE TEMPORARY TABLE deduplicated_wnba AS
            SELECT DISTINCT
                player_name,
                season,
                stat_category,
                stat_value,
                data_category,
                game_date,
                team_abbreviation,
                source_file,
                data_type,
                raw_data,
                created_at
            FROM unified_nba_wnba_data 
            WHERE league_name = 'WNBA'
            AND stat_value IS NOT NULL
            AND player_name IS NOT NULL
        """
        
        conn.execute(dedup_query)
        
        # Get deduplicated count
        dedup_count_query = "SELECT COUNT(*) FROM deduplicated_wnba"
        deduplicated_count = pd.read_sql_query(dedup_count_query, conn).iloc[0, 0]
        
        # Step 2: Further deduplication - remove semantic duplicates
        # Keep only the most recent/reliable source for each player-stat combination
        refined_query = """
            CREATE TEMPORARY TABLE refined_wnba AS
            SELECT *
            FROM deduplicated_wnba d1
            WHERE d1.created_at = (
                SELECT MAX(d2.created_at)
                FROM deduplicated_wnba d2
                WHERE d2.player_name = d1.player_name
                AND d2.stat_category = d1.stat_category
                AND d2.stat_value = d1.stat_value
                AND d2.season = d1.season
            )
        """
        
        conn.execute(refined_query)
        
        # Get refined count
        refined_count_query = "SELECT COUNT(*) FROM refined_wnba"
        refined_count = pd.read_sql_query(refined_count_query, conn).iloc[0, 0]
        
        # Step 3: Create final clean dataset with quality filters
        final_query = """
            SELECT 
                player_name,
                season,
                stat_category,
                CAST(stat_value AS FLOAT) as stat_value,
                data_category,
                game_date,
                team_abbreviation,
                source_file,
                data_type
            FROM refined_wnba
            WHERE CAST(stat_value AS FLOAT) >= 0
            AND CAST(stat_value AS FLOAT) < 1000  -- Remove outliers
            ORDER BY player_name, season, game_date
        """
        
        clean_dataset = pd.read_sql_query(final_query, conn)
        conn.close()
        
        # Calculate deduplication impact
        original_count = self.analyze_duplicates()['total_records']
        final_count = len(clean_dataset)
        removed_count = original_count - final_count
        removal_percentage = (removed_count / original_count) * 100
        
        logger.info(f"📊 DEDUPLICATION RESULTS:")
        logger.info(f"   Original records: {original_count:,}")
        logger.info(f"   After exact dedup: {deduplicated_count:,}")
        logger.info(f"   After semantic dedup: {refined_count:,}")
        logger.info(f"   Final clean dataset: {final_count:,}")
        logger.info(f"   Removed: {removed_count:,} ({removal_percentage:.1f}%)")
        
        # Analyze final dataset quality
        quality_stats = self._analyze_dataset_quality(clean_dataset)
        
        return {
            'original_count': original_count,
            'deduplicated_count': deduplicated_count,
            'refined_count': refined_count,
            'final_count': final_count,
            'removed_count': removed_count,
            'removal_percentage': removal_percentage,
            'clean_dataset': clean_dataset,
            'quality_stats': quality_stats
        }
    
    def _analyze_dataset_quality(self, dataset: pd.DataFrame) -> Dict[str, Any]:
        """Analyze the quality of the deduplicated dataset"""
        
        quality_stats = {
            'unique_players': dataset['player_name'].nunique(),
            'unique_seasons': dataset['season'].nunique(),
            'stat_categories': dataset['stat_category'].nunique(),
            'data_categories': dataset['data_category'].nunique(),
            'avg_records_per_player': len(dataset) / dataset['player_name'].nunique(),
            'stat_value_range': {
                'min': dataset['stat_value'].min(),
                'max': dataset['stat_value'].max(),
                'mean': dataset['stat_value'].mean(),
                'std': dataset['stat_value'].std()
            }
        }
        
        logger.info(f"📊 CLEAN DATASET QUALITY:")
        logger.info(f"   Unique players: {quality_stats['unique_players']:,}")
        logger.info(f"   Unique seasons: {quality_stats['unique_seasons']}")
        logger.info(f"   Stat categories: {quality_stats['stat_categories']}")
        logger.info(f"   Avg records/player: {quality_stats['avg_records_per_player']:.1f}")
        logger.info(f"   Stat value range: {quality_stats['stat_value_range']['min']:.1f} - {quality_stats['stat_value_range']['max']:.1f}")
        
        return quality_stats
    
    def save_clean_dataset(self, clean_dataset: pd.DataFrame, filename: str = "clean_wnba_training_data.csv") -> str:
        """Save the clean dataset for ML training"""
        filepath = f"data/{filename}"
        clean_dataset.to_csv(filepath, index=False)
        logger.info(f"💾 Clean dataset saved: {filepath}")
        logger.info(f"📊 Ready for ML training: {len(clean_dataset):,} clean records")
        return filepath

def main():
    """Run comprehensive duplicate analysis and deduplication"""
    
    analyzer = DataDeduplicationAnalysis()
    
    # Step 1: Analyze duplicates
    duplicate_analysis = analyzer.analyze_duplicates()
    
    
    # Step 2: Create clean dataset
    dedup_results = analyzer.create_deduplicated_dataset()
    
    
    # Step 3: Save clean dataset
    clean_file = analyzer.save_clean_dataset(dedup_results['clean_dataset'])
    
    
    # Recommendations
    if dedup_results['removal_percentage'] > 30:
        logger.warning("⚠️ High percentage of records removed. Review data sources for excessive duplication.")
    elif dedup_results['removal_percentage'] > 10:
        logger.info("ℹ️ Moderate deduplication. Data quality is reasonable, but check for improvement opportunities.")
    else:
        logger.info("✅ Low duplication. Data quality is high.")
    
    return dedup_results

if __name__ == "__main__":
    main()
