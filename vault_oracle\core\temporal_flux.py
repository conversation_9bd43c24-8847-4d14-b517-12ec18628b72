import logging
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta
import random

"""
Temporal Flux Module - Bridge to Unified Temporal Analysis

This module provides backward compatibility for the old vault_oracle.core.temporal_flux
imports by bridging to the new unified temporal analysis system.

The temporal flux stabilizer has been unified and moved to:
- vault_oracle.analysis.temporal_stabilizer

This module maintains compatibility while directing to the new unified system.
"""


logger = logging.getLogger(__name__)

# Import from the unified temporal analysis system
try:
    from vault_oracle.analysis.temporal_stabilizer import (
        ExpertTemporalStabilizer,
        TemporalStabilizationResult,
        TemporalMetrics,
        QuantumTemporalAnalyzer,
        BasketballTemporalIntelligence,
        create_expert_temporal_stabilizer
    )
    UNIFIED_TEMPORAL_AVAILABLE = True
    logger.info("🧠 MEDUSA VAULT: Successfully imported unified temporal stabilizer system")
except ImportError as e:
    UNIFIED_TEMPORAL_AVAILABLE = False
    logger.warning(f"🧠 MEDUSA VAULT: Unified temporal stabilizer system unavailable: {e}")


class TemporalFluxStabilizer:
    """
    Temporal Flux Stabilizer - Bridge to Unified Temporal Analysis
    
    This class provides backward compatibility for temporal flux stabilization
    by bridging to the new unified temporal analysis system.
    """
    
    def __init__(self, config: Optional[Any] = None):
        """Initialize the temporal flux stabilizer."""
        self.config = config
        self.logger = logger
        
        if UNIFIED_TEMPORAL_AVAILABLE:
            # Convert config to dict if it's a Pydantic model or other object
            if hasattr(config, 'model_dump'):
                config_dict = config.model_dump()
            elif hasattr(config, '__dict__'):
                config_dict = config.__dict__
            elif isinstance(config, dict):
                config_dict = config
            else:
                config_dict = {}
            
            self.stabilizer = ExpertTemporalStabilizer(config_dict)
            self.logger.info("🧠 MEDUSA VAULT: TemporalFluxStabilizer initialized with unified system")
        else:
            self.stabilizer = None
            self.logger.warning("🧠 MEDUSA VAULT: TemporalFluxStabilizer using fallback mode")
    
    def stabilize_flux(self, flux_value: float) -> bool:
        """
        Stabilize temporal flux.
        
        Args:
            flux_value: The flux value to stabilize
            
        Returns:
            True if stabilization was needed and performed, False otherwise
        """
        if self.stabilizer:
            # Use unified temporal stabilizer
            try:
                # Create simple data structure for flux analysis
                flux_data = {
                    'flux_value': flux_value,
                    'timestamp': datetime.now(),
                    'type': 'temporal_flux'
                }
                
                result = self.stabilizer.stabilize(flux_data)
                
                # Return True if stabilization was applied
                return result.stabilization_applied if hasattr(result, 'stabilization_applied') else False
                
            except Exception as e:
                self.logger.error(f"🧠 MEDUSA VAULT: Flux stabilization failed: {e}")
                return self._fallback_stabilize_flux(flux_value)
        else:
            return self._fallback_stabilize_flux(flux_value)
    
    def _fallback_stabilize_flux(self, flux_value: float) -> bool:
        """Fallback flux stabilization."""
        # Simple threshold-based stabilization
        threshold = 0.8
        if abs(flux_value) > threshold:
            self.logger.info(f"🧠 MEDUSA VAULT: Fallback flux stabilization applied for value {flux_value}")
            return True
        return False
    
    def analyze_temporal_patterns(self, data: Any) -> Dict[str, Any]:
        """
        Analyze temporal patterns in data.
        
        Args:
            data: Input data for temporal analysis
            
        Returns:
            Dictionary containing temporal pattern analysis
        """
        if self.stabilizer:
            try:
                return self.stabilizer.analyze_temporal_patterns(data)
            except Exception as e:
                self.logger.error(f"🧠 MEDUSA VAULT: Temporal pattern analysis failed: {e}")
                return self._fallback_pattern_analysis(data)
        else:
            return self._fallback_pattern_analysis(data)
    
    def _fallback_pattern_analysis(self, data: Any) -> Dict[str, Any]:
        """Fallback temporal pattern analysis."""
        
        return {
            "temporal_stability": random.uniform(0.4, 0.9),
            "flux_intensity": random.uniform(0.1, 0.6),
            "pattern_coherence": random.uniform(0.6, 0.95),
            "prediction_horizon": random.uniform(0.3, 0.8),
            "method": "fallback",
            "warning": "Using fallback analysis - unified temporal system unavailable"
        }
    
    def get_stabilization_metrics(self) -> Dict[str, Any]:
        """Get current stabilization metrics."""
        if self.stabilizer:
            try:
                # Get metrics from the unified stabilizer
                return {
                    "system_status": "unified_temporal_system",
                    "stabilizer_available": True,
                    "quantum_analysis": getattr(self.stabilizer, 'enable_quantum_analysis', False),
                    "basketball_awareness": getattr(self.stabilizer, 'basketball_awareness', False)
                }
            except Exception as e:
                self.logger.error(f"🧠 MEDUSA VAULT: Failed to get stabilization metrics: {e}")
        
        return {
            "system_status": "fallback_mode",
            "stabilizer_available": False,
            "quantum_analysis": False,
            "basketball_awareness": False
        }
    
    def reset_stabilization_state(self):
        """Reset the stabilization state."""
        if self.stabilizer:
            try:
                # Reset the unified stabilizer state
                self.stabilizer.temporal_anchors = {}
                self.stabilizer.stabilization_history = []
                self.stabilizer.quantum_calibration = {}
                self.logger.info("🧠 MEDUSA VAULT: Stabilization state reset")
            except Exception as e:
                self.logger.error(f"🧠 MEDUSA VAULT: Failed to reset stabilization state: {e}")
        else:
            self.logger.info("🧠 MEDUSA VAULT: Fallback stabilization state reset")


# Provide backward compatibility aliases
ExpertTemporalFluxStabilizer = TemporalFluxStabilizer  # Legacy alias

# Factory function for easy instantiation
def create_temporal_flux_stabilizer(config: Optional[Any] = None) -> TemporalFluxStabilizer:
    """Factory function to create temporal flux stabilizer."""
    return TemporalFluxStabilizer(config)

# Export main classes and functions
__all__ = [
    'TemporalFluxStabilizer',
    'ExpertTemporalFluxStabilizer',  # Legacy alias
    'create_temporal_flux_stabilizer'
]
