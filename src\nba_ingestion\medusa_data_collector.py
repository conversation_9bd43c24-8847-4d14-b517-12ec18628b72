import asyncio
import logging
import sys
from pathlib import Path
from datetime import datetime
from vault_oracle.wells.nba_api_connector import BasketballDataConnector
from src.nba_ingestion.nba_api_parameter_manager import NBAParameterManager, BasketballLeague

#!/usr/bin/env python3
"""
MEDUSA VAULT: Streamlined NBA/WNBA Data Collection
====================================================

Streamlined data collection script that uses our fixed components
to collect NBA and WNBA data with Oracle Memory integration.
"""


# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Import our fixed components
from vault_oracle.core.OracleMemory import (
    ExpertOracleMemory,
    MemoryPriority,
    GamePhase,
    MemoryDimension
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format=" %(asctime)s │ %(levelname)s │ %(message)s"
)
logger = logging.getLogger(__name__)

class MedusaDataCollector:
    """Streamlined MEDUSA data collector with Oracle Memory integration"""

    def __init__(self):
        """Initialize the collector with Oracle Memory"""

        # Initialize Oracle Memory
        oracle_config = {
            'memory_path': 'medusa_unified.db',
            'encryption_key': None,
            'max_memory_size': 1000000,
            'basketball_analytics': True,
            'quantum_features': True
        }
        self.oracle_memory = ExpertOracleMemory(oracle_config)
        # Initialize parameter manager
        self.param_manager = NBAParameterManager(self.oracle_memory)

        # Initialize data connector (no oracle_memory needed in constructor)
        self.connector = BasketballDataConnector("NBA")

        logger.info(" MEDUSA Data Collector initialized successfully!")

    async def collect_data_for_league(self, league: BasketballLeague, max_endpoints: int = 50):
        """Collect data for a specific league"""

        logger.info(f" Starting data collection for {league.name}")

        try:
            # Create parameter matrix
            parameter_matrix = await self.param_manager.create_comprehensive_parameter_matrix(
                league, include_historical=True
            )

            total_collected = 0
            total_endpoints = 0

            # Process each category
            for category, params_list in parameter_matrix.items():
                if total_endpoints >= max_endpoints:
                    logger.info(f" Reached maximum endpoints limit ({max_endpoints})")
                    break

                logger.info(f" Processing {category} with {len(params_list)} parameter sets")

                # Process a subset of parameters for each category
                for i, params in enumerate(params_list[:5]): # Limit to 5 per category
                    if total_endpoints >= max_endpoints:
                        break

                    try:
                        # Call the appropriate data collection method based on category
                        records = await self._collect_data_by_category(category, params)
                        total_collected += records
                        total_endpoints += 1

                        # Log success to Oracle Memory
                        self.oracle_memory.log_expert_event(
                            event_type=f"DATA_COLLECTION_SUCCESS_{category.upper()}",
                            content=f"Successfully collected {records} records for {category}",
                            tags=["data_collection", "success", category.lower()],
                            priority=MemoryPriority.HIGH,
                            game_phase=GamePhase.DATA_COLLECTION,
                            dimensions={
                                MemoryDimension.BASKETBALL_INTELLIGENCE,
                                MemoryDimension.QUANTUM_COHERENCE
                            },
                            basketball_context={
                                'league': league.name,
                                'category': category,
                                'records_collected': records,
                                'parameters': params.to_dict()
                            }
                        )

                        logger.info(f" {category} #{i+1}: Collected {records} records")

                        # Small delay to respect API limits
                        await asyncio.sleep(1)

                    except Exception as e:
                        logger.error(f" {category} #{i+1} failed: {e}")

                        # Log error to Oracle Memory
                        self.oracle_memory.log_expert_event(
                            event_type=f"DATA_COLLECTION_ERROR_{category.upper()}",
                            content=f"Failed to collect data for {category}: {str(e)}",
                            tags=["data_collection", "error", category.lower()],
                            priority=MemoryPriority.HIGH,
                            game_phase=GamePhase.DATA_COLLECTION,
                            basketball_context={
                                'league': league.name,
                                'category': category,
                                'error': str(e),
                                'parameters': params.to_dict()
                            }
                        )
                        continue

            logger.info(f" {league.name} Collection Complete:")
            logger.info(f" Total Endpoints Processed: {total_endpoints}")
            logger.info(f" Total Records Collected: {total_collected}")

            return total_collected

        except Exception as e:
            logger.error(f" League collection failed for {league.name}: {e}")
            return 0

    async def _collect_data_by_category(self, category: str, params) -> int:
        """Collect data based on category type"""

        try:
            if "league_dashboard" in category:
                # Use league dashboard endpoints
                data = await self.connector.get_league_dashboard_player_stats(
                    league_id=params.league_id,
                    season=params.season,
                    season_type=params.season_type
                )
                return len(data[0]) if data and data[0] is not None else 0

            elif "team_dashboard" in category:
                # Use team dashboard endpoints
                if hasattr(params, 'team_id') and params.team_id:
                    data = await self.connector.get_team_dashboard_by_general_splits(
                        team_id=params.team_id,
                        season=params.season,
                        season_type=params.season_type,
                        measure_type=params.measure_type
                    )
                    return len(data[0]) if data and data[0] is not None else 0

            elif "player_dashboard" in category:
                # Use player dashboard endpoints
                if hasattr(params, 'player_id') and params.player_id:
                    data = await self.connector.get_player_career_stats(params.player_id)
                    return len(data[0]) if data and data[0] is not None else 0

            elif "game_data" in category:
                # Use game data endpoints
                data = await self.connector.get_league_game_finder(
                    params.season, params.season_type
                )
                return len(data[0]) if data and data[0] is not None else 0

            elif "clutch" in category:
                # Use clutch data endpoints
                data = await self.connector.get_clutch_stats("Player", params.season)
                return len(data[0]) if data and data[0] is not None else 0

            else:
                # Default: try league game finder
                data = await self.connector.get_league_game_finder(
                    params.season, params.season_type
                )
                return len(data[0]) if data and data[0] is not None else 0

        except Exception as e:
            logger.warning(f"Data collection failed for {category}: {e}")
            return 0

    async def run_comprehensive_collection(self):
        """Run comprehensive data collection for both NBA and WNBA"""

        logger.info(" STARTING COMPREHENSIVE MEDUSA DATA COLLECTION")
        logger.info("=" * 60)

        start_time = datetime.now()

        # Collect NBA data
        nba_records = await self.collect_data_for_league(BasketballLeague.NBA, max_endpoints=25)

        # Collect WNBA data
        wnba_records = await self.collect_data_for_league(BasketballLeague.WNBA, max_endpoints=25)

        # Final summary
        end_time = datetime.now()
        duration = end_time - start_time

        # Log final summary to Oracle Memory
        self.oracle_memory.log_expert_event(
            event_type="COMPREHENSIVE_COLLECTION_COMPLETE",
            content=f"Comprehensive data collection completed: {nba_records + wnba_records} total records",
            tags=["data_collection", "complete", "comprehensive"],
            priority=MemoryPriority.CRITICAL,
            game_phase=GamePhase.DATA_COLLECTION,
            dimensions={
                MemoryDimension.BASKETBALL_INTELLIGENCE,
                MemoryDimension.QUANTUM_COHERENCE,
                MemoryDimension.TEMPORAL
            },
            basketball_context={
                'nba_records': nba_records,
                'wnba_records': wnba_records,
                'total_records': nba_records + wnba_records,
                'duration_seconds': duration.total_seconds(),
                'collection_complete': True
            }
        )

        logger.info("=" * 60)
        logger.info(" COMPREHENSIVE COLLECTION SUMMARY")
        logger.info(f" NBA Records Collected: {nba_records:,}")
        logger.info(f" WNBA Records Collected: {wnba_records:,}")
        logger.info(f" Total Records: {nba_records + wnba_records:,}")
        logger.info(f"⏱️ Duration: {duration}")
        logger.info(f" All data stored in Oracle Memory: medusa_unified.db")
        logger.info(" Basketball intelligence analytics enabled")
        logger.info(" Quantum-optimized storage active")
        logger.info("=" * 60)

async def main():
    """Main data collection execution"""

    collector = MedusaDataCollector()
    await collector.run_comprehensive_collection()

if __name__ == "__main__":
    asyncio.run(main())
