import hashlib # Moved here to avoid re-import in method
import json
import logging
import traceback
import uuid
import socket
import os
import time
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional, Callable
from enum import Enum
import sys
import asyncio
from backend.infrastructure.database import get_expert_database_manager

#!/usr/bin/env python3

"""
Expert-Level Cosmic Exceptions System

====================================

Comprehensive exception hierarchy for the Oracle prediction system with
quantum-inspired error handling, basketball-specific failures, and
advanced debugging capabilities.

Features:
- Hierarchical exception structure for different system levels
- Basketball-specific prediction errors
- Quantum-inspired error handling with uncertainty principles
- Professional debugging and context preservation
- Advanced error recovery mechanisms
- Real-time failure analytics and reporting

"""


# Configure logger for cosmic exceptions
logger = logging.getLogger(__name__) # Corrected __name__ usage

class ErrorSeverity(Enum):
    """Severity levels for cosmic errors"""
    TRIVIAL = "TRIVIAL" # Minor issues, system continues
    LOW = "LOW" # Low impact, degraded performance
    MEDIUM = "MEDIUM" # Medium impact, some features affected
    HIGH = "HIGH" # High impact, major functionality down
    CRITICAL = "CRITICAL" # Critical impact, system instability
    CATASTROPHIC = "CATASTROPHIC" # Complete system failure

class ErrorCategory(Enum):
    """Categories of cosmic errors"""
    PREDICTION = "PREDICTION" # Prediction-related errors
    DATA = "DATA" # Data access/processing errors
    NETWORK = "NETWORK" # Network/connectivity errors
    AUTHENTICATION = "AUTHENTICATION" # Security/auth errors
    CONFIGURATION = "CONFIGURATION" # Config/setup errors
    QUANTUM = "QUANTUM" # Quantum computation errors
    BASKETBALL = "BASKETBALL" # Basketball-specific errors
    CLUSTER = "CLUSTER" # Cluster management errors
    TEMPORAL = "TEMPORAL" # Time/scheduling errors
    UNKNOWN = "UNKNOWN" # Unclassified errors

class BaseCosmicException(Exception):
    """
    Elite Base Exception with advanced diagnostics, recovery hooks, and tracing capabilities
    """
    # Class-level error rate limiting
    _error_count = 0
    _last_error_time = time.time()
    _error_rate_limit = 100  # Max errors per minute
    _suppress_until = 0
    
    def __init__(
        self,
        message: str,
        error_code: str = "COSMIC_000",
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        category: ErrorCategory = ErrorCategory.UNKNOWN,
        context: Optional[Dict[str, Any]] = None,
        quantum_uncertainty: float = 0.5,
        recovery_suggestions: Optional[List[str]] = None,
        timestamp: Optional[datetime] = None,
        correlation_id: Optional[str] = None,
        cause: Optional[Exception] = None,
        recovery_hook: Optional[Callable[['BaseCosmicException'], bool]] = None,
        system_load: Optional[float] = None,
        response_time: Optional[float] = None,
        suppress_logging: bool = False
    ):
        super().__init__(message)
        # Core properties
        self.message = message
        self.error_code = error_code
        self.severity = severity
        self.category = category
        self.context = context or {}
        self.quantum_uncertainty = max(0.0, min(1.0, quantum_uncertainty))
        self.recovery_suggestions = recovery_suggestions or []
        self.timestamp = timestamp or datetime.now(timezone.utc)
        self.correlation_id = correlation_id or str(uuid.uuid4())
        self.__cause__ = cause
        self.recovery_hook = recovery_hook
        self.suppress_logging = suppress_logging
        
        # Performance metrics
        self.system_load = system_load
        self.response_time = response_time
        
        # Enrich context with system diagnostics
        self._enrich_context()
        
        # Generate unique identifiers
        self.error_id = self._generate_error_id()
        self.stack_trace = self._capture_stack_trace()
        
        # Apply rate limiting
        self._apply_rate_limiting()
        
        # Log the exception (unless suppressed)
        if not self.suppress_logging:
            self._log_exception()

    def _enrich_context(self) -> None:
        """Add system diagnostics to context"""
        self.context.setdefault('hostname', socket.gethostname())
        self.context.setdefault('pid', os.getpid())
        self.context.setdefault('python_version', os.sys.version)
        
        if self.__cause__:
            self.context['root_cause'] = str(self.__cause__)
            if hasattr(self.__cause__, 'error_id'):
                self.context['root_cause_id'] = getattr(self.__cause__, 'error_id')
        
        if self.system_load is not None:
            self.context['system_load'] = self.system_load
            
        if self.response_time is not None:
            self.context['response_time_ms'] = self.response_time

    def _generate_error_id(self) -> str:
        """Generate unique error ID with correlation support"""
        base_string = f"{self.error_code}_{self.timestamp.timestamp()}_{self.correlation_id}"
        return f"ERR-{hashlib.sha256(base_string.encode()).hexdigest()[:10].upper()}"
    def _capture_stack_trace(self) -> str:
        """Capture full stack trace with cause chaining"""
        
        if self.__cause__:
            # Format chained exceptions
            return ''.join(traceback.format_exception(
                type(self.__cause__),
                self.__cause__,
                self.__cause__.__traceback__
            ))
        else:
            # Capture current stack
            return ''.join(traceback.format_stack())

    def _apply_rate_limiting(self) -> None:
        """Implement error storm protection"""
        current_time = time.time()
        elapsed = current_time - self._last_error_time
        
        # Reset counter if more than 1 minute has passed
        if elapsed > 60:
            BaseCosmicException._error_count = 0
            BaseCosmicException._last_error_time = current_time
        
        # Increment error count
        BaseCosmicException._error_count += 1
        
        # Check if we're in suppression mode
        if current_time < BaseCosmicException._suppress_until:
            self.suppress_logging = True
            return
            
        # Activate suppression if rate limit exceeded
        if BaseCosmicException._error_count > self._error_rate_limit:
            suppression_minutes = 5
            BaseCosmicException._suppress_until = current_time + (suppression_minutes * 60)
            self.suppress_logging = True
            logger.critical(
                f"🚨 ERROR STORM DETECTED! Suppressing errors for {suppression_minutes} minutes. "
                f"Current rate: {BaseCosmicException._error_count}/min"
            )

    def _log_exception(self) -> None:
        """Log the exception with advanced formatting"""
        log_data = {
            'error_id': self.error_id,
            'correlation_id': self.correlation_id,
            'error_code': self.error_code,
            'severity': self.severity.value,
            'category': self.category.value,
            'message': self.message,
            'quantum_uncertainty': self.quantum_uncertainty,
            'context': self.context,
            'timestamp': self.timestamp.isoformat()
        }
        log_message = f"🚨 COSMIC EXCEPTION: {json.dumps(log_data, default=str)}"
        
        if self.severity in [ErrorSeverity.CRITICAL, ErrorSeverity.CATASTROPHIC]:
            logger.critical(log_message)
        elif self.severity == ErrorSeverity.HIGH:
            logger.error(log_message)
        elif self.severity == ErrorSeverity.MEDIUM:
            logger.warning(log_message)
        else:
            logger.info(log_message)

    def attempt_recovery(self) -> bool:
        """
        Execute attached recovery hook with automatic fallback
        
        Returns:
            True if recovery was attempted and successful
        """
        if self.recovery_hook:
            try:
                logger.info(f"⚡ Attempting recovery for {self.error_id}")
                return self.recovery_hook(self)
            except Exception as recovery_error:
                logger.error(f"Recovery hook failed: {str(recovery_error)}")
                return False
                
        # Attempt standard recovery if no hook provided
        if self.recovery_suggestions:
            logger.warning(f"No recovery hook for {self.error_id}, using suggestions")
            for suggestion in self.recovery_suggestions:
                logger.info(f"⚙️  Recovery suggestion: {suggestion}")
            return self.severity.value in ['LOW', 'MEDIUM', 'TRIVIAL']
            
        return False

    def to_dict(self) -> Dict[str, Any]:
        """Enhanced serialization with new properties"""
        return {
            'error_id': self.error_id,
            'correlation_id': self.correlation_id,
            'error_code': self.error_code,
            'severity': self.severity.value,
            'category': self.category.value,
            'message': self.message,
            'quantum_uncertainty': self.quantum_uncertainty,
            'context': self.context,
            'recovery_suggestions': self.recovery_suggestions,
            'timestamp': self.timestamp.isoformat(),
            'stack_trace': self.stack_trace,
            'system_load': self.system_load,
            'response_time': self.response_time,
            'suppressed': self.suppress_logging
        }

    def __str__(self) -> str:
        """Enhanced string representation with correlation ID"""
        return (
            f"[{self.error_id}] {self.correlation_id} | "
            f"{self.severity.value}: {self.message} "
            f"(Uncertainty: {self.quantum_uncertainty:.2f})"
        )

# =============================================================================
# CATASTROPHIC LEVEL EXCEPTIONS
# =============================================================================

class CosmicCollapse(BaseCosmicException):
    """
    Exception raised for catastrophic failures that threaten the entire
    Oracle system integrity.
    """
    def __init__(self, message: str, error_code: str = "COSMIC_001", **kwargs):
        super().__init__(
            message=message,
            error_code=error_code,
            severity=ErrorSeverity.CATASTROPHIC,
            category=ErrorCategory.UNKNOWN,
            **kwargs
        )

class QuantumDecoherence(BaseCosmicException):
    """
    Exception raised when quantum systems lose coherence and enter
    undefined states.
    """
    def __init__(self, message: str, coherence_level: float = 0.0, **kwargs):
        context = kwargs.pop('context', {})
        context['coherence_level'] = coherence_level
        super().__init__(
            message=message,
            error_code="QUANTUM_001",
            severity=ErrorSeverity.CRITICAL,
            category=ErrorCategory.QUANTUM,
            context=context,
            quantum_uncertainty=1.0 - coherence_level,
            **kwargs
        )

class OracleMemoryCorruption(BaseCosmicException):
    """
    Exception raised when Oracle memory databases become corrupted
    or inaccessible.
    """
    def __init__(self, message: str, corruption_extent: float = 0.5, **kwargs):
        super().__init__(
            message=message,
            error_code="ORACLE_001",
            severity=ErrorSeverity.CATASTROPHIC,
            category=ErrorCategory.DATA,
            quantum_uncertainty=corruption_extent,
            recovery_suggestions=[
                "Restore from latest backup",
                "Run memory integrity check",
                "Reinitialize Oracle database"
            ],
            **kwargs
        )

# =============================================================================
# PREDICTION SYSTEM EXCEPTIONS
# =============================================================================

class PredictionSystemFailure(BaseCosmicException):
    """Base exception for prediction system failures"""
    def __init__(self, message: str, error_code: str = "PRED_000", **kwargs):
        # Allow child classes to override these parameters
        severity = kwargs.pop('severity', ErrorSeverity.HIGH)
        category = kwargs.pop('category', ErrorCategory.PREDICTION)
        super().__init__(
            message=message,
            error_code=error_code,
            severity=severity,
            category=category,
            **kwargs
        )

class ModelNotFound(PredictionSystemFailure):
    """Exception raised when required ML models cannot be located"""
    def __init__(self, model_name: str, search_paths: Optional[List[str]] = None, **kwargs):
        message = f"Model '{model_name}' not found"
        context = kwargs.pop('context', {})
        context.update({
            'model_name': model_name,
            'search_paths': search_paths or []
        })
        super().__init__(
            message=message,
            error_code="PRED_001",
            context=context,
            recovery_suggestions=[
                f"Download {model_name} model",
                "Check model file permissions",
                "Verify model path configuration"
            ],
            **kwargs
        )

class InsufficientTrainingData(PredictionSystemFailure):
    """Exception raised when insufficient data is available for predictions"""
    def __init__(self, required_samples: int, available_samples: int, **kwargs):
        # Prevent division by zero if required_samples is 0
        if required_samples == 0:
            quantum_uncertainty = 1.0
            message = "Insufficient training data: required samples cannot be zero"
        else:
            quantum_uncertainty = max(0.0, min(1.0, 1.0 - (available_samples / required_samples)))
            message = f"Insufficient training data: need {required_samples}, have {available_samples}"

        context = kwargs.pop('context', {})
        context.update({
            'required_samples': required_samples,
            'available_samples': available_samples,
            'data_deficit': required_samples - available_samples
        })
        super().__init__(
            message=message,
            error_code="PRED_002",
            context=context,
            quantum_uncertainty=quantum_uncertainty,
            **kwargs
        )

class PredictionConfidenceTooLow(PredictionSystemFailure):
    """Exception raised when prediction confidence is below acceptable threshold"""
    def __init__(self, confidence: float, threshold: float, prediction_type: str = "unknown", **kwargs):
        message = f"{prediction_type} prediction confidence {confidence:.3f} below threshold {threshold:.3f}"
        context = kwargs.pop('context', {})
        context.update({
            'confidence': confidence,
            'threshold': threshold,
            'prediction_type': prediction_type,
            'confidence_deficit': threshold - confidence
        })
        super().__init__(
            message=message,
            error_code="PRED_003",
            severity=ErrorSeverity.MEDIUM,
            context=context,
            quantum_uncertainty=max(0.0, min(1.0, 1.0 - confidence)), # Clamp 0-1
            **kwargs
        )

# =============================================================================
# BASKETBALL-SPECIFIC EXCEPTIONS
# =============================================================================

class BasketballPredictionError(BaseCosmicException):
    """Base exception for basketball-specific prediction errors"""
    def __init__(self, message: str, error_code: str = "BBL_000", **kwargs):
        # Allow child classes to override these parameters
        severity = kwargs.pop('severity', ErrorSeverity.MEDIUM)
        category = kwargs.pop('category', ErrorCategory.BASKETBALL)
        super().__init__(
            message=message,
            error_code=error_code,
            severity=severity,
            category=category,
            **kwargs
        )

class GameNotFound(BasketballPredictionError):
    """Exception raised when a requested game cannot be found"""
    def __init__(self, titan_clash_id: str, league: str = "NBA", **kwargs):
        message = f"Game {titan_clash_id} not found in {league}"
        context = kwargs.pop('context', {})
        context.update({
            'titan_clash_id': titan_clash_id,
            'league': league
        })
        super().__init__(
            message=message,
            error_code="BBL_001",
            context=context,
            **kwargs
        )

class PlayerDataIncomplete(BasketballPredictionError):
    """Exception raised when player data is incomplete for predictions"""
    def __init__(self, player_name: str, missing_fields: List[str], total_expected_fields: int = 10, **kwargs):
        message = f"Incomplete data for {player_name}: missing {', '.join(missing_fields)}"
        context = kwargs.pop('context', {})
        completeness = 1.0 - (len(missing_fields) / total_expected_fields) if total_expected_fields > 0 else 0.0
        context.update({
            'player_name': player_name,
            'missing_fields': missing_fields,
            'completeness': completeness # Using the calculated completeness
        })
        # Clamp quantum uncertainty
        quantum_uncertainty = max(0.0, min(1.0, len(missing_fields) / total_expected_fields if total_expected_fields > 0 else 1.0))
        super().__init__(
            message=message,
            error_code="BBL_002",
            context=context,
            quantum_uncertainty=quantum_uncertainty,
            **kwargs
        )

class SeasonDataOutdated(BasketballPredictionError):
    """Exception raised when season data is too old for reliable predictions"""
    def __init__(self, last_update: datetime, max_age_days: int = 7, **kwargs):
        age_days = (datetime.now(timezone.utc) - last_update).days # Use timezone-aware datetime
        message = f"Season data outdated: {age_days} days old (max: {max_age_days})"
        context = kwargs.pop('context', {})
        context.update({
            'last_update': last_update.isoformat(),
            'age_days': age_days,
            'max_age_days': max_age_days
        })
        # Clamp quantum uncertainty
        quantum_uncertainty = max(0.0, min(1.0, age_days / max_age_days if max_age_days > 0 else 1.0))
        super().__init__(
            message=message,
            error_code="BBL_003",
            context=context,
            quantum_uncertainty=quantum_uncertainty,
            recovery_suggestions=[
                "Update season data",
                "Check data refresh schedule",
                "Verify data source connectivity"
            ],
            **kwargs
        )

class InjuryReportUnavailable(BasketballPredictionError):
    """Exception raised when injury reports are unavailable"""
    def __init__(self, team_name: str, game_date: datetime, **kwargs):
        message = f"Injury report unavailable for {team_name} on {game_date.strftime('%Y-%m-%d')}"
        context = kwargs.pop('context', {})
        context.update({
            'team_name': team_name,
            'game_date': game_date.isoformat()
        })
        super().__init__(
            message=message,
            error_code="BBL_004",
            severity=ErrorSeverity.HIGH, # Injury reports are critical for predictions
            context=context,
            quantum_uncertainty=0.8, # High uncertainty without injury data
            **kwargs
        )

# =============================================================================
# DATA ACCESS EXCEPTIONS
# =============================================================================

class DataAccessError(BaseCosmicException):
    """Base exception for data access and processing errors"""
    def __init__(self, message: str, error_code: str = "DATA_000", **kwargs):
        # Allow child classes to override these parameters
        severity = kwargs.pop('severity', ErrorSeverity.MEDIUM)
        category = kwargs.pop('category', ErrorCategory.DATA)
        super().__init__(
            message=message,
            error_code=error_code,
            severity=severity,
            category=category,
            **kwargs
        )

class DatabaseConnectionFailure(DataAccessError):
    """Elite implementation with recovery hooks"""
    
    @staticmethod
    def _recovery_hook(exception: 'DatabaseConnectionFailure') -> bool:
        """Automated recovery procedure for database failures"""
        try:
            db_name = exception.context.get('database_name', 'unknown')
            logger.info(f"🔧 Executing database recovery for {db_name}")
            db_manager = get_expert_database_manager()
            # Call the async recovery method synchronously
            result = asyncio.run(db_manager._reconnect_database())
            logger.info("✅ Database recovery successful")
            return True
        except Exception as e:
            logger.error(f"Database recovery failed: {str(e)}")
            return False

    def __init__(self, database_name: str, connection_string: str = "redacted", **kwargs):
        # Create recovery context
        recovery_context = kwargs.pop('context', {})
        recovery_context.update({
            'database_name': database_name,
            'connection_string': connection_string
        })
        
        # Call parent with recovery hook
        super().__init__(
            message=f" TITAN PROCESSING FAILED: connect to database: {database_name}",
            error_code="DATA_001",
            severity=ErrorSeverity.HIGH,
            context=recovery_context,
            recovery_suggestions=[
                "Check database server status",
                "Verify connection credentials",
                "Test network connectivity"
            ],
            recovery_hook=self._recovery_hook,
            **kwargs
        )

class DataValidationFailure(DataAccessError):
    """Exception raised when data validation fails"""
    def __init__(self, validation_errors: List[str], data_source: str = "unknown", **kwargs):
        message = f"Data validation failed for {data_source}: {len(validation_errors)} errors"
        context = kwargs.pop('context', {})
        context.update({
            'validation_errors': validation_errors,
            'data_source': data_source,
            'error_count': len(validation_errors)
        })
        # Clamp quantum uncertainty
        quantum_uncertainty = max(0.0, min(1.0, len(validation_errors) / 10 if len(validation_errors) > 0 else 0.0)) # Example divisor
        super().__init__(
            message=message,
            error_code="DATA_002",
            context=context,
            quantum_uncertainty=quantum_uncertainty,
            **kwargs
        )

# =============================================================================
# NETWORK AND API EXCEPTIONS
# =============================================================================

class NetworkError(BaseCosmicException):
    """Base exception for network-related errors"""
    def __init__(self, message: str, error_code: str = "NET_000", **kwargs):
        # Allow child classes to override these parameters
        severity = kwargs.pop('severity', ErrorSeverity.MEDIUM)
        category = kwargs.pop('category', ErrorCategory.NETWORK)
        super().__init__(
            message=message,
            error_code=error_code,
            severity=severity,
            category=category,
            **kwargs
        )

class APIRateLimitExceeded(NetworkError):
    """Exception raised when API rate limits are exceeded"""
    def __init__(self, api_name: str, rate_limit: int, reset_time: Optional[datetime] = None, **kwargs):
        message = f" AEGIS PROTECTION: Rate limit exceeded for {api_name} API (limit: {rate_limit}/hour)"
        context = kwargs.pop('context', {})
        context.update({
            'api_name': api_name,
            'rate_limit': rate_limit,
            'reset_time': reset_time.isoformat() if reset_time else None
        })
        super().__init__(
            message=message,
            error_code="NET_001",
            severity=ErrorSeverity.LOW,
            context=context,
            recovery_suggestions=[
                "Wait for rate limit reset",
                "Use alternative API endpoint",
                "Implement request queuing"
            ],
            **kwargs
        )

class ExternalServiceUnavailable(NetworkError):
    """Exception raised when external services are unavailable"""
    def __init__(self, service_name: str, endpoint: str, status_code: Optional[int] = None, **kwargs):
        message = f"External service unavailable: {service_name}"
        context = kwargs.pop('context', {})
        context.update({
            'service_name': service_name,
            'endpoint': endpoint,
            'status_code': status_code
        })
        super().__init__(
            message=message,
            error_code="NET_002",
            severity=ErrorSeverity.HIGH,
            context=context,
            **kwargs
        )

# =============================================================================
# CLUSTER MANAGEMENT EXCEPTIONS
# =============================================================================

class ClusterError(BaseCosmicException):
    """Base exception for cluster management errors"""
    def __init__(self, message: str, error_code: str = "CLUSTER_000", **kwargs):
        # Allow child classes to override these parameters
        severity = kwargs.pop('severity', ErrorSeverity.MEDIUM)
        category = kwargs.pop('category', ErrorCategory.CLUSTER)
        super().__init__(
            message=message,
            error_code=error_code,
            severity=severity,
            category=category,
            **kwargs
        )

class RealmUnreachable(ClusterError):
    """Exception raised when cluster realms become unreachable"""
    def __init__(self, realm_id: str, endpoint: str, last_successful_ping: Optional[datetime] = None, **kwargs):
        message = f"Realm '{realm_id}' unreachable at {endpoint}"
        context = kwargs.pop('context', {})
        context.update({
            'realm_id': realm_id,
            'endpoint': endpoint,
            'last_successful_ping': last_successful_ping.isoformat() if last_successful_ping else None
        })
        super().__init__(
            message=message,
            error_code="CLUSTER_001",
            severity=ErrorSeverity.HIGH,
            context=context,
            recovery_suggestions=[
                "Check realm health status",
                "Verify network connectivity",
                "Failover to backup realm"
            ],
            **kwargs
        )

class LoadBalancerFailure(ClusterError):
    """Exception raised when load balancing fails"""
    def __init__(self, strategy: str, available_realms: int, total_realms: int = 10, **kwargs):
        message = f"Load balancer failure using {strategy} strategy ({available_realms} realms available)"
        context = kwargs.pop('context', {})
        context.update({
            'strategy': strategy,
            'available_realms': available_realms
        })
        # Clamp quantum uncertainty
        quantum_uncertainty = max(0.0, min(1.0, 1.0 - (available_realms / total_realms) if total_realms > 0 else 1.0))
        super().__init__(
            message=message,
            error_code="CLUSTER_002",
            severity=ErrorSeverity.HIGH,
            context=context,
            quantum_uncertainty=quantum_uncertainty,
            **kwargs
        )

# =============================================================================
# AUTHENTICATION AND SECURITY EXCEPTIONS
# =============================================================================

class SecurityError(BaseCosmicException):
    """Base exception for security and authentication errors"""
    def __init__(self, message: str, error_code: str = "SEC_000", **kwargs):
        # Allow child classes to override these parameters
        severity = kwargs.pop('severity', ErrorSeverity.HIGH)
        category = kwargs.pop('category', ErrorCategory.AUTHENTICATION)
        super().__init__(
            message=message,
            error_code=error_code,
            severity=severity,
            category=category,
            **kwargs
        )

class InvalidApiKey(SecurityError):
    """Exception raised for invalid API keys"""
    def __init__(self, api_name: str, key_prefix: str = "***", **kwargs):
        message = f"Invalid API key for {api_name} (key: {key_prefix}...)"
        context = kwargs.pop('context', {})
        context.update({
            'api_name': api_name,
            'key_prefix': key_prefix
        })
        super().__init__(
            message=message,
            error_code="SEC_001",
            context=context,
            recovery_suggestions=[
                "Verify API key is correct",
                "Check API key expiration",
                "Regenerate API key if needed"
            ],
            **kwargs
        )

class EncryptionFailure(SecurityError):
    """Exception raised when encryption operations fail"""
    def __init__(self, operation: str, algorithm: str = "unknown", **kwargs):
        message = f"Encryption failure during {operation} using {algorithm}"
        context = kwargs.pop('context', {})
        context.update({
            'operation': operation,
            'algorithm': algorithm
        })
        super().__init__(
            message=message,
            error_code="SEC_002",
            severity=ErrorSeverity.CRITICAL,
            context=context,
            **kwargs
        )

# =============================================================================
# TEMPORAL AND CHRONOS EXCEPTIONS
# =============================================================================

class TemporalError(BaseCosmicException):
    """Base exception for temporal and scheduling errors"""
    def __init__(self, message: str, error_code: str = "TEMP_000", **kwargs):
        # Allow child classes to override these parameters
        severity = kwargs.pop('severity', ErrorSeverity.MEDIUM)
        category = kwargs.pop('category', ErrorCategory.TEMPORAL)
        super().__init__(
            message=message,
            error_code=error_code,
            severity=severity,
            category=category,
            **kwargs
        )

class ChronosTimeoutExceeded(TemporalError):
    """Exception raised when Chronos patience thresholds are exceeded"""
    def __init__(self, operation: str, timeout: float, actual_time: float, **kwargs):
        message = f"Chronos timeout exceeded for {operation}: {actual_time:.2f}s > {timeout:.2f}s"
        context = kwargs.pop('context', {})
        context.update({
            'operation': operation,
            'timeout_seconds': timeout,
            'actual_time_seconds': actual_time,
            'time_excess': actual_time - timeout
        })
        # Clamp quantum uncertainty
        quantum_uncertainty = max(0.0, min(1.0, (actual_time - timeout) / timeout if timeout > 0 else 1.0))
        super().__init__(
            message=message,
            error_code="TEMP_001",
            severity=ErrorSeverity.MEDIUM,
            context=context,
            quantum_uncertainty=quantum_uncertainty,
            **kwargs
        )

class SchedulingConflict(TemporalError):
    """Exception raised when scheduling conflicts occur"""
    def __init__(self, task_name: str, scheduled_time: datetime, conflict_task: Optional[str] = None, **kwargs):
        message = f"Scheduling conflict for {task_name} at {scheduled_time.isoformat()}" # Ensure datetime is formatted
        context = kwargs.pop('context', {})
        context.update({
            'task_name': task_name,
            'scheduled_time': scheduled_time.isoformat(),
            'conflict_task': conflict_task
        })
        super().__init__(
            message=message,
            error_code="TEMP_002",
            context=context,
            **kwargs
        )

# =============================================================================
# CONFIGURATION EXCEPTIONS
# =============================================================================

class ConfigurationError(BaseCosmicException):
    """Base exception for configuration errors"""
    def __init__(self, message: str, error_code: str = "CONFIG_000", **kwargs):
        # Allow child classes to override these parameters
        severity = kwargs.pop('severity', ErrorSeverity.HIGH)
        category = kwargs.pop('category', ErrorCategory.CONFIGURATION)
        super().__init__(
            message=message,
            error_code=error_code,
            severity=severity,
            category=category,
            **kwargs
        )

class MissingConfiguration(ConfigurationError):
    """Exception raised when required configuration is missing"""
    def __init__(self, config_key: str, config_file: str = "unknown", **kwargs):
        message = f"Missing required configuration: {config_key} in {config_file}"
        context = kwargs.pop('context', {})
        context.update({
            'config_key': config_key,
            'config_file': config_file
        })
        super().__init__(
            message=message,
            error_code="CONFIG_001",
            context=context,
            recovery_suggestions=[
                f"Add {config_key} to configuration",
                "Check configuration file syntax",
                "Verify configuration file permissions"
            ],
            **kwargs
        )

class InvalidConfiguration(ConfigurationError):
    """Exception raised when configuration values are invalid"""
    def __init__(self, config_key: str, invalid_value: Any, expected_type: Optional[str] = None, **kwargs):
        message = f"Invalid configuration for {config_key}: {invalid_value}"
        context = kwargs.pop('context', {})
        context.update({
            'config_key': config_key,
            'invalid_value': str(invalid_value),
            'expected_type': expected_type
        })
        super().__init__(
            message=message,
            error_code="CONFIG_002",
            context=context,
            **kwargs
        )

# =============================================================================
# QUANTUM SYSTEM EXCEPTIONS
# =============================================================================

class QuantumSystemError(BaseCosmicException):
    """Base exception for quantum system errors"""
    def __init__(self, message: str, error_code: str = "QTM_000", **kwargs):
        # Allow child classes to override these parameters
        severity = kwargs.pop('severity', ErrorSeverity.HIGH)
        category = kwargs.pop('category', ErrorCategory.QUANTUM)
        super().__init__(
            message=message,
            error_code=error_code,
            severity=severity,
            category=category,
            **kwargs
        )

class QuantumEntanglementError(QuantumSystemError):
    """Exception raised when quantum entanglement operations fail"""
    def __init__(self, node_id: Optional[str] = None, operation: str = "unknown", **kwargs):
        if node_id:
            message = f"Quantum entanglement failed for node {node_id} during {operation}"
        else:
            message = f"Quantum entanglement operation failed: {operation}"
        context = kwargs.pop('context', {})
        context.update({
            'node_id': node_id,
            'operation': operation,
            'quantum_state': 'decoherent'
        })
        super().__init__(
            message=message,
            error_code="QTM_001",
            context=context,
            recovery_suggestions=[
                "Re-initialize quantum entanglement",
                "Check quantum coherence levels",
                "Validate quantum node connections"
            ],
            **kwargs
        )

class QuantumCoherenceFailure(QuantumSystemError):
    """Exception raised when quantum coherence drops below threshold"""
    def __init__(self, current_coherence: float, threshold: float = 0.7, **kwargs):
        message = f"Quantum coherence {current_coherence:.3f} below threshold {threshold:.3f}"
        context = kwargs.pop('context', {})
        context.update({
            'current_coherence': current_coherence,
            'threshold': threshold,
            'coherence_deficit': threshold - current_coherence
        })
        # Clamp quantum uncertainty
        quantum_uncertainty = max(0.0, min(1.0, 1.0 - current_coherence))
        super().__init__(
            message=message,
            error_code="QTM_002",
            severity=ErrorSeverity.CRITICAL,
            context=context,
            quantum_uncertainty=quantum_uncertainty,
            recovery_suggestions=[
                "Perform quantum coherence restoration",
                "Reduce quantum noise",
                "Recalibrate quantum systems"
            ],
            **kwargs
        )

class TemporalCoherenceFailure(QuantumSystemError):
    """Exception raised when temporal coherence is compromised"""
    def __init__(self, temporal_drift: float, max_drift: float = 1.0, **kwargs):
        message = f"Temporal coherence failure: drift {temporal_drift:.3f} exceeds maximum {max_drift:.3f}"
        context = kwargs.pop('context', {})
        context.update({
            'temporal_drift': temporal_drift,
            'max_drift': max_drift,
            'drift_excess': temporal_drift - max_drift
        })
        # Clamp quantum uncertainty
        quantum_uncertainty = max(0.0, min(1.0, temporal_drift / max_drift if max_drift > 0 else 1.0))
        super().__init__(
            message=message,
            error_code="QTM_003",
            severity=ErrorSeverity.HIGH,
            context=context,
            recovery_suggestions=[
                "Perform temporal synchronization",
                "Reset temporal anchors",
                "Recalibrate time systems"
            ],
            **kwargs
        )

class DataSynchronizationError(QuantumSystemError):
    """Exception raised when data synchronization fails"""
    def __init__(self, data_source: str, sync_operation: str = "unknown", **kwargs):
        message = f"Data synchronization failed for {data_source} during {sync_operation}"
        context = kwargs.pop('context', {})
        context.update({
            'data_source': data_source,
            'sync_operation': sync_operation,
            'sync_state': 'failed'
        })
        super().__init__(
            message=message,
            error_code="QTM_004",
            severity=ErrorSeverity.MEDIUM,
            context=context,
            recovery_suggestions=[
                "Retry data synchronization",
                "Check data source connectivity",
                "Validate data integrity"
            ],
            **kwargs
        )

# =============================================================================
# SIMULATION EXCEPTIONS
# =============================================================================

class SimulationError(BaseCosmicException):
    """Exception raised when simulation operations fail"""
    def __init__(self, message: str, simulation_type: str = "unknown", error_code: str = "SIM_000", **kwargs):
        context = kwargs.pop('context', {})
        context.update({
            'simulation_type': simulation_type,
            'simulation_state': 'failed'
        })
        super().__init__(
            message=message,
            error_code=error_code,
            severity=kwargs.pop('severity', ErrorSeverity.MEDIUM),
            category=kwargs.pop('category', ErrorCategory.PREDICTION),
            context=context,
            recovery_suggestions=[
                "Restart simulation with different parameters",
                "Check simulation input data",
                "Validate simulation configuration"
            ],
            **kwargs
        )

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

def handle_cosmic_exception(exception: BaseCosmicException,
                            recovery_attempt: bool = True) -> Dict[str, Any]:
    """
    Handle cosmic exceptions with automatic recovery attempts
    """
    logger.error(f" Handling cosmic exception: {exception}")
    result = {
        'handled': True,
        'exception_data': exception.to_dict(),
        'recovery_attempted': False,
        'recovery_successful': False
    }

    if recovery_attempt and exception.recovery_suggestions:
        logger.info(f"🔧 Attempting recovery for {exception.error_code}")
        result['recovery_attempted'] = True
        # Here you would implement actual recovery logic
        # For now, just log the suggestions
        for suggestion in exception.recovery_suggestions:
            logger.info(f" Recovery suggestion: {suggestion}")
        # Simulate recovery attempt (in real implementation, execute recovery)
        result['recovery_successful'] = exception.severity.value in ['LOW', 'MEDIUM']
    return result

def create_error_report(exceptions: List[BaseCosmicException]) -> Dict[str, Any]:
    """
    Create comprehensive error report from multiple exceptions
    """
    if not exceptions:
        return {'total_errors': 0, 'report_generated': datetime.now(timezone.utc).isoformat()} # Use timezone-aware datetime

    severity_counts = {}
    category_counts = {}

    for exc in exceptions:
        severity_counts[exc.severity.value] = severity_counts.get(exc.severity.value, 0) + 1
        category_counts[exc.category.value] = category_counts.get(exc.category.value, 0) + 1

    # Ensure there's at least one exception to find max and calculate avg
    if not exceptions:
        return {'total_errors': 0, 'report_generated': datetime.now(timezone.utc).isoformat()}
        
    most_severe = max(exceptions, key=lambda x: list(ErrorSeverity).index(x.severity)).severity.value
    avg_uncertainty = sum(exc.quantum_uncertainty for exc in exceptions) / len(exceptions)

    return {
        'total_errors': len(exceptions),
        'severity_distribution': severity_counts,
        'category_distribution': category_counts,
        'most_severe': most_severe,
        'avg_uncertainty': avg_uncertainty,
        'errors': [exc.to_dict() for exc in exceptions],
        'report_generated': datetime.now(timezone.utc).isoformat() # Use timezone-aware datetime
    }

# Export all exception classes for easy importing
__all__ = [
    # Base classes
    'BaseCosmicException', 'ErrorSeverity', 'ErrorCategory',
    # Catastrophic exceptions
    'CosmicCollapse', 'QuantumDecoherence', 'OracleMemoryCorruption',
    # Prediction exceptions
    'PredictionSystemFailure', 'ModelNotFound', 'InsufficientTrainingData', 'PredictionConfidenceTooLow',
    # Basketball exceptions
    'BasketballPredictionError', 'GameNotFound', 'PlayerDataIncomplete', 'SeasonDataOutdated', 'InjuryReportUnavailable',
    # Data exceptions
    'DataAccessError', 'DatabaseConnectionFailure', 'DataValidationFailure',
    # Network exceptions
    'NetworkError', 'APIRateLimitExceeded', 'ExternalServiceUnavailable',
    # Cluster exceptions
    'ClusterError', 'RealmUnreachable', 'LoadBalancerFailure',
    # Security exceptions
    'SecurityError', 'InvalidApiKey', 'EncryptionFailure',
    # Temporal exceptions
    'TemporalError', 'ChronosTimeoutExceeded', 'SchedulingConflict',
    # Configuration exceptions
    'ConfigurationError', 'MissingConfiguration', 'InvalidConfiguration',
    # Quantum system exceptions
    'QuantumSystemError', 'QuantumEntanglementError', 'QuantumCoherenceFailure', 'TemporalCoherenceFailure', 'DataSynchronizationError',
    # Simulation exceptions
    'SimulationError',
    # Utility functions
    'handle_cosmic_exception', 'create_error_report'
]

if __name__ == "__main__":
    # Configure logging level for the example
    logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
    

    # Example 1: Critical System Failure
    try:
        raise CosmicCollapse("Core Oracle processing unit unresponsive.", context={"module": "OracleCore"})
    except CosmicCollapse as e:
        report = handle_cosmic_exception(e)

    # Example 2: Prediction Model Not Found
    try:
        raise ModelNotFound("NBA_GamePredictor_v3", search_paths=["/models/prod", "/models/dev"])
    except ModelNotFound as e:
        report = handle_cosmic_exception(e)

    # Example 3: Insufficient Training Data
    try:
        raise InsufficientTrainingData(required_samples=1000, available_samples=750, context={"dataset": "historical_games"})
    except InsufficientTrainingData as e:
        report = handle_cosmic_exception(e)

    # Example 4: Basketball Game Not Found
    try:
        raise GameNotFound("GAME_ID_XYZ_123", league="NBA", context={"source": "LiveDataAPI"})
    except GameNotFound as e:
        report = handle_cosmic_exception(e)

    # Example 5: API Rate Limit Exceeded
    try:
        reset_time = datetime.now(timezone.utc) + timedelta(minutes=1)
        raise APIRateLimitExceeded("SportsDataAPI", rate_limit=100, reset_time=reset_time, context={"user_ip": "***********"})
    except APIRateLimitExceeded as e:
        report = handle_cosmic_exception(e, recovery_attempt=False) # No recovery for rate limit

    # Example 6: Comprehensive Error Report
    exceptions_list = []
    
    try:
        raise DatabaseConnectionFailure("MainPredictionDB", connection_string="db_url_redacted")
    except DatabaseConnectionFailure as e:
        exceptions_list.append(e)

    try:
        raise PlayerDataIncomplete("LeBron James", ["rebounds", "assists_per_game"])
    except PlayerDataIncomplete as e:
        exceptions_list.append(e)
        
    try:
        # Simulate a QuantumCoherenceFailure with traceback
        def simulate_quantum_failure():
            nested_func()
        
        def nested_func():
            deep_nested_func()

        def deep_nested_func():
            raise QuantumCoherenceFailure(current_coherence=0.05, threshold=0.7)

        simulate_quantum_failure()
    except QuantumCoherenceFailure as e:
        exceptions_list.append(e)
        
    try:
        raise InvalidConfiguration("MODEL_PATH", "/non/existent/path", expected_type="directory")
    except InvalidConfiguration as e:
        exceptions_list.append(e)

    full_report = create_error_report(exceptions_list)

