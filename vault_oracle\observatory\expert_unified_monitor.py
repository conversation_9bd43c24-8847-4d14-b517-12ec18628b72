# DIGITAL FINGERPRINT: UUID=bc3d4e5f-6a7b-8c9d-0e1f-2a3b4c5d6e7f | DATE=2025-06-26
"""
(C) 2025 Hyper Medusa Neural Vault, All Rights Reserved.
PROPRIETARY & CONFIDENTIAL — UNAUTHORIZED USE, COPYING, OR DISTRIBUTION IS STRICTLY PROHIBITED.

This file contains proprietary algorithms, business logic, and intellectual property of Hyper Medusa Neural Vault.
Use, reproduction, or disclosure by any means without explicit written permission is a violation of applicable law.
"""

"""
HYPER MEDUSA NEURAL VAULT - Expert Unified Monitor Business Value Documentation
===============================================================================

expert_unified_monitor.py
-------------------------
Centralizes monitoring and observability for the Medusa Vault platform.

Business Value:
- Provides unified, real-time monitoring and alerting for all core services.
- Enables rapid detection and resolution of issues, improving reliability.
- Supports extensibility for custom metrics, dashboards, and alerting plugins.

Extension Points for Plugins & Custom Monitoring:
-------------------------------------------------
- Subclass `ExpertUnifiedMonitor` to add new monitoring or alerting logic.
- Register monitoring plugins via a plugin registry or callback system.
- Add new metrics or data sources by extending the monitor class.
- Use the config system to enable/disable features per environment.

For further details, see module-level docstrings and architecture documentation.
"""

"""
Expert Unified Monitoring System - Oracle Observatory
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

A comprehensive, expert-level monitoring system that unifies all observability needs:
- System health & resource monitoring
- Model performance & accuracy tracking
- API/business metrics & monitoring
- Quantum/prophecy observability
- Alert management & notifications
- Prometheus/Grafana integration
- Oracle Focus integration
- Real-time dashboards & analytics

Features:
- Type-safe, async/sync compatible
- Prometheus metrics export
- Grafana dashboard provisioning
- Multi-channel alerting (FCM, Discord, Slack, Email)
- Oracle Focus deep integration
- Quantum observability & anomaly detection
- Basketball-specific metrics & analytics
- Production-ready error handling
- Comprehensive logging & audit trails
"""

import sys
import os
import asyncio
import logging
import json
import time
import threading
import hashlib
import hmac
import traceback
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable, Union, Set, Tuple
from dataclasses import dataclass, asdict, field
from collections import defaultdict, deque
from enum import Enum
from contextlib import asynccontextmanager
import psutil
import numpy as np
import pandas as pd
from pydantic import BaseModel, Field, validator
import aiohttp
import websockets
from vault_oracle.core.oracle_focus import oracle_focus
from vault_oracle.core.expert_metrics_registry import ExpertMetricsRegistry
from vault_oracle.interfaces.expert_messaging_orchestrator import ExpertMessagingOrchestrator
from vault_oracle.interfaces.unified_message import UnifiedMessage, MessageType, MessagePriority, DeliveryChannel
try:
    from grafana_api.grafana_face import GrafanaFace
    GRAFANA_AVAILABLE = True
except ImportError:
    GrafanaFace = None
    GRAFANA_AVAILABLE = False

# Removed circular import - push_grafana_annotation is defined in this file
import requests


# Optional dependencies with fallbacks
try:
    AIOHTTP_AVAILABLE = True
except ImportError:
    AIOHTTP_AVAILABLE = False

try:
    WEBSOCKETS_AVAILABLE = True
except ImportError:
    WEBSOCKETS_AVAILABLE = False

# Add project root to path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../")))

# Oracle Focus and Core Imports
try:
    ORACLE_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Oracle components not available: {e}")
    ORACLE_AVAILABLE = False

    def oracle_focus(func):
        return func

    # Mock classes for missing Oracle components
    # TODO: Replace with production implementation
    # TODO: Replace with production implementation
    class MockUnifiedMessage:
        def __init__(self, *args, **kwargs): pass

    # TODO: Replace with production implementation
    # TODO: Replace with production implementation
    class MockMessageType:
        SYSTEM_ALERT = "system_alert"
        GAME_ALERT = "game_alert"

    # TODO: Replace with production implementation
    # TODO: Replace with production implementation
    class MockMessagePriority:
        HIGH = "high"
        NORMAL = "normal"

    # TODO: Replace with production implementation
    # TODO: Replace with production implementation
    class MockDeliveryChannel:
        QUANTUM_MESSENGER = "quantum_messenger"
        FCM_PUSH = "fcm_push"

    UnifiedMessage = MockUnifiedMessage
    MessageType = MockMessageType
    MessagePriority = MockMessagePriority
    DeliveryChannel = MockDeliveryChannel

# Prometheus imports with fallback
try:
    from prometheus_client import (
        Counter, Histogram, Gauge, Summary, Info, Enum as PrometheusEnum,
        CollectorRegistry, generate_latest, CONTENT_TYPE_LATEST,
        start_http_server, push_to_gateway
    )
    PROMETHEUS_AVAILABLE = True
except ImportError:
    logging.warning("Prometheus client not available. Using mock metrics.")
    PROMETHEUS_AVAILABLE = False

    # TODO: Replace with production implementation
    # TODO: Replace with production implementation
    class MockMetric:
        def inc(self, *args, **kwargs): pass
        def dec(self, *args, **kwargs): pass
        def set(self, *args, **kwargs): pass
        def observe(self, *args, **kwargs): pass
        def labels(self, *args, **kwargs): return self
        def time(self): return self
        def __enter__(self): return self
        def __exit__(self, *args): pass

    Counter = Histogram = Gauge = Summary = Info = PrometheusEnum = lambda *args, **kwargs: MockMetric()
    CollectorRegistry = lambda: None
    generate_latest = lambda *args: b"Mock metrics"
    CONTENT_TYPE_LATEST = "text/plain"

# Grafana API imports with fallback
try:
    GRAFANA_AVAILABLE = True
except ImportError:
    logging.warning("Grafana API not available. Dashboard creation will be disabled.")
    GRAFANA_AVAILABLE = False
    GrafanaFace = None

logger = logging.getLogger("expert_unified_monitor")


# =============================================================================
# CORE DATA MODELS & CONFIGURATION
# =============================================================================

class MonitoringLevel(Enum):
    """Monitoring detail levels"""
    MINIMAL = "minimal"
    STANDARD = "standard"
    COMPREHENSIVE = "comprehensive"
    QUANTUM = "quantum"


class AlertSeverity(Enum):
    """Alert severity levels"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"


class MetricType(Enum):
    """Types of metrics"""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    SUMMARY = "summary"


class ComponentStatus(Enum):
    """Component health status"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


@dataclass
class MetricDefinition:
    """Definition for a metric"""
    name: str
    metric_type: MetricType
    description: str
    labels: List[str] = field(default_factory=list)
    buckets: Optional[List[float]] = None
    unit: Optional[str] = None
    namespace: str = "nba_book"


@dataclass
class ExpertMetric:
    """Enhanced metric data with Oracle Focus integration"""
    name: str
    value: Union[float, int, str]
    timestamp: datetime
    labels: Dict[str, str] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    component: str = "system"
    level: MonitoringLevel = MonitoringLevel.STANDARD
    quantum_signature: Optional[str] = None

    def __post_init__(self):
        """Generate quantum signature for metric integrity"""
        if self.quantum_signature is None:
            data = f"{self.name}:{self.value}:{self.timestamp.isoformat()}"
            self.quantum_signature = hashlib.sha256(data.encode()).hexdigest()[:16]


@dataclass
class ExpertAlert:
    """Enhanced alert with Oracle Focus integration"""
    id: str
    severity: AlertSeverity
    title: str
    description: str
    timestamp: datetime
    component: str
    source: str
    resolved: bool = False
    acknowledged: bool = False
    labels: Dict[str, str] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    oracle_signature: Optional[str] = None
    prophecy_confidence: Optional[float] = None

    def __post_init__(self):
        """Generate Oracle signature for alert integrity"""
        if self.oracle_signature is None:
            data = f"{self.title}:{self.description}:{self.timestamp.isoformat()}"
            self.oracle_signature = hashlib.sha256(data.encode()).hexdigest()[:16]


@dataclass
class ComponentHealth:
    """Component health status"""
    component: str
    status: ComponentStatus
    last_check: datetime
    metrics: Dict[str, float] = field(default_factory=dict)
    errors: List[str] = field(default_factory=list)
    uptime: Optional[float] = None
    version: Optional[str] = None


class ExpertMonitoringConfig(BaseModel):
    """Configuration for Expert Unified Monitoring"""
    # Core settings
    monitoring_level: MonitoringLevel = MonitoringLevel.STANDARD
    enable_prometheus: bool = True
    enable_grafana: bool = True
    enable_quantum_observability: bool = True

    # Prometheus settings
    prometheus_port: int = 9191
    prometheus_host: str = "localhost"
    prometheus_gateway: Optional[str] = None
    metrics_prefix: str = "nba_book"

    # Grafana settings
    grafana_url: str = "http://localhost:3000"
    grafana_token: Optional[str] = None
    grafana_org_id: int = 1

    # Alert settings
    alert_channels: List[str] = Field(default_factory=lambda: ["console", "file", "oracle"])
    alert_cooldown: int = 300 # 5 minutes
    max_alerts: int = 10000

    # Health check settings
    health_check_interval: int = 30
    component_timeout: float = 10.0

    # Data retention
    metrics_retention_days: int = 30
    alerts_retention_days: int = 90

    # Performance settings
    max_metrics_per_component: int = 100000
    batch_size: int = 1000
    async_processing: bool = True

    # Oracle Focus integration
    oracle_focus_enabled: bool = True
    quantum_validation: bool = True
    prophecy_integration: bool = True

    class Config:
        use_enum_values = True


# =============================================================================
# CORE MONITORING COMPONENTS
# =============================================================================

class ExpertMetricsCollector:
    """Advanced metrics collection with Oracle Focus integration"""

    def __init__(self, config: ExpertMonitoringConfig):
        self.config = config
        self.metrics: Dict[str, deque] = defaultdict(
            lambda: deque(maxlen=config.max_metrics_per_component)
        )
        self.prometheus_metrics: Dict[str, Any] = {}
        self.metric_definitions: Dict[str, MetricDefinition] = {}
        self._lock = threading.RLock()
        self._registry = CollectorRegistry() if PROMETHEUS_AVAILABLE else None

        # Initialize Prometheus metrics if available
        if PROMETHEUS_AVAILABLE and config.enable_prometheus:
            self._setup_prometheus_metrics()

        logger.info(" MEDUSA VAULT: ExpertMetricsCollector initialized")

    async def initialize(self):
        """Initialize the metrics collector"""
        logger.info(" MEDUSA VAULT: 🔧 Initializing ExpertMetricsCollector...")
        try:
            # Initialize Prometheus registry if needed
            if PROMETHEUS_AVAILABLE and self.config.enable_prometheus and not self._registry:
                self._registry = CollectorRegistry()
                self._setup_prometheus_metrics()

            # Start background metrics collection if configured
            if hasattr(self.config, 'enable_background_collection') and self.config.enable_background_collection:
                # Could start background tasks here
                pass

            logger.info(" MEDUSA VAULT: ExpertMetricsCollector initialized successfully")
            return True
        except Exception as e:
            logger.error(f" TITAN PROCESSING FAILED: initialize ExpertMetricsCollector: {e}")
            return False

    async def _collect_system_metrics(self):
        """Background task to collect system metrics"""
        while True:
            try:
                # Collect CPU usage
                cpu_usage = psutil.cpu_percent(interval=None)
                self.record_metric(ExpertMetric(
                    name="system_cpu_usage",
                    value=cpu_usage,
                    timestamp=datetime.now(),
                    labels={"host": "localhost"},
                    component="system"
                ))

                # Collect memory usage
                memory = psutil.virtual_memory()
                self.record_metric(ExpertMetric(
                    name="system_memory_usage",
                    value=memory.percent,
                    timestamp=datetime.now(),
                    labels={"host": "localhost"},
                    component="system"
                ))

                # Collect disk usage
                try:
                    disk = psutil.disk_usage('/')
                    disk_usage = (disk.used / disk.total) * 100
                    self.record_metric(ExpertMetric(
                        name="system_disk_usage",
                        value=disk_usage,
                        timestamp=datetime.now(),
                        labels={"host": "localhost", "mount": "/"},
                        component="system"
                    ))
                except Exception:
                    # Handle Windows path
                    disk = psutil.disk_usage('C:\\')
                    disk_usage = (disk.used / disk.total) * 100
                    self.record_metric(ExpertMetric(
                        name="system_disk_usage",
                        value=disk_usage,
                        timestamp=datetime.now(),
                        labels={"host": "localhost", "mount": "C:"},
                        component="system"
                    ))

                await asyncio.sleep(self.config.health_check_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.warning(f"System metrics collection error: {e}")
                await asyncio.sleep(self.config.health_check_interval)

    def _setup_prometheus_metrics(self):
        """Setup core Prometheus metrics"""
        core_metrics = [
            MetricDefinition("system_cpu_usage", MetricType.GAUGE, "CPU usage percentage", ["host"]),
            MetricDefinition("system_memory_usage", MetricType.GAUGE, "Memory usage percentage", ["host"]),
            MetricDefinition("system_disk_usage", MetricType.GAUGE, "Disk usage percentage", ["host", "mount"]),
            MetricDefinition("api_request_duration", MetricType.HISTOGRAM, "API request duration", ["method", "endpoint", "status"]),
            MetricDefinition("api_request_total", MetricType.COUNTER, "Total API requests", ["method", "endpoint", "status"]),
            MetricDefinition("model_prediction_accuracy", MetricType.GAUGE, "Model prediction accuracy", ["model", "version"]),
            MetricDefinition("model_inference_duration", MetricType.HISTOGRAM, "Model inference duration", ["model", "version"]),
            MetricDefinition("quantum_coherence", MetricType.GAUGE, "Quantum system coherence", ["component"]),
            MetricDefinition("oracle_prophecy_confidence", MetricType.GAUGE, "Oracle prophecy confidence", ["prophet", "type"]),
        ]

        for metric_def in core_metrics:
            self.register_metric_definition(metric_def)

    def register_metric_definition(self, metric_def: MetricDefinition):
        """Register a new metric definition"""
        with self._lock:
            self.metric_definitions[metric_def.name] = metric_def

            if PROMETHEUS_AVAILABLE and self.config.enable_prometheus:
                full_name = f"{self.config.metrics_prefix}_{metric_def.name}"

                if metric_def.metric_type == MetricType.COUNTER:
                    self.prometheus_metrics[metric_def.name] = Counter(
                        full_name, metric_def.description, metric_def.labels, registry=self._registry
                    )
                elif metric_def.metric_type == MetricType.GAUGE:
                    self.prometheus_metrics[metric_def.name] = Gauge(
                        full_name, metric_def.description, metric_def.labels, registry=self._registry
                    )
                elif metric_def.metric_type == MetricType.HISTOGRAM:
                    buckets = metric_def.buckets if metric_def.buckets is not None else (0.01, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0)
                    self.prometheus_metrics[metric_def.name] = Histogram(
                        full_name, metric_def.description, metric_def.labels,
                        buckets=buckets, registry=self._registry
                    )
                elif metric_def.metric_type == MetricType.SUMMARY:
                    self.prometheus_metrics[metric_def.name] = Summary(
                        full_name, metric_def.description, metric_def.labels, registry=self._registry
                    )

    @oracle_focus
    def record_metric(self, metric: ExpertMetric):
        """Record a metric with Oracle Focus validation"""
        with self._lock:
            # Store in time-series collection
            self.metrics[metric.name].append(metric)

            # Update Prometheus metric if available
            if PROMETHEUS_AVAILABLE and metric.name in self.prometheus_metrics:
                prom_metric = self.prometheus_metrics[metric.name]

                try:
                    if metric.labels:
                        prom_metric = prom_metric.labels(**metric.labels)

                    if isinstance(prom_metric, (Counter, type(prom_metric))) and hasattr(prom_metric, 'inc'):
                        if isinstance(metric.value, (int, float)):
                            prom_metric.inc(metric.value)
                        else:
                            prom_metric.inc()
                    elif hasattr(prom_metric, 'set'):
                        prom_metric.set(float(metric.value))
                    elif hasattr(prom_metric, 'observe'):
                        prom_metric.observe(float(metric.value))

                except Exception as e:
                    logger.warning(f" TITAN PROCESSING FAILED: update Prometheus metric {metric.name}: {e}")

    def get_metrics(self, name: str, since: Optional[datetime] = None,
                    component: Optional[str] = None) -> List[ExpertMetric]:
        """Get metrics with optional filtering"""
        with self._lock:
            metrics = list(self.metrics.get(name, []))

            if since:
                metrics = [m for m in metrics if m.timestamp >= since]

            if component:
                metrics = [m for m in metrics if m.component == component]

            return metrics

    def get_metric_summary(self, name: str, window_minutes: int = 60) -> Dict[str, Any]:
        """Get statistical summary of a metric over time window"""
        since = datetime.now() - timedelta(minutes=window_minutes)
        metrics = self.get_metrics(name, since=since)

        if not metrics:
            return {}

        values = [float(m.value) for m in metrics if isinstance(m.value, (int, float))]
        if not values:
            return {}

        return {
            'count': len(values),
            'min': min(values),
            'max': max(values),
            'mean': np.mean(values),
            'median': np.median(values),
            'std': np.std(values),
            'p95': np.percentile(values, 95),
            'p99': np.percentile(values, 99),
            'latest': values[-1] if values else None,
            'trend': 'increasing' if len(values) > 1 and values[-1] > values[0] else 'stable'
        }

    def export_prometheus_metrics(self) -> bytes:
        """Export Prometheus metrics"""
        if PROMETHEUS_AVAILABLE and self._registry:
            return generate_latest(self._registry)
        return b"Prometheus not available"

    async def shutdown(self):
        """Gracefully shutdown metrics collector, cancel background tasks, flush metrics."""
        # Add logic to cancel any running background tasks if present
        logger.info(" MEDUSA VAULT: Shutting down ExpertMetricsCollector")
        # If you add background tasks in the future, cancel them here
        # Flush metrics if needed
        pass


class ExpertAlertManager:
    """Advanced alert management with Oracle Focus integration"""

    def __init__(self, config: ExpertMonitoringConfig):
        self.config = config
        self.alerts: deque = deque(maxlen=config.max_alerts)
        self.alert_rules: List[Callable] = []
        self.alert_history: Dict[str, datetime] = {} # For cooldown tracking
        self._lock = threading.RLock()
        self._messaging_orchestrator = None

        # Setup messaging if Oracle is available
        if ORACLE_AVAILABLE:
            try:
                self._messaging_orchestrator = ExpertMessagingOrchestrator()
            except Exception as e:
                logger.warning(f"Could not initialize messaging orchestrator: {e}")

        logger.info(" MEDUSA VAULT: 🚨 ExpertAlertManager initialized")

    async def initialize(self):
        """Initialize alert management system"""
        try:
            logger.info(" MEDUSA VAULT: 🔧 Initializing ExpertAlertManager...")

            # Initialize messaging orchestrator if available
            if ORACLE_AVAILABLE and not self._messaging_orchestrator:
                try:
                    self._messaging_orchestrator = ExpertMessagingOrchestrator()
                    logger.info(" MEDUSA VAULT: Messaging orchestrator initialized")
                except Exception as e:
                    logger.warning(f"Could not initialize messaging orchestrator: {e}")

            # Setup alert directories
            alerts_dir = Path("logs/alerts")
            alerts_dir.mkdir(parents=True, exist_ok=True)

            logger.info(" MEDUSA VAULT: ExpertAlertManager initialized successfully")
            return True

        except Exception as e:
            logger.error(f" TITAN PROCESSING FAILED: initialize ExpertAlertManager: {e}")
            return False

    def add_alert_rule(self, rule_func: Callable):
        """Add an alert rule function"""
        self.alert_rules.append(rule_func)
        logger.info(f"Added alert rule: {rule_func.__name__}")

    @oracle_focus
    async def create_alert(self, severity: AlertSeverity, title: str, description: str,
                           component: str, source: str, labels: Dict[str, str] = None,
                           metadata: Dict[str, Any] = None) -> ExpertAlert:
        """Create a new alert with Oracle Focus validation"""

        # Check cooldown to prevent spam
        alert_key = f"{component}:{title}"
        if self._is_in_cooldown(alert_key):
            return None

        alert = ExpertAlert(
            id=f"{source}_{int(time.time())}_{hash(title) % 10000}",
            severity=severity,
            title=title,
            description=description,
            timestamp=datetime.now(),
            component=component,
            source=source,
            labels=labels or {},
            metadata=metadata or {}
        )

        with self._lock:
            self.alerts.append(alert)
            self.alert_history[alert_key] = alert.timestamp

        # Send alert through channels
        await self._dispatch_alert(alert)

        logger.info(f"🚨 Alert created: [{severity.value}] {title}")
        return alert

    def _is_in_cooldown(self, alert_key: str) -> bool:
        """Check if alert is in cooldown period"""
        if alert_key not in self.alert_history:
            return False

        last_alert = self.alert_history[alert_key]
        cooldown_expires = last_alert + timedelta(seconds=self.config.alert_cooldown)
        return datetime.now() < cooldown_expires

    async def _dispatch_alert(self, alert: ExpertAlert):
        """Dispatch alert through configured channels"""
        for channel in self.config.alert_channels:
            try:
                if channel == "console":
                    self._send_console_alert(alert)
                elif channel == "file":
                    self._send_file_alert(alert)
                elif channel == "oracle" and self._messaging_orchestrator:
                    await self._send_oracle_alert(alert)
                # --- Grafana annotation push ---
                elif channel == "grafana":
                    push_grafana_annotation(
                        message=f"[{alert.severity.value.upper()}] {alert.title}: {alert.description}",
                        tags=["alert", alert.severity.value, alert.component],
                        config=self.config
                    )
                # Add more channels as needed (Discord, Slack, Email, etc.)
            except Exception as e:
                logger.error(f" TITAN PROCESSING FAILED: send alert via {channel}: {e}")

    def _send_console_alert(self, alert: ExpertAlert):
        """Send alert to console"""
        log_level = {
            AlertSeverity.CRITICAL: logging.CRITICAL,
            AlertSeverity.HIGH: logging.ERROR,
            AlertSeverity.MEDIUM: logging.WARNING,
            AlertSeverity.LOW: logging.INFO,
            AlertSeverity.INFO: logging.INFO
        }.get(alert.severity, logging.INFO)

        logger.log(log_level, f"🚨 [{alert.severity.value.upper()}] {alert.title}: {alert.description}")

    def _send_file_alert(self, alert: ExpertAlert):
        """Send alert to file"""
        alerts_dir = Path("logs/alerts")
        alerts_dir.mkdir(parents=True, exist_ok=True)

        alert_file = alerts_dir / f"alerts_{datetime.now().strftime('%Y%m%d')}.json"

        alert_data = asdict(alert)
        alert_data['timestamp'] = alert.timestamp.isoformat()
        alert_data['severity'] = alert.severity.value # Convert enum to string

        with open(alert_file, 'a') as f:
            f.write(json.dumps(alert_data) + '\n')

    async def _send_oracle_alert(self, alert: ExpertAlert):
        """Send alert through Oracle messaging system"""
        if not self._messaging_orchestrator:
            return

        message = UnifiedMessage(
            message_type=MessageType.SYSTEM_ALERT,
            priority=MessagePriority.HIGH if alert.severity in [AlertSeverity.CRITICAL, AlertSeverity.HIGH] else MessagePriority.NORMAL,
            title=f"Oracle Alert: {alert.title}",
            body=alert.description,
            content={
                "alert_id": alert.id,
                "severity": alert.severity.value,
                "component": alert.component,
                "source": alert.source,
                "metadata": alert.metadata
            },
            channels=[DeliveryChannel.QUANTUM_MESSENGER, DeliveryChannel.FCM_PUSH]
        )

        try:
            results = await self._messaging_orchestrator.send_unified_message(message)
            logger.info(f"Alert sent through Oracle messaging: {len(results)} channels")
        except Exception as e:
            logger.error(f" TITAN PROCESSING FAILED: send Oracle alert: {e}")

    def get_active_alerts(self, severity: Optional[AlertSeverity] = None,
                           component: Optional[str] = None) -> List[ExpertAlert]:
        """Get active (unresolved) alerts"""
        with self._lock:
            alerts = [a for a in self.alerts if not a.resolved]

            if severity:
                alerts = [a for a in alerts if a.severity == severity]

            if component:
                alerts = [a for a in alerts if a.component == component]

            return alerts

    def resolve_alert(self, alert_id: str) -> bool:
        """Mark an alert as resolved"""
        with self._lock:
            for alert in self.alerts:
                if alert.id == alert_id:
                    alert.resolved = True
                    logger.info(f"Alert resolved: {alert_id}")
                    return True
            return False

    def get_alert_summary(self) -> Dict[str, Any]:
        """Get summary of alert statistics"""
        with self._lock:
            active_alerts = [a for a in self.alerts if not a.resolved]

            summary = {
                'total_alerts': len(self.alerts),
                'active_alerts': len(active_alerts),
                'resolved_alerts': len(self.alerts) - len(active_alerts),
                'by_severity': {},
                'by_component': {},
                'recent_alerts': len([a for a in active_alerts if a.timestamp > datetime.now() - timedelta(hours=1)])
            }

            for alert in active_alerts:
                # Count by severity
                severity_key = alert.severity.value
                summary['by_severity'][severity_key] = summary['by_severity'].get(severity_key, 0) + 1

                # Count by component
                component_key = alert.component
                summary['by_component'][component_key] = summary['by_component'].get(component_key, 0) + 1

            return summary

    async def shutdown(self):
        """Gracefully shutdown alert manager, flush alerts, close messaging orchestrator."""
        logger.info(" MEDUSA VAULT: Shutting down ExpertAlertManager")
        # If messaging orchestrator has a shutdown/close, call it
        if self._messaging_orchestrator and hasattr(self._messaging_orchestrator, 'shutdown'):
            await self._messaging_orchestrator.shutdown()
        # Flush alerts to disk if needed
        pass


class ExpertHealthMonitor:
    """Advanced health monitoring with Oracle Focus integration"""

    def __init__(self, config: ExpertMonitoringConfig, metrics_collector: ExpertMetricsCollector):
        self.config = config
        self.metrics = metrics_collector
        self.components: Dict[str, ComponentHealth] = {}
        self._monitoring_active = False
        self._monitoring_task = None
        self._lock = threading.RLock()

        logger.info(" MEDUSA VAULT: 💚 ExpertHealthMonitor initialized")

    def register_component(self, name: str, health_check: Optional[Callable] = None,
                           version: Optional[str] = None):
        """Register a component for health monitoring"""
        with self._lock:
            self.components[name] = ComponentHealth(
                component=name,
                status=ComponentStatus.UNKNOWN,
                last_check=datetime.now(),
                version=version
            )

        logger.info(f"Registered component: {name}")

    @oracle_focus
    async def start_monitoring(self):
        """Start health monitoring"""
        if self._monitoring_active:
            logger.warning(" TITAN WARNING: Health monitoring already active")
            return

        self._monitoring_active = True
        self._monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info(" MEDUSA VAULT: 💚 Health monitoring started")

    async def stop_monitoring(self):
        """Stop health monitoring"""
        self._monitoring_active = False

        if self._monitoring_task and not self._monitoring_task.done():
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass

        logger.info(" MEDUSA VAULT: Health monitoring stopped")

    async def _monitoring_loop(self):
        """Main health monitoring loop"""
        while self._monitoring_active:
            try:
                await self._check_all_components()
                await self._collect_system_metrics()
                await asyncio.sleep(self.config.health_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f" MEDUSA ERROR: health monitoring loop: {e}")
                await asyncio.sleep(5) # Brief pause on error

    async def _check_all_components(self):
        """Check health of all registered components"""
        for component_name in list(self.components.keys()):
            await self._check_component_health(component_name)

    async def _check_component_health(self, component_name: str):
        """Check health of a specific component"""
        try:
            with self._lock:
                component = self.components.get(component_name)
                if not component:
                    return

                # Basic health check - can be extended with custom checks
                start_time = time.time()

                # System resource check
                cpu_usage = psutil.cpu_percent(interval=0.1)
                memory_usage = psutil.virtual_memory().percent

                # Determine status
                if cpu_usage > 90 or memory_usage > 95:
                    status = ComponentStatus.CRITICAL
                elif cpu_usage > 75 or memory_usage > 85:
                    status = ComponentStatus.DEGRADED
                else:
                    status = ComponentStatus.HEALTHY

                # Update component health
                component.status = status
                component.last_check = datetime.now()
                component.metrics = {
                    'cpu_usage': cpu_usage,
                    'memory_usage': memory_usage,
                    'response_time': (time.time() - start_time) * 1000
                }
                component.uptime = time.time() - start_time

        except Exception as e:
            logger.error(f"Error checking component {component_name}: {e}")
            with self._lock:
                if component_name in self.components:
                    self.components[component_name].status = ComponentStatus.CRITICAL
                    self.components[component_name].errors.append(str(e))

    async def _collect_system_metrics(self):
        """Collect system-wide metrics"""
        try:
            timestamp = datetime.now()

            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=0.1)
            self.metrics.record_metric(ExpertMetric(
                name="system_cpu_usage",
                value=cpu_percent,
                timestamp=timestamp,
                labels={"host": "localhost"},
                component="system"
            ))

            # Memory metrics
            memory = psutil.virtual_memory()
            self.metrics.record_metric(ExpertMetric(
                name="system_memory_usage",
                value=memory.percent,
                timestamp=timestamp,
                labels={"host": "localhost"},
                component="system"
            ))

            # Disk metrics
            for partition in psutil.disk_partitions():
                try:
                    disk = psutil.disk_usage(partition.mountpoint)
                    self.metrics.record_metric(ExpertMetric(
                        name="system_disk_usage",
                        value=disk.percent,
                        timestamp=timestamp,
                        labels={"host": "localhost", "mount": partition.mountpoint},
                        component="system"
                    ))
                except PermissionError:
                    continue # Skip inaccessible drives

            # Network metrics
            try:
                net_io = psutil.net_io_counters()
                self.metrics.record_metric(ExpertMetric(
                    name="system_network_bytes_sent",
                    value=net_io.bytes_sent,
                    timestamp=timestamp,
                    labels={"host": "localhost"},
                    component="system"
                ))
                self.metrics.record_metric(ExpertMetric(
                    name="system_network_bytes_recv",
                    value=net_io.bytes_recv,
                    timestamp=timestamp,
                    labels={"host": "localhost"},
                    component="system"
                ))
            except Exception:
                pass # Network stats may not be available

        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")

    def get_component_health(self, component_name: str) -> Optional[ComponentHealth]:
        """Get health status of a specific component"""
        with self._lock:
            return self.components.get(component_name)

    def get_overall_health(self) -> Dict[str, Any]:
        """Get overall system health summary"""
        with self._lock:
            component_statuses = [c.status for c in self.components.values()]

            # Determine overall status
            if ComponentStatus.CRITICAL in component_statuses:
                overall_status = ComponentStatus.CRITICAL
            elif ComponentStatus.DEGRADED in component_statuses:
                overall_status = ComponentStatus.DEGRADED
            elif ComponentStatus.HEALTHY in component_statuses:
                overall_status = ComponentStatus.HEALTHY
            else:
                overall_status = ComponentStatus.UNKNOWN

            return {
                'overall_status': overall_status.value,
                'timestamp': datetime.now().isoformat(),
                'components': {
                    name: {
                        'status': comp.status.value,
                        'last_check': comp.last_check.isoformat(),
                        'metrics': comp.metrics,
                        'uptime': comp.uptime,
                        'version': comp.version,
                        'error_count': len(comp.errors)
                    }
                    for name, comp in self.components.items()
                },
                'system_summary': {
                    'total_components': len(self.components),
                    'healthy_components': len([c for c in self.components.values() if c.status == ComponentStatus.HEALTHY]),
                    'degraded_components': len([c for c in self.components.values() if c.status == ComponentStatus.DEGRADED]),
                    'critical_components': len([c for c in self.components.values() if c.status == ComponentStatus.CRITICAL])
                }
            }

    async def initialize(self):
        """Initialize the health monitoring system"""
        logger.info(" MEDUSA VAULT: 🔧 Initializing ExpertHealthMonitor...")
        try:
            # Register default system components
            self.register_component("system", version="1.0")
            self.register_component("database", version="1.0")
            self.register_component("api", version="1.0")

            # Initialize health check directories
            health_dir = Path("logs/health")
            health_dir.mkdir(parents=True, exist_ok=True)

            logger.info(" MEDUSA VAULT: ExpertHealthMonitor initialized successfully")
            return True

        except Exception as e:
            logger.error(f" TITAN PROCESSING FAILED: initialize ExpertHealthMonitor: {e}")
            return False

    async def shutdown(self):
        """Gracefully shutdown health monitor, cancel monitoring task."""
        logger.info(" MEDUSA VAULT: Shutting down ExpertHealthMonitor")
        await self.stop_monitoring()


class ExpertModelMonitor:
    """Expert model performance and accuracy monitoring"""

    def __init__(self, config: ExpertMonitoringConfig, metrics_collector: ExpertMetricsCollector,
                 alert_manager: ExpertAlertManager):
        self.config = config
        self.metrics = metrics_collector
        self.alerts = alert_manager
        self.models: Dict[str, Dict[str, Any]] = {}

        logger.info(" MEDUSA VAULT: ExpertModelMonitor initialized")

    def register_model(self, model_name: str, version: str = "1.0",
                       accuracy_threshold: float = 0.85):
        """Register a model for monitoring"""
        self.models[model_name] = {
            'version': version,
            'accuracy_threshold': accuracy_threshold,
            'last_prediction': None,
            'prediction_count': 0,
            'accuracy_history': deque(maxlen=1000)
        }
        logger.info(f"Registered model: {model_name} v{version}")

    @oracle_focus
    def record_prediction(self, model_name: str, prediction_time: float,
                          accuracy: Optional[float] = None, metadata: Dict[str, Any] = None):
        """Record a model prediction"""
        timestamp = datetime.now()

        # Record prediction duration
        self.metrics.record_metric(ExpertMetric(
            name="model_inference_duration",
            value=prediction_time,
            timestamp=timestamp,
            labels={"model": model_name, "version": self.models.get(model_name, {}).get('version', '1.0')},
            component="model",
            metadata=metadata
        ))

        # Record accuracy if provided
        if accuracy is not None:
            self.metrics.record_metric(ExpertMetric(
                name="model_prediction_accuracy",
                value=accuracy,
                timestamp=timestamp,
                labels={"model": model_name, "version": self.models.get(model_name, {}).get('version', '1.0')},
                component="model",
                metadata=metadata
            ))

        # Update model tracking
        if model_name in self.models:
            self.models[model_name]['last_prediction'] = timestamp
            self.models[model_name]['prediction_count'] += 1
            self.models[model_name]['accuracy_history'].append(accuracy)

            # Check accuracy threshold
            threshold = self.models[model_name]['accuracy_threshold']
            if accuracy < threshold:
                asyncio.create_task(self.alerts.create_alert(
                    severity=AlertSeverity.MEDIUM,
                    title=f"Model Accuracy Below Threshold",
                    description=f"Model {model_name} accuracy {accuracy:.3f} below threshold {threshold:.3f}",
                    component="model",
                    source="model_monitor",
                    labels={"model": model_name},
                    metadata={"accuracy": accuracy, "threshold": threshold}
                ))

    def get_model_summary(self, model_name: str) -> Dict[str, Any]:
        """Get comprehensive model performance summary"""
        if model_name not in self.models:
            return {}

        model_info = self.models[model_name]

        # Get recent metrics
        accuracy_summary = self.metrics.get_metric_summary("model_prediction_accuracy", window_minutes=60)
        duration_summary = self.metrics.get_metric_summary("model_inference_duration", window_minutes=60)

        return {
            'model_name': model_name,
            'version': model_info['version'],
            'prediction_count': model_info['prediction_count'],
            'last_prediction': model_info['last_prediction'].isoformat() if model_info['last_prediction'] else None,
            'accuracy_threshold': model_info['accuracy_threshold'],
            'recent_accuracy': accuracy_summary,
            'recent_performance': duration_summary,
            'accuracy_trend': self._calculate_accuracy_trend(model_name)
        }

    def _calculate_accuracy_trend(self, model_name: str) -> str:
        """Calculate accuracy trend for a model"""
        if model_name not in self.models:
            return "unknown"

        accuracy_history = list(self.models[model_name]['accuracy_history'])
        if len(accuracy_history) < 10:
            return "insufficient_data"

        recent = accuracy_history[-5:]
        older = accuracy_history[-10:-5]

        recent_avg = np.mean(recent)
        older_avg = np.mean(older)

        if recent_avg > older_avg * 1.05:
            return "improving"
        elif recent_avg < older_avg * 0.95:
            return "declining"
        else:
            return "stable"

    async def initialize(self):
        """Initialize the model monitoring system"""
        logger.info(" MEDUSA VAULT: 🔧 Initializing ExpertModelMonitor...")
        try:
            # Register default NBA models if they exist
            default_models = [
                ("nba_prophet", "2.0", 0.85),
                ("wnba_prophet", "2.0", 0.85),
                ("game_predictor", "1.5", 0.80),
                ("player_performance", "1.0", 0.75)
            ]

            for model_name, version, threshold in default_models:
                self.register_model(model_name, version, threshold)

            # Initialize model tracking directories
            models_dir = Path("logs/models")
            models_dir.mkdir(parents=True, exist_ok=True)

            logger.info(" MEDUSA VAULT: ExpertModelMonitor initialized successfully")
            return True

        except Exception as e:
            logger.error(f" TITAN PROCESSING FAILED: initialize ExpertModelMonitor: {e}")
            return False

    async def shutdown(self):
        """Gracefully shutdown model monitor, flush model stats if needed."""
        logger.info(" MEDUSA VAULT: Shutting down ExpertModelMonitor")
        # Add logic to flush or close resources if needed
        pass


class ExpertAPIMonitor:
    """Expert API performance and business metrics monitoring"""

    def __init__(self, config: ExpertMonitoringConfig, metrics_collector: ExpertMetricsCollector,
                 alert_manager: ExpertAlertManager):
        self.config = config
        self.metrics = metrics_collector
        self.alerts = alert_manager
        self.endpoints: Set[str] = set()
        logger.info(" MEDUSA VAULT: 🌐 ExpertAPIMonitor initialized")

    @oracle_focus
    def record_request(self, endpoint: str, method: str, status_code: int,
                       duration: float, error: Optional[str] = None,
                       user_agent: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None):
        """Record an API request"""
        timestamp = datetime.now()

        # Track endpoint
        self.endpoints.add(endpoint)

        # Record request duration
        self.metrics.record_metric(ExpertMetric(
            name="api_request_duration",
            value=duration,
            timestamp=timestamp,
            labels={"method": method, "endpoint": endpoint, "status": str(status_code)},
            component="api",
            metadata=dict(metadata or {}, error=error, user_agent=user_agent)
        ))

        # Record request count
        self.metrics.record_metric(ExpertMetric(
            name="api_request_total",
            value=1,
            timestamp=timestamp,
            labels={"method": method, "endpoint": endpoint, "status": str(status_code)},
            component="api"
        ))

        # Check for alerts
        if status_code >= 500:
            asyncio.create_task(self.alerts.create_alert(
                severity=AlertSeverity.HIGH,
                title=f"API Server Error",
                description=f"HTTP {status_code} on {method} {endpoint}",
                component="api",
                source="api_monitor",
                labels={"endpoint": endpoint, "method": method, "status": str(status_code)},
                metadata={"error": error, "duration": duration}
            ))
        elif duration > 5.0: # Slow response threshold
            asyncio.create_task(self.alerts.create_alert(
                severity=AlertSeverity.MEDIUM,
                title=f"Slow API Response",
                description=f"{method} {endpoint} took {duration:.2f}s",
                component="api",
                source="api_monitor",
                labels={"endpoint": endpoint, "method": method},
                metadata={"duration": duration}
            ))

    def get_api_summary(self) -> Dict[str, Any]:
        """Get comprehensive API performance summary"""
        # Get metrics for last hour
        duration_summary = self.metrics.get_metric_summary("api_request_duration", window_minutes=60)

        # Calculate error rate
        recent_requests = self.metrics.get_metrics("api_request_total",
                                                   since=datetime.now() - timedelta(hours=1))

        total_requests = len(recent_requests)
        error_requests = len([r for r in recent_requests if r.labels.get('status', '200').startswith(('4', '5'))])
        error_rate = (error_requests / total_requests * 100) if total_requests > 0 else 0

        # Get top endpoints
        endpoint_counts = defaultdict(int)
        for request in recent_requests:
            endpoint = request.labels.get('endpoint', 'unknown')
            endpoint_counts[endpoint] += 1

        top_endpoints = sorted(endpoint_counts.items(), key=lambda x: x[1], reverse=True)[:10]

        return {
            'total_requests_1h': total_requests,
            'error_rate_1h': error_rate,
            'performance_summary': duration_summary,
            'top_endpoints': top_endpoints,
            'tracked_endpoints': len(self.endpoints),
            'status_breakdown': self._get_status_breakdown(recent_requests)
        }

    def _get_status_breakdown(self, requests: List[ExpertMetric]) -> Dict[str, int]:
        """Get breakdown of status codes"""
        status_counts = defaultdict(int)
        for request in requests:
            status = request.labels.get('status', '200')
            status_family = f"{status[0]}xx"
            status_counts[status_family] += 1
        return dict(status_counts)

    async def initialize(self):
        """Initialize the API monitoring system"""
        logger.info(" MEDUSA VAULT: 🔧 Initializing ExpertAPIMonitor...")
        try:
            # Initialize API tracking directories
            api_dir = Path("logs/api")
            api_dir.mkdir(parents=True, exist_ok=True)

            # Setup default endpoint monitoring
            default_endpoints = [
                "/api/predictions",
                "/api/games",
                "/api/players",
                "/api/stats",
                "/api/health"
            ]

            for endpoint in default_endpoints:
                self.endpoints.add(endpoint)

            logger.info(" MEDUSA VAULT: ExpertAPIMonitor initialized successfully")
            return True

        except Exception as e:
            logger.error(f" TITAN PROCESSING FAILED: initialize ExpertAPIMonitor: {e}")
            return False

    async def shutdown(self):
        """Gracefully shutdown API monitor, flush API stats if needed."""
        logger.info(" MEDUSA VAULT: Shutting down ExpertAPIMonitor")
        # Add logic to flush or close resources if needed
        pass


class ExpertQuantumMonitor:
    """Expert quantum/prophecy observability with Oracle Focus integration"""

    def __init__(self, config: ExpertMonitoringConfig, metrics_collector: ExpertMetricsCollector,
                 alert_manager: ExpertAlertManager):
        self.config = config
        self.metrics = metrics_collector
        self.alerts = alert_manager
        self.quantum_state: Dict[str, Any] = {}
        self.prophecy_accuracy: deque = deque(maxlen=1000)

        logger.info(" MEDUSA VAULT: ExpertQuantumMonitor initialized")

    @oracle_focus
    def record_quantum_coherence(self, component: str, coherence: float,
                                 entanglement_strength: Optional[float] = None):
        """Record quantum system coherence"""
        timestamp = datetime.now()

        self.metrics.record_metric(ExpertMetric(
            name="quantum_coherence",
            value=coherence,
            timestamp=timestamp,
            labels={"component": component},
            component="quantum",
            level=MonitoringLevel.QUANTUM
        ))

        if entanglement_strength is not None:
            self.metrics.record_metric(ExpertMetric(
                name="quantum_entanglement",
                value=entanglement_strength,
                timestamp=timestamp,
                labels={"component": component},
                component="quantum",
                level=MonitoringLevel.QUANTUM
            ))

        # Update quantum state
        self.quantum_state[component] = {
            'coherence': coherence,
            'entanglement': entanglement_strength,
            'last_update': timestamp
        }

        # Check for quantum anomalies
        if coherence < 0.5: # Low coherence threshold
            asyncio.create_task(self.alerts.create_alert(
                severity=AlertSeverity.HIGH,
                title="Quantum Coherence Anomaly",
                description=f"Low quantum coherence detected in {component}: {coherence:.3f}", component="quantum",
                source="quantum_monitor",
                labels={"component": component},
                metadata={"coherence": coherence, "entanglement": entanglement_strength}
            ))

    @oracle_focus
    def record_prophecy(self, prophet: str, prophecy_type: str, confidence: float,
                        accuracy: Optional[float] = None, metadata: Optional[Dict[str, Any]] = None):
        """Record Oracle prophecy metrics"""
        timestamp = datetime.now()

        self.metrics.record_metric(ExpertMetric(
            name="oracle_prophecy_confidence",
            value=confidence,
            timestamp=timestamp,
            labels={"prophet": prophet, "type": prophecy_type},
            component="quantum",
            metadata=metadata or {}
        ))

        if accuracy is not None:
            self.prophecy_accuracy.append(accuracy)
            self.metrics.record_metric(ExpertMetric(
                name="oracle_prophecy_accuracy",
                value=accuracy,
                timestamp=timestamp,
                labels={"prophet": prophet, "type": prophecy_type},
                component="quantum",
                metadata=metadata or {}
            ))

    def get_quantum_summary(self) -> Dict[str, Any]:
        """Get comprehensive quantum monitoring summary"""
        # Get metrics for last hour
        coherence_summary = self.metrics.get_metric_summary("quantum_coherence", window_minutes=60)
        entanglement_summary = self.metrics.get_metric_summary("quantum_entanglement", window_minutes=60)
        
        # Get prophecy confidence and accuracy summaries
        confidence_summary = self.metrics.get_metric_summary("oracle_prophecy_confidence", window_minutes=60)
        accuracy_summary = self.metrics.get_metric_summary("oracle_prophecy_accuracy", window_minutes=60)

        return {
            'quantum_state_summary': {
                'tracked_components': len(self.quantum_state),
                'overall_coherence': coherence_summary,
                'overall_entanglement': entanglement_summary,
            },
            'prophecy_summary': {
                'confidence': confidence_summary,
                'accuracy': accuracy_summary,
                'total_prophecies_recorded': len(self.prophecy_accuracy)
            },
            'current_quantum_states': {
                comp: {
                    'coherence': state['coherence'],
                    'entanglement': state['entanglement'],
                    'last_update': state['last_update'].isoformat()
                }
                for comp, state in self.quantum_state.items()
            }
        }

    async def initialize(self):
        """Initialize the quantum monitoring system"""
        logger.info(" MEDUSA VAULT: 🔧 Initializing ExpertQuantumMonitor...")
        try:
            # Initialize quantum tracking directories
            quantum_dir = Path("logs/quantum")
            quantum_dir.mkdir(parents=True, exist_ok=True)
            logger.info(" MEDUSA VAULT: ExpertQuantumMonitor initialized successfully")
            return True
        except Exception as e:
            logger.error(f" TITAN PROCESSING FAILED: initialize ExpertQuantumMonitor: {e}")
            return False

    async def shutdown(self):
        """Gracefully shutdown quantum monitor, flush quantum stats if needed."""
        logger.info(" MEDUSA VAULT: Shutting down ExpertQuantumMonitor")
        # Add logic to flush or close resources if needed
        pass


# =============================================================================
# MAIN UNIFIED MONITORING SYSTEM
# =============================================================================

class ExpertUnifiedMonitor:
    """
    Expert Unified Monitoring System - Central orchestrator for all monitoring

    This is the main class that brings together all monitoring components into
    a single, comprehensive, production-ready monitoring system.
    """

    def __init__(self, config: Optional[ExpertMonitoringConfig] = None):
        self.config = config or ExpertMonitoringConfig()

        # Initialize core components
        self.metrics_collector = ExpertMetricsCollector(self.config)
        self.alert_manager = ExpertAlertManager(self.config)
        self.health_monitor = ExpertHealthMonitor(self.config, self.metrics_collector)

        # Initialize specialized monitors
        self.model_monitor = ExpertModelMonitor(self.config, self.metrics_collector, self.alert_manager)
        self.api_monitor = ExpertAPIMonitor(self.config, self.metrics_collector, self.alert_manager)
        self.quantum_monitor = ExpertQuantumMonitor(self.config, self.metrics_collector, self.alert_manager)

        # Prometheus server
        self._prometheus_server = None

        # Grafana integration
        self._grafana_client = None
        if GRAFANA_AVAILABLE and self.config.enable_grafana and self.config.grafana_token:
            try:
                self._grafana_client = GrafanaFace(
                    auth=self.config.grafana_token,
                    host=self.config.grafana_url.split("//")[1]
                )
            except Exception as e:
                logger.warning(f"Could not initialize Grafana client: {e}")

        # Setup default alert rules
        self._setup_default_alert_rules()

        # Register core components for health monitoring
        self._register_core_components()

        logger.info(" MEDUSA VAULT: ExpertUnifiedMonitor initialized successfully")

    def _setup_default_alert_rules(self):
        """Setup default alert rules"""

        def check_system_resources():
            cpu_metric = self.metrics_collector.get_metrics("system_cpu_usage")
            memory_metric = self.metrics_collector.get_metrics("system_memory_usage")

            if cpu_metric and cpu_metric[-1].value > 90:
                asyncio.create_task(self.alert_manager.create_alert(
                    severity=AlertSeverity.CRITICAL,
                    title="Critical CPU Usage",
                    description=f"CPU usage at {cpu_metric[-1].value:.1f}%",
                    component="system",
                    source="system_monitor"
                ))

            if memory_metric and memory_metric[-1].value > 95:
                asyncio.create_task(self.alert_manager.create_alert(
                    severity=AlertSeverity.CRITICAL,
                    title="Critical Memory Usage",
                    description=f"Memory usage at {memory_metric[-1].value:.1f}%",
                    component="system",
                    source="system_monitor"
                ))

        self.alert_manager.add_alert_rule(check_system_resources)

    def _register_core_components(self):
        """Register core components for health monitoring"""
        self.health_monitor.register_component("metrics_collector", version="1.0")
        self.health_monitor.register_component("alert_manager", version="1.0")
        self.health_monitor.register_component("model_monitor", version="1.0")
        self.health_monitor.register_component("api_monitor", version="1.0")
        self.health_monitor.register_component("quantum_monitor", version="1.0")

    @oracle_focus
    async def start_monitoring(self):
        """Start all monitoring components"""
        logger.info(" MEDUSA VAULT: Starting Expert Unified Monitoring System...")

        # Initialize all specialized monitors first
        await self.metrics_collector.initialize()
        await self.alert_manager.initialize()
        await self.health_monitor.initialize() # Health monitor depends on metrics_collector
        await self.model_monitor.initialize()
        await self.api_monitor.initialize()
        await self.quantum_monitor.initialize()

        # Start health monitoring loop
        await self.health_monitor.start_monitoring()

        # Start Prometheus server
        if PROMETHEUS_AVAILABLE and self.config.enable_prometheus:
            try:
                self._prometheus_server = start_http_server(
                    self.config.prometheus_port,
                    addr=self.config.prometheus_host,
                    registry=self.metrics_collector._registry
                )
                logger.info(f" Prometheus server started on {self.config.prometheus_host}:{self.config.prometheus_port}")
            except Exception as e:
                logger.error(f" TITAN PROCESSING FAILED: start Prometheus server: {e}")

        # Setup Grafana dashboards
        if self._grafana_client:
            await self._setup_grafana_dashboards()

        logger.info(" MEDUSA VAULT: Expert Unified Monitoring System started successfully")

    async def stop_monitoring(self):
        """Stop all monitoring components and gracefully shutdown all subcomponents."""
        logger.info(" MEDUSA VAULT: Stopping Expert Unified Monitoring System...")
        await self.health_monitor.shutdown()
        await self.metrics_collector.shutdown()
        await self.alert_manager.shutdown()
        await self.model_monitor.shutdown()
        await self.api_monitor.shutdown()
        await self.quantum_monitor.shutdown()
        logger.info(" MEDUSA VAULT: Expert Unified Monitoring System stopped and all resources released")

    async def _setup_grafana_dashboards(self):
        """Setup Grafana dashboards"""
        try:
            # Main system dashboard
            dashboard_config = {
                "dashboard": {
                    "id": None,
                    "title": "HYPER MEDUSA NEURAL VAULT - Expert Monitoring",
                    "panels": [
                        {
                            "id": 1,
                            "title": "System CPU Usage",
                            "type": "stat",
                            "targets": [{"expr": "nba_book_system_cpu_usage"}]
                        },
                        {
                            "id": 2,
                            "title": "System Memory Usage",
                            "type": "stat",
                            "targets": [{"expr": "nba_book_system_memory_usage"}]
                        },
                        {
                            "id": 3,
                            "title": "API Request Rate",
                            "type": "graph",
                            "targets": [{"expr": "rate(nba_book_api_request_total[5m])"}]
                        }
                    ]
                }
            }
            # Create dashboard (simplified - would need full Grafana API integration)
            logger.info(" MEDUSA VAULT: Grafana dashboards would be created here")

        except Exception as e:
            logger.error(f" TITAN PROCESSING FAILED: setup Grafana dashboards: {e}")

    def get_comprehensive_status(self) -> Dict[str, Any]:
        """Get comprehensive monitoring status"""
        health_status = self.health_monitor.get_overall_health()
        alert_summary = self.alert_manager.get_alert_summary()
        api_summary = self.api_monitor.get_api_summary()
        quantum_summary = self.quantum_monitor.get_quantum_summary()

        # Handle monitoring level (could be enum or string)
        monitoring_level_value = (
            self.config.monitoring_level.value
            if hasattr(self.config.monitoring_level, 'value')
            else str(self.config.monitoring_level)
        )

        return {
            'timestamp': datetime.now().isoformat(),
            'monitoring_level': monitoring_level_value,
            'overall_health': health_status,
            'alerts': alert_summary,
            'api_performance': api_summary,
            'quantum_status': quantum_summary,
            'system_info': {
                'prometheus_enabled': PROMETHEUS_AVAILABLE and self.config.enable_prometheus,
                'grafana_enabled': GRAFANA_AVAILABLE and self.config.enable_grafana,
                'oracle_integration': ORACLE_AVAILABLE and self.config.oracle_focus_enabled,
                'config': {
                    'monitoring_level': monitoring_level_value,
                    'health_check_interval': self.config.health_check_interval,
                    'metrics_retention_days': self.config.metrics_retention_days
                }
            }
        }

    def export_prometheus_metrics(self) -> bytes:
        """Export Prometheus metrics"""
        return self.metrics_collector.export_prometheus_metrics()

    async def export_metrics_json(self, start_time: Optional[datetime] = None,
                                  end_time: Optional[datetime] = None) -> Dict[str, Any]:
        """Export metrics in JSON format"""
        if not start_time:
            start_time = datetime.now() - timedelta(hours=24)
        if not end_time:
            end_time = datetime.now()

        export_data = {
            'export_info': {
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'exported_at': datetime.now().isoformat(),
                'monitoring_level': (
                    self.config.monitoring_level.value
                    if hasattr(self.config.monitoring_level, 'value')
                    else str(self.config.monitoring_level)
                )
            },
            'metrics': {},
            'alerts': [],
            'health': self.get_comprehensive_status()
        }

        # Export metrics
        for metric_name in self.metrics_collector.metrics.keys():
            metrics = self.metrics_collector.get_metrics(metric_name, since=start_time)
            metrics = [m for m in metrics if m.timestamp <= end_time]

            if metrics:
                export_data['metrics'][metric_name] = [
                    {
                        'timestamp': m.timestamp.isoformat(),
                        'value': m.value,
                        'labels': m.labels,
                        'component': m.component,
                        'metadata': m.metadata,
                        'quantum_signature': m.quantum_signature
                    }
                    for m in metrics
                ]

        # Export alerts
        for alert in self.alert_manager.alerts:
            if start_time <= alert.timestamp <= end_time:
                alert_dict = asdict(alert)
                alert_dict['timestamp'] = alert.timestamp.isoformat()
                alert_dict['severity'] = alert.severity.value
                export_data['alerts'].append(alert_dict)

        return export_data

    # Convenience methods for easy integration
    def record_api_request(self, endpoint: str, method: str, status_code: int, duration: float):
        """Convenience method for API monitoring"""
        self.api_monitor.record_request(endpoint, method, status_code, duration)

    def record_model_prediction(self, model_name: str, duration: float, accuracy: Optional[float] = None):
        """Convenience method for model monitoring"""
        self.model_monitor.record_prediction(model_name, duration, accuracy)

    def record_quantum_event(self, component: str, coherence: float):
        """Convenience method for quantum monitoring"""
        self.quantum_monitor.record_quantum_coherence(component, coherence)

    async def create_alert(self, severity: str, title: str, description: str, component: str = "system"):
        """Convenience method for creating alerts"""
        severity_enum = AlertSeverity(severity.lower())
        await self.alert_manager.create_alert(severity_enum, title, description, component, "manual")


# =============================================================================
# GLOBAL INSTANCE & FACTORY FUNCTIONS
# =============================================================================

_global_monitor: Optional[ExpertUnifiedMonitor] = None
_monitor_lock = threading.Lock()


def get_expert_monitor(config: Optional[ExpertMonitoringConfig] = None) -> ExpertUnifiedMonitor:
    """Get or create the global expert monitoring instance"""
    global _global_monitor

    with _monitor_lock:
        if _global_monitor is None:
            _global_monitor = ExpertUnifiedMonitor(config)
        return _global_monitor


async def initialize_expert_monitoring(config: Optional[ExpertMonitoringConfig] = None) -> ExpertUnifiedMonitor:
    """Initialize and start the expert monitoring system"""
    monitor = get_expert_monitor(config)
    await monitor.start_monitoring()
    return monitor


def create_monitoring_config(**kwargs) -> ExpertMonitoringConfig:
    """Create a monitoring configuration with custom settings"""
    return ExpertMonitoringConfig(**kwargs)


# =============================================================================
# DECORATORS & UTILITIES
# =============================================================================

def monitor_performance(metric_name: Optional[str] = None, component: str = "application"):
    """Decorator to automatically monitor function performance"""
    def decorator(func):
        async def async_wrapper(*args, **kwargs):
            monitor = get_expert_monitor()
            start_time = time.time()

            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time

                monitor.metrics_collector.record_metric(ExpertMetric(
                    name=metric_name or f"function_{func.__name__}_duration",
                    value=duration,
                    timestamp=datetime.now(),
                    labels={"function": func.__name__, "status": "success"},
                    component=component
                ))

                return result
            except Exception as e:
                duration = time.time() - start_time

                monitor.metrics_collector.record_metric(ExpertMetric(
                    name=metric_name or f"function_{func.__name__}_duration",
                    value=duration,
                    timestamp=datetime.now(),
                    labels={"function": func.__name__, "status": "error"},
                    component=component,
                    metadata={"error": str(e)}
                ))

                await monitor.alert_manager.create_alert(
                    severity=AlertSeverity.MEDIUM,
                    title=f"Function Error: {func.__name__}",
                    description=f"Function {func.__name__} failed: {str(e)}",
                    component=component,
                    source="performance_monitor",
                    metadata={"function": func.__name__, "error": str(e), "duration": duration}
                )

                raise

        def sync_wrapper(*args, **kwargs):
            monitor = get_expert_monitor()
            start_time = time.time()

            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time

                monitor.metrics_collector.record_metric(ExpertMetric(
                    name=metric_name or f"function_{func.__name__}_duration",
                    value=duration,
                    timestamp=datetime.now(),
                    labels={"function": func.__name__, "status": "success"},
                    component=component
                ))

                return result
            except Exception as e:
                duration = time.time() - start_time

                monitor.metrics_collector.record_metric(ExpertMetric(
                    name=metric_name or f"function_{func.__name__}_duration",
                    value=duration,
                    timestamp=datetime.now(),
                    labels={"function": func.__name__, "status": "error"},
                    component=component,
                    metadata={"error": str(e)}
                ))

                # Create alert task for sync functions
                asyncio.create_task(monitor.alert_manager.create_alert(
                    severity=AlertSeverity.MEDIUM,
                    title=f"Function Error: {func.__name__}",
                    description=f"Function {func.__name__} failed: {str(e)}",
                    component=component,
                    source="performance_monitor",
                    metadata={"function": func.__name__, "error": str(e), "duration": duration}
                ))

                raise

        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator

# Grafana integration helper

def push_grafana_annotation(message: str, dashboard_uid: str = None, panel_id: int = None, tags: list = None, config: 'ExpertMonitoringConfig' = None):
    """
    Pushes an annotation to Grafana using the HTTP API.
    Requires: config.grafana_url, config.grafana_token
    """
    if not config or not config.grafana_url or not config.grafana_token:
        logger.warning("Grafana config missing for annotation push.")
        return False
    url = f"{config.grafana_url.rstrip('/')}/api/annotations"
    headers = {
        "Authorization": f"Bearer {config.grafana_token}",
        "Content-Type": "application/json"
    }
    payload = {
        "text": message,
        "tags": tags or ["medusa", "automated"],
    }
    if dashboard_uid:
        payload["dashboardUID"] = dashboard_uid
    if panel_id:
        payload["panelId"] = panel_id
    try:
        response = requests.post(url, headers=headers, json=payload, timeout=5)
        response.raise_for_status()
        logger.info(f"Grafana annotation pushed: {payload}")
        return True
    except Exception as e:
        logger.warning(f"Failed to push Grafana annotation: {e}")
        return False
