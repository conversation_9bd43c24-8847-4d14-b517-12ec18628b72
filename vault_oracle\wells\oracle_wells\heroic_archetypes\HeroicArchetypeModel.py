#!/usr/bin/env python3
# DIGITAL FINGERPRINT: UUID=9f2a3b4c-5d6e-7f8a-9b0c-1d2e3f4a5b6c | DATE=2025-06-26
"""
(C) 2025 Hyper Medusa Neural Vault, All Rights Reserved.
PROPRIETARY & CONFIDENTIAL — UNAUTHORIZED USE, COPYING, OR DISTRIBUTION IS STRICTLY PROHIBITED.

This file contains proprietary algorithms, business logic, and intellectual property of Hyper Medusa Neural Vault.
Use, reproduction, or disclosure by any means without explicit written permission is a violation of applicable law.
"""

"""
HYPER MEDUSA NEURAL VAULT - Heroic Archetype Model Business Value Documentation
===============================================================================

HeroicArchetypeModel.py
-----------------------
Advanced mythological archetype classification for basketball players with expert clustering, feature engineering, and real-time analysis.

Business Value:
- Proprietary archetype modeling: Multi-dimensional, mythological, and expert clustering.
- Competitive Edge: Enables unique player insights, team chemistry, and performance prediction.
- Integration: Connects with Oracle Focus, DataTitan, and simulation modules for holistic analytics.
- Explainability: Provides confidence scoring, validation, and production-ready reporting.
- Extensibility: Designed for plugin analytics, new archetypes, and custom endpoints.

For further details, see module-level docstrings and architecture documentation.
"""

"""
Expert Heroic Archetype Model - Advanced Basketball Player Classification Engine
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
Expert Functions:
- Advanced mythological archetype classification for basketball players
- Multi-dimensional feature engineering and analysis
- Expert clustering integration with quality validation
- Oracle Focus performance monitoring and basketball intelligence
- Production-ready async/sync compatibility for real-time analysis
- Comprehensive archetype confidence scoring and validation
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
"""


import asyncio
import logging
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union, Any, Protocol
import warnings
import sys
import os
import numpy as np
import pandas as pd
from pydantic import BaseModel, Field, SecretStr, ValidationError
from pydantic_settings import BaseSettings, SettingsConfigDict
from vault_oracle.core.oracle_focus import oracle_focus, ExpertOracleFocus
from vault_oracle.core.cosmic_exceptions import BaseCosmicException, PredictionSystemFailure, ClusterError
from vault_oracle.interfaces.expert_messaging_orchestrator import ExpertMessagingOrchestrator
from vault_oracle.wells.oracle_wells.data_titans.OracleClustering import ExpertOracleClustering, ClusteringConfig, ClusteringResult, ClusteringQuality, ClusteringMethod, DimensionalityMethod
from sklearn.preprocessing import StandardScaler
from vault_oracle.wells.oracle_wells.data_titans.ArchetypeAssigner import ExpertArchetypeAssigner, EXPERT_ARCHETYPE_MAP
from src.features.feature_alchemist import generate_features



try:
    pass
except ImportError as e:
    raise ImportError(
        f"Expert Heroic Archetype Model requires numpy, pandas, and pydantic: {e}"
    )


# --- Configure Logger ---
logger = logging.getLogger(__name__)
if not logger.handlers:
    logging.basicConfig(
        level=logging.INFO,
        format="𓄿 %(asctime)s 𓃬 %(levelname)s 𓄢 %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
        handlers=[logging.StreamHandler(sys.stdout)],
    )
    logger = logging.getLogger(__name__)
    logger.info(" MEDUSA VAULT: HeroicArchetypeModel logger initialized.")
# --- End Logger Configuration ---

# Add project root to sys.path for internal imports
# Adjust based on your actual project structure.
# Assuming this file is at vault_oracle/wells/oracle_wells/data_titans/HeroicArchetypeModel.py
# and project root is 4 levels up.
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))


# --- MOCK/PLACEHOLDER IMPORTS ---
# These are necessary for HeroicArchetypeModel to run independently for testing.
# In a full project, these would be actual imports.

# Mock oracle_focus decorator
try:
    logger.info(" MEDUSA VAULT: Successfully imported real oracle_focus for HeroicArchetypeModel.")
except ImportError:
    logger.warning(" Could not import oracle_focus. Using mock decorator and class.")
    def oracle_focus(func):
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)
        return wrapper
    class ExpertOracleFocus: # Mock class
        def __init__(self, func): self.func = func
        def __call__(self, *args, **kwargs): return self.func(*args, **kwargs)


# Mock cosmic_exceptions
try:
    logger.info(" MEDUSA VAULT: Cosmic exceptions available for HeroicArchetypeModel.")
except ImportError as e:
    logger.warning(f" Cosmic exceptions not available: {e}. Using mock classes.")
    class BaseCosmicException(Exception): pass
    class PredictionSystemFailure(BaseCosmicException): pass
    class ClusterError(BaseCosmicException): pass


# Mock ExpertMessagingOrchestrator
try:
    logger.info(" MEDUSA VAULT: ExpertMessagingOrchestrator available for HeroicArchetypeModel.")
except ImportError as e:
    logger.warning(f" ExpertMessagingOrchestrator not available: {e}. Using mock.")
    class ExpertMessagingOrchestrator:
        async def send_basketball_alert(self, alert_type: str, message: str, context: Dict[str, Any]):
            logger.info(f"MOCK Expert Messaging Alert ({alert_type}): {message} - Context: {context}")
            await asyncio.sleep(0.01) # Simulate async operation

# Mock ExpertOracleClustering (from oracle_engine_file content)
try:
    # This import should point to your ExpertOracleClustering if it's in a different file
    logger.info(" MEDUSA VAULT: ExpertOracleClustering available for HeroicArchetypeModel.")
except ImportError as e:
    logger.warning(f" ExpertOracleClustering not available: {e}. Using mock.")
    # Re-define mock ExpertOracleClustering if the real import fails
    class ClusteringMethod(Enum):
        GAUSSIAN_MIXTURE = "gaussian_mixture"
        KMEANS = "kmeans"
    class DimensionalityMethod(Enum):
        TSNE = "tsne"
        PCA = "pca"
    class ClusteringQuality(Enum):
        EXCELLENT = "excellent"
        GOOD = "good"
        ACCEPTABLE = "acceptable"
        POOR = "poor"

    @dataclass
    class ClusteringResult:
        cluster_probabilities: np.ndarray
        cluster_labels: np.ndarray
        embeddings: np.ndarray
        quality_metrics: Dict[str, float]
        n_clusters: int
        method_used: ClusteringMethod
        dimensionality_method: DimensionalityMethod
        quality_assessment: ClusteringQuality
        timestamp: datetime = field(default_factory=datetime.now)
        @property
        def is_high_quality(self) -> bool:
            return self.quality_assessment in [ClusteringQuality.EXCELLENT, ClusteringQuality.GOOD]

    @dataclass
    class ClusteringConfig:
        n_components: int = 3
        dimensionality_method: DimensionalityMethod = DimensionalityMethod.TSNE
        clustering_method: ClusteringMethod = ClusteringMethod.GAUSSIAN_MIXTURE
        tsne_perplexity: int = 30
        tsne_n_components: int = 2
        random_state: int = 42
        quality_threshold: float = 0.6
        auto_optimize: bool = True
        enable_scaling: bool = True

    class ExpertOracleClustering:
        def __init__(self, config: Optional[ClusteringConfig] = None):
            self.config = config or ClusteringConfig()
            self.logger = logging.getLogger(self.__class__.__name__)
            self.logger.info("MOCK ExpertOracleClustering initialized.")
            self._last_result: Optional[ClusteringResult] = None
            self._embeddings_cache: Optional[np.ndarray] = None
            self._tsne_model: Any = None
            self._gmm_model: Any = None
            self._scaler: Any = None
            if self.config.enable_scaling:
                self._scaler = StandardScaler()

            # Simple mock for TSNE and GMM fit/transform/predict methods
            # TODO: Replace with production implementation
            # TODO: Replace with production implementation
            class MockTSNE:
                def __init__(self, n_components, perplexity, random_state, init, learning_rate, n_jobs): pass
                def fit_transform(self, X):
                    return X[:, :min(X.shape[1], self.config.tsne_n_components)] + np.random.rand(X.shape[0], min(X.shape[1], self.config.tsne_n_components)) * 0.1
            
            # TODO: Replace with production implementation
            # TODO: Replace with production implementation
            class MockGMM:
                def __init__(self, n_components, random_state, covariance_type, max_iter): self.n_components = n_components
                def fit(self, X): pass
                def predict_proba(self, X):
                    probs = np.random.rand(X.shape[0], self.n_components)
                    return probs / probs.sum(axis=1, keepdims=True)
                def predict(self, X):
                    return np.random.randint(0, self.n_components, X.shape[0])
            
            self._tsne_model = MockTSNE(self.config.tsne_n_components, self.config.tsne_perplexity, self.config.random_state, "pca", "auto", -1)
            self._tsne_model.config = self.config
            self._gmm_model = MockGMM(self.config.n_components, self.config.random_state, 'full', 200)

        def _preprocess_features(self, features: pd.DataFrame) -> np.ndarray:
            features_clean = features.fillna(features.mean(numeric_only=True))
            feature_array = features_clean.values
            if self._scaler:
                # Handle case where scaler wasn't fitted yet (e.g., in _initialize_models)
                if feature_array.shape[0] > 0:
                    self._scaler.fit(feature_array) # Fit scaler here for mock
                    feature_array = self._scaler.transform(feature_array)
            return feature_array

        def divine_bloodlines(self, features: pd.DataFrame) -> ClusteringResult:
            self.logger.info("MOCK ExpertOracleClustering: Performing mock divine_bloodlines analysis.")
            if features.empty: raise ValueError("Empty features for mock clustering.")
            processed_features = self._preprocess_features(features)
            embeddings = self._tsne_model.fit_transform(processed_features)
            cluster_probs = self._gmm_model.predict_proba(embeddings)
            cluster_labels = self._gmm_model.predict(embeddings)
            quality_metrics = {
                'silhouette_score': np.random.uniform(0.4, 0.9),
                'calinski_harabasz_score': np.random.uniform(100, 1000),
                'aic': np.random.uniform(100, 500), 'bic': np.random.uniform(200, 600),
                'log_likelihood': np.random.uniform(-500, -100)
            }
            quality_assessment = ClusteringQuality.GOOD if quality_metrics['silhouette_score'] >= self.config.quality_threshold else ClusteringQuality.POOR
            result = ClusteringResult(
                cluster_probabilities=cluster_probs, cluster_labels=cluster_labels,
                embeddings=embeddings, quality_metrics=quality_metrics, n_clusters=self.config.n_components,
                method_used=self.config.clustering_method, dimensionality_method=self.config.dimensionality_method,
                quality_assessment=quality_assessment
            )
            self._last_result = result
            return result
        
        async def divine_bloodlines_async(self, features: pd.DataFrame) -> ClusteringResult:
            # Use a thread pool executor for mock async behavior
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, self.divine_bloodlines, features)

        def visualize_olympus(self, embeddings: Optional[np.ndarray] = None, result: Optional[ClusteringResult] = None, save_path: Optional[str] = None) -> Optional[str]:
            self.logger.info("MOCK ExpertOracleClustering: Mocking visualization.")
            if save_path:
                with open(save_path + ".txt", "w") as f: f.write("Mock visualization content.")
                return save_path + ".txt"
            return None
        def get_archetype_insights(self) -> Dict[str, Any]:
            self.logger.info("MOCK ExpertOracleClustering: Mocking archetype insights.")
            if self._last_result is None: return {"summary": "No data", "recommendations": []}
            return {
                "summary": {"n_clusters": self._last_result.n_clusters, "quality": self._last_result.quality_assessment.value},
                "cluster_distribution": {f"archetype_{i}": {"count": 10, "percentage": 20.0} for i in range(self._last_result.n_clusters)},
                "recommendations": ["Mock recommendation: adjust parameters."]
            }

# Mock ArchetypeAssigner
try:
    logger.info(" MEDUSA VAULT: ExpertArchetypeAssigner available for HeroicArchetypeModel.")
except ImportError as e:
    logger.warning(f" ExpertArchetypeAssigner not available: {e}. Using mock.")
    EXPERT_ARCHETYPE_MAP = {
        0: {"archetype": "Offensive Dynamo", "deity": "Zeus"},
        1: {"archetype": "Defensive Titan", "deity": "Hades"},
        2: {"archetype": "Versatile Demigod", "deity": "Hermes"},
        3: {"archetype": "Playmaking Oracle", "deity": "Apollo"},
        4: {"archetype": "Rebounding Colossus", "deity": "Atlas"},
        # Add more mappings as needed for your n_archetypes
    }
    class ExpertArchetypeAssigner:
        def __init__(self, messaging_orchestrator: Any = None): # Ensure it can be initialized without args
            self.messaging_orchestrator = messaging_orchestrator # Store it if provided
            logger.info("MOCK ExpertArchetypeAssigner initialized.")
        def assign_heroic_identity(self, cluster_probabilities: np.ndarray) -> Dict[str, Any]:
            cluster_id = np.argmax(cluster_probabilities[0]) # Get primary cluster
            return EXPERT_ARCHETYPE_MAP.get(cluster_id, {"archetype": f"Unknown Archetype {cluster_id}", "deity": "Unknown"})

# Mock feature_alchemist
try:
    logger.info(" MEDUSA VAULT: Feature Alchemist (generate_features) available.")
except ImportError as e:
    logger.warning(f" Feature Alchemist (generate_features) not available: {e}. Using mock function.")
    def generate_features(raw_data: Dict[str, Any]) -> pd.DataFrame:
        logger.warning(" TITAN WARNING: Using mock generate_features. Real feature engineering is required.")
        # Create a dummy DataFrame from raw_data for testing
        base_stats = raw_data.get('base_stats', {})
        tracking = raw_data.get('tracking', {})
        clutch = raw_data.get('clutch', {})
        advanced = raw_data.get('advanced', {})

        # Combine all dictionaries, filling missing with 0 or None
        combined_data = {**base_stats, **tracking, **clutch, **advanced}
        
        # Create a DataFrame from the combined data. Ensure all expected feature columns are present.
        # This mock needs to produce a DataFrame with the same columns that the real one would,
        # otherwise clustering might fail.
        mock_columns = [
            'points', 'assists', 'rebounds', 'steals', 'blocks',
            'field_goal_pct', 'three_point_pct', 'free_throw_pct',
            'speed', 'distance', 'touches', 'dribbles',
            'clutch_scoring_pct', 'game_winners', 'clutch_assists',
            'player_efficiency_rating', 'true_shooting_pct', 'usage_rate', 'box_plus_minus'
        ]
        
        # Create a dictionary with all mock columns, fill with existing data, then 0 for missing
        df_data = {col: combined_data.get(col, 0.0) for col in mock_columns}
        return pd.DataFrame([df_data])


# --- END MOCK/PLACEHOLDER IMPORTS ---


class ArchetypeConfidence(Enum):
    """Archetype assignment confidence levels."""
    LEGENDARY = "legendary" # 90%+ confidence
    HEROIC = "heroic" # 75-89% confidence
    WORTHY = "worthy" # 60-74% confidence
    UNCERTAIN = "uncertain" # 40-59% confidence
    UNCLEAR = "unclear" # <40% confidence


class ArchetypeCategory(Enum):
    """Basketball archetype categories."""
    OFFENSIVE = "offensive"
    DEFENSIVE = "defensive"
    PLAYMAKING = "playmaking"
    VERSATILE = "versatile"
    SPECIALIZED = "specialized"


@dataclass
class ArchetypeFeatures:
    """Comprehensive feature representation for archetype analysis."""
    raw_stats: Dict[str, float]
    engineered_features: pd.DataFrame
    feature_importance: Dict[str, float] = field(default_factory=dict)
    quality_score: float = 0.0
    completeness: float = 0.0
    
    @property
    def is_high_quality(self) -> bool:
        """Check if features meet expert quality standards."""
        return self.quality_score >= 0.7 and self.completeness >= 0.8


@dataclass
class ArchetypeResult:
    """Comprehensive archetype analysis result."""
    primary_archetype: str
    primary_confidence: float
    secondary_archetype: Optional[str] = None
    secondary_confidence: Optional[float] = None
    confidence_level: ArchetypeConfidence = ArchetypeConfidence.UNCERTAIN
    category: ArchetypeCategory = ArchetypeCategory.VERSATILE
    deity: Optional[str] = None
    cluster_probabilities: Optional[np.ndarray] = None
    feature_contributions: Dict[str, float] = field(default_factory=dict)
    analysis_metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    
    @property
    def is_confident(self) -> bool:
        """Check if the archetype assignment is confident."""
        return self.confidence_level in [ArchetypeConfidence.LEGENDARY, ArchetypeConfidence.HEROIC]
    
    @property
    def composite_confidence(self) -> float:
        """Calculate composite confidence score."""
        if self.secondary_confidence:
            return (self.primary_confidence * 0.7) + (self.secondary_confidence * 0.3)
        return self.primary_confidence


@dataclass
class ExpertArchetypeConfig:
    """Expert configuration for archetype modeling."""
    n_archetypes: int = 32
    confidence_threshold: float = 0.6
    clustering_config: Optional[ClusteringConfig] = None
    feature_selection_threshold: float = 0.05
    enable_secondary_archetypes: bool = True
    quality_threshold: float = 0.7
    auto_optimize: bool = True
    basketball_context_weight: float = 0.8


class ExpertHeroicArchetypeModel(BaseModel): # Inherit from BaseModel
    """
    Expert-level heroic archetype classification engine for basketball players.
    
    Features:
    - Advanced multi-dimensional feature engineering
    - Expert clustering integration with quality validation
    - Confidence-based archetype assignment with secondary classifications
    - Oracle Focus performance monitoring and basketball intelligence
    - Async/sync compatibility for production environments
    - Comprehensive quality assessment and optimization
    """
    config: ExpertArchetypeConfig = Field(default_factory=ExpertArchetypeConfig)
    # env_settings from ConfigValidator is not directly used in __init__ in the provided code
    # If it's truly needed here for config validation or secrets, it should be added as a Field.
    # For now, it's removed to avoid an unused variable warning if not provided.

    # Internal components, instantiated in __init__
    clustering_engine: ExpertOracleClustering = Field(init=False)
    messenger: ExpertMessagingOrchestrator = Field(init=False)
    _is_initialized: bool = False

    model_config = {
        "arbitrary_types_allowed": True # Allow non-Pydantic types like ExpertOracleClustering
    }

    @oracle_focus()
    def __init__(self, **data):
        """
        Initializes the HeroicArchetypeModel, setting up the clustering engine
        and messaging system based on provided configuration.
        """
        super().__init__(**data) # Call BaseModel's __init__

        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Initialize the messenger first, as ArchetypeAssigner might need it
        self.messenger = ExpertMessagingOrchestrator()

        # Initialize expert components
        self._initialize_components()
        
        self._last_result: Optional[ArchetypeResult] = None # Ensure this is initialized
        self._feature_cache: Dict[str, Any] = {} # Ensure this is initialized
        
        self.logger.info(f"ExpertHeroicArchetypeModel initialized with {self.config.n_archetypes} archetypes")
        self._is_initialized = True

    def _initialize_components(self) -> None:
        """Initialize expert clustering and archetype assignment components."""
        # Initialize clustering engine
        clustering_config = self.config.clustering_config or ClusteringConfig(
            n_components=self.config.n_archetypes,
            quality_threshold=self.config.quality_threshold,
            auto_optimize=self.config.auto_optimize,
            tsne_n_components=3 # Ensure this is set for visualization
        )
        self._clustering_engine = ExpertOracleClustering(clustering_config)
        
        # Initialize archetype assigner, passing the messenger
        self._archetype_assigner = ExpertArchetypeAssigner(messaging_orchestrator=self.messenger)
        
    @oracle_focus()
    def fit(self, raw_data: Dict[str, Any]) -> ArchetypeResult:
        """
        Perform expert archetype classification on player data.
        
        Args:
            raw_data: Raw basketball player data dictionary
        
        Returns:
            Comprehensive archetype analysis result
        
        Raises:
            PredictionSystemFailure: If archetype classification fails
        """
        try:
            if not self._is_initialized:
                raise PredictionSystemFailure("ExpertHeroicArchetypeModel is not initialized.")
            
            self.logger.info(f"🎭 Starting expert archetype analysis for player data")
            
            # Step 1: Advanced feature engineering
            features = self._engineer_features(raw_data)
            
            # Step 2: Quality validation
            if not features.is_high_quality:
                self.logger.warning(f" Feature quality below threshold: {features.quality_score:.3f}. Completeness: {features.completeness:.3f}")
            
            # Step 3: Expert clustering analysis
            # Ensure engineered_features is a DataFrame and not empty
            if features.engineered_features.empty:
                raise ClusterError("Engineered features DataFrame is empty, cannot perform clustering.")
            
            clustering_result = self._clustering_engine.divine_bloodlines(features.engineered_features)
            
            # Step 4: Archetype assignment with confidence scoring
            archetype_result = self._assign_archetype(clustering_result, features, raw_data)
            
            # Step 5: Quality enhancement and validation
            enhanced_result = self._enhance_result(archetype_result, features)
            
            # Cache and alert
            self._last_result = enhanced_result
            
            # Send expert alert for high-confidence assignments
            if enhanced_result.is_confident:
                # Fire-and-forget alert
                asyncio.create_task(self._send_archetype_alert(
                    "expert_archetype_assignment",
                    f"🎭 Expert archetype classified: {enhanced_result.primary_archetype} "
                    f"({enhanced_result.confidence_level.value})",
                    {
                        "archetype": enhanced_result.primary_archetype,
                        "confidence": enhanced_result.primary_confidence,
                        "category": enhanced_result.category.value
                    }
                ))
            
            self.logger.info(f" Archetype analysis complete: {enhanced_result.primary_archetype}")
            return enhanced_result
        
        except (PredictionSystemFailure, ClusterError) as e:
            error_msg = f"Expert archetype classification failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            asyncio.create_task(self._send_archetype_alert(
                "expert_archetype_error",
                f" {error_msg}",
                {"error": str(e), "player_data": str(raw_data)[:200]}
            ))
            raise PredictionSystemFailure(error_msg) from e
        except Exception as e:
            error_msg = f"Unexpected error during expert archetype classification: {str(e)}"
            self.logger.critical(error_msg, exc_info=True)
            asyncio.create_task(self._send_archetype_alert(
                "expert_archetype_error",
                f" {error_msg}",
                {"error": str(e), "player_data": str(raw_data)[:200]}
            ))
            raise PredictionSystemFailure(error_msg) from e

    @oracle_focus()
    async def fit_async(self, raw_data: Dict[str, Any]) -> ArchetypeResult:
        """
        Async version of archetype classification.
        
        Args:
            raw_data: Raw basketball player data
        
        Returns:
            Comprehensive archetype analysis result
        """
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.fit, raw_data)

    def _engineer_features(self, raw_data: Dict[str, Any]) -> ArchetypeFeatures:
        """Engineer comprehensive features from raw player data."""
        try:
            # Generate features using the existing feature alchemist
            engineered_df = generate_features(raw_data) # This function must return a pd.DataFrame
            
            # Calculate feature quality metrics
            quality_score = self._assess_feature_quality(engineered_df)
            completeness = self._calculate_completeness(raw_data, engineered_df)
            
            # Calculate feature importance (simplified)
            feature_importance = self._calculate_feature_importance(engineered_df)
            
            return ArchetypeFeatures(
                raw_stats=raw_data,
                engineered_features=engineered_df,
                feature_importance=feature_importance,
                quality_score=quality_score,
                completeness=completeness
            )
        
        except Exception as e:
            self.logger.error(f"Feature engineering failed: {e}", exc_info=True)
            # Return basic features with low quality score and empty engineered_features DataFrame
            # to prevent further errors downstream.
            basic_df_columns = ['points', 'rebounds', 'assists'] # Placeholder for expected columns
            basic_df = pd.DataFrame([raw_data.get('base_stats', {})], columns=basic_df_columns).fillna(0)
            
            return ArchetypeFeatures(
                raw_stats=raw_data,
                engineered_features=basic_df, # Provide an empty or minimal DataFrame
                quality_score=0.3,
                completeness=0.5,
                feature_importance={}
            )

    def _assess_feature_quality(self, features: pd.DataFrame) -> float:
        """Assess the quality of engineered features."""
        if features.empty:
            return 0.0
        
        # Check for missing values
        total_cells = features.shape[0] * features.shape[1]
        missing_ratio = features.isnull().sum().sum() / total_cells if total_cells > 0 else 1.0
        
        # Check for feature variance
        # Use numeric_only=True for mean/var to avoid warning on non-numeric columns
        numeric_features = features.select_dtypes(include=[np.number])
        if numeric_features.empty:
            variance_score = 0.0
        else:
            # Ensure there are non-zero variances to avoid division by zero or NaN
            variances = numeric_features.var()
            # If all variances are zero, mean variance is zero, otherwise calculate
            if variances.sum() == 0:
                variance_score = 0.0
            else:
                # Normalize mean variance (adjust '100' based on expected feature scale)
                variance_score = min(1.0, variances.mean() / (numeric_features.max().max() - numeric_features.min().min() + 1e-6) ) if not numeric_features.empty else 0.0
 
        # Composite quality score
        quality = (1.0 - missing_ratio) * 0.6 + variance_score * 0.4
        return min(1.0, max(0.0, quality))

    def _calculate_completeness(self, raw_data: Dict[str, Any], features: pd.DataFrame) -> float:
        """Calculate data completeness score."""
        expected_keys = ['base_stats', 'tracking', 'clutch', 'advanced'] # Added 'advanced'
        present_keys = sum(1 for key in expected_keys if key in raw_data)
        
        # Feature count score - scale this based on the number of features your `generate_features` *actually* produces
        expected_feature_count = 19 # Based on mock generate_features
        feature_count_score = min(1.0, len(features.columns) / expected_feature_count) 
        
        # Raw data completeness
        raw_completeness = present_keys / len(expected_keys)
        
        return (raw_completeness * 0.7) + (feature_count_score * 0.3)

    def _calculate_feature_importance(self, features: pd.DataFrame) -> Dict[str, float]:
        """Calculate simplified feature importance scores."""
        importance = {}
        
        numeric_cols = features.select_dtypes(include=[np.number]).columns
        if numeric_cols.empty:
            return {}

        for col in numeric_cols:
            # Simple variance-based importance
            var = features[col].var()
            importance[col] = float(var) if not pd.isna(var) else 0.0
        
        # Normalize to sum to 1
        total = sum(importance.values())
        if total > 0:
            importance = {k: v/total for k, v in importance.items()}
        
        return importance

    def _assign_archetype(self, clustering_result: ClusteringResult, features: ArchetypeFeatures, raw_data: Dict[str, Any]) -> ArchetypeResult:
        """Assign archetype based on clustering results and features."""
        # Ensure cluster_probabilities is not empty
        if clustering_result.cluster_probabilities is None or clustering_result.cluster_probabilities.size == 0:
            raise ClusterError("Clustering probabilities are empty, cannot assign archetype.")

        # Get primary cluster assignment
        # Assumes clustering_result.cluster_probabilities is 2D and contains probabilities for a single sample (player)
        primary_cluster = np.argmax(clustering_result.cluster_probabilities[0])
        primary_confidence = clustering_result.cluster_probabilities[0][primary_cluster]
        
        # Map cluster to archetype using expert archetype assigner
        # Ensure _archetype_assigner is initialized
        if self._archetype_assigner is None:
            raise PredictionSystemFailure("Archetype Assigner is not initialized.")
        
        archetype_assignment = self._archetype_assigner.assign_heroic_identity(
            clustering_result.cluster_probabilities
        )
        
        # Determine confidence level
        confidence_level = self._determine_confidence_level(primary_confidence)
        
        # Determine category based on features
        category = self._determine_category(features.raw_stats)
        
        # Find secondary archetype if enabled
        secondary_archetype = None
        secondary_confidence = None
        
        if self.config.enable_secondary_archetypes:
            sorted_probs_indices = np.argsort(clustering_result.cluster_probabilities[0])[::-1]
            if len(sorted_probs_indices) > 1:
                # The second highest probability and its corresponding cluster ID
                secondary_cluster_id = sorted_probs_indices[1]
                secondary_confidence = clustering_result.cluster_probabilities[0][secondary_cluster_id]
                
                # Check threshold for secondary archetype
                if secondary_confidence > self.config.confidence_threshold * 0.5: # Example threshold
                    secondary_archetype_info = EXPERT_ARCHETYPE_MAP.get(secondary_cluster_id)
                    if secondary_archetype_info:
                        secondary_archetype = secondary_archetype_info.get("archetype")
                    else:
                        secondary_archetype = f"Secondary_Archetype_{secondary_cluster_id}"
                else:
                    secondary_archetype = None
                    secondary_confidence = None
        
        return ArchetypeResult(
            primary_archetype=archetype_assignment.get('archetype', 'Unknown'),
            primary_confidence=float(primary_confidence), # Cast to float for Pydantic
            secondary_archetype=secondary_archetype,
            secondary_confidence=float(secondary_confidence) if secondary_confidence is not None else None,
            confidence_level=confidence_level,
            category=category,
            deity=archetype_assignment.get('deity'),
            cluster_probabilities=clustering_result.cluster_probabilities[0],
            feature_contributions=features.feature_importance,
            analysis_metadata={
                'clustering_quality': clustering_result.quality_assessment.value,
                'feature_quality': features.quality_score,
                'completeness': features.completeness,
                'n_archetypes_configured': self.config.n_archetypes,
                'clustering_method_used': clustering_result.method_used.value,
                'dimensionality_method_used': clustering_result.dimensionality_method.value
            }
        )

    def _determine_confidence_level(self, confidence: float) -> ArchetypeConfidence:
        """Determine confidence level based on probability score."""
        if confidence >= 0.9:
            return ArchetypeConfidence.LEGENDARY
        elif confidence >= 0.75:
            return ArchetypeConfidence.HEROIC
        elif confidence >= 0.6:
            return ArchetypeConfidence.WORTHY
        elif confidence >= 0.4:
            return ArchetypeConfidence.UNCERTAIN
        else:
            return ArchetypeConfidence.UNCLEAR

    def _determine_category(self, raw_stats: Dict[str, Any]) -> ArchetypeCategory:
        """Determine archetype category based on raw statistics."""
        # Simple heuristic-based categorization
        base_stats = raw_stats.get('base_stats', {})
        
        points = base_stats.get('points', 0)
        assists = base_stats.get('assists', 0)
        rebounds = base_stats.get('rebounds', 0)
        steals = base_stats.get('steals', 0)
        blocks = base_stats.get('blocks', 0)
        
        # Categorize based on dominant stat
        if points > 20:
            return ArchetypeCategory.OFFENSIVE
        elif assists > 7:
            return ArchetypeCategory.PLAYMAKING
        elif steals > 2 or blocks > 2:
            return ArchetypeCategory.DEFENSIVE
        elif all(stat > 5 for stat in [points, assists, rebounds]):
            return ArchetypeCategory.VERSATILE
        else:
            return ArchetypeCategory.SPECIALIZED

    def _enhance_result(self, result: ArchetypeResult, features: ArchetypeFeatures) -> ArchetypeResult:
        """Enhance the archetype result with additional analysis."""
        # Add feature quality to metadata
        result.analysis_metadata['feature_engineering_quality'] = features.quality_score
        result.analysis_metadata['top_features'] = list(
            sorted(features.feature_importance.items(), key=lambda x: x[1], reverse=True)[:5]
        )
        
        # Adjust confidence based on feature quality
        if features.quality_score < 0.5:
            result.primary_confidence *= 0.8 # Reduce confidence for poor quality features
            result.confidence_level = self._determine_confidence_level(result.primary_confidence)
        
        return result

    @oracle_focus()
    async def _send_archetype_alert(self, alert_type: str, message: str, context: Dict[str, Any]):
        """Helper to send alerts via the expert messenger."""
        try:
            if self.messenger: # Ensure messenger is initialized
                await self.messenger.send_basketball_alert(
                    alert_type=f"archetype_{alert_type}",
                    message=message,
                    context=context
                )
                self.logger.info(f" Archetype alert sent: {alert_type}")
            else:
                self.logger.warning(" Messaging orchestrator not initialized, cannot send alert.")
        except Exception as e:
            self.logger.error(f" Failed to send archetype alert {alert_type}: {e}", exc_info=True)

    # Legacy compatibility methods
    def predict(self, raw_data: Dict[str, Any]) -> ArchetypeResult:
        """Legacy compatibility: Alias for fit method."""
        warnings.warn("predict() is deprecated. Use fit() instead.", 
                      DeprecationWarning, stacklevel=2)
        return self.fit(raw_data)

    @property
    def n_components(self) -> int:
        """Legacy compatibility: Number of archetype components."""
        warnings.warn("Direct n_components access is deprecated. Use config.n_archetypes instead.", 
                      DeprecationWarning, stacklevel=2)
        return self.config.n_archetypes


# Legacy alias for backward compatibility
HeroicArchetypeModel = ExpertHeroicArchetypeModel


# Example Usage and Testing
if __name__ == "__main__":
    """
    Expert-level testing and demonstration of the ExpertHeroicArchetypeModel system.
    """
    
    # Configure logging
    logging.basicConfig(level=logging.INFO, 
                        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    async def run_demo():
        try:
            # Create expert configuration
            config = ExpertArchetypeConfig(
                n_archetypes=5, # Reduced for mock demonstration
                confidence_threshold=0.7,
                enable_secondary_archetypes=True,
                quality_threshold=0.6,
                auto_optimize=True
            )
            
            # Initialize expert archetype model
            expert_model = ExpertHeroicArchetypeModel(config=config)
            logger.info(" ExpertHeroicArchetypeModel instance created.")
            
            # Create comprehensive test data
            test_player_data = {
                "hero_id": "expert_test_player_001",
                "base_stats": {
                    "points": 27.5,
                    "rebounds": 8.2,
                    "assists": 6.8,
                    "steals": 1.8,
                    "blocks": 1.2,
                    "field_goal_pct": 0.485,
                    "three_point_pct": 0.375,
                    "free_throw_pct": 0.847
                },
                "tracking": {
                    "speed": 8.5,
                    "distance": 4.8,
                    "touches": 85,
                    "dribbles": 12.5
                },
                "clutch": {
                    "clutch_scoring_pct": 0.52,
                    "game_winners": 4,
                    "clutch_assists": 2.1
                },
                "advanced": {
                    "player_efficiency_rating": 24.8,
                    "true_shooting_pct": 0.595,
                    "usage_rate": 0.285,
                    "box_plus_minus": 6.2
                }
            }
            
            logger.info(f" Created comprehensive test player data")
            logger.info(f" Points: {test_player_data['base_stats']['points']}")
            logger.info(f" Assists: {test_player_data['base_stats']['assists']}")
            logger.info(f" Steals: {test_player_data['base_stats']['steals']}")
            
            # Perform expert archetype analysis (synchronous call, but calls async alerts)
            logger.info("\n🎭 Performing expert archetype analysis (sync call)...")
            archetype_result = expert_model.fit(test_player_data)
            
            logger.info(f" Archetype analysis complete!")
            logger.info(f" Primary Archetype: {archetype_result.primary_archetype}")
            logger.info(f" Confidence: {archetype_result.primary_confidence:.3f} ({archetype_result.confidence_level.value})")
            logger.info(f" Category: {archetype_result.category.value}")
            logger.info(f" Composite Confidence: {archetype_result.composite_confidence:.3f}")
            
            if archetype_result.deity:
                logger.info(f" Deity: {archetype_result.deity}")
            
            if archetype_result.secondary_archetype:
                logger.info(f" 🥈 Secondary: {archetype_result.secondary_archetype} ({archetype_result.secondary_confidence:.3f})")
            
            # Display analysis metadata
            logger.info(f"\n📋 Analysis Metadata:")
            for key, value in archetype_result.analysis_metadata.items():
                if isinstance(value, float):
                    logger.info(f" {key}: {value:.3f}")
                else:
                    logger.info(f" {key}: {value}")
            
            # Test async functionality
            logger.info(f"\n Testing async archetype analysis...")
            async_archetype, async_confidence = await expert_model.fit_async(test_player_data)
            logger.info(f" Async analysis complete: {async_archetype} ({async_confidence})")
            
            # Test legacy compatibility
            logger.info(f"\n Testing legacy compatibility...")
            try:
                with warnings.catch_warnings(record=True) as w:
                    warnings.simplefilter("always")
                    
                    # Test legacy methods
                    legacy_result = expert_model.predict(test_player_data)
                    legacy_n_components = expert_model.n_components
                    
                    logger.info(f" Legacy compatibility verified")
                    logger.info(f" 🎭 Legacy archetype: {legacy_result.primary_archetype}")
                    logger.info(f" 🔢 Legacy n_components: {legacy_n_components}")
                    
                    if w:
                        logger.info(f" {len(w)} deprecation warnings issued (expected)")
                    
            except Exception as legacy_error:
                logger.error(f" Legacy compatibility error: {legacy_error}", exc_info=True)
            
            # Performance summary
            logger.info(f"\n Performance Summary:")
            logger.info(f" Archetype Identified: {archetype_result.primary_archetype}")
            logger.info(f" Confidence Level: {archetype_result.confidence_level.value}")
            logger.info(f" Is Confident: {archetype_result.is_confident}")
            logger.info(f" Category: {archetype_result.category.value}")
            
            # Performance rating
            if archetype_result.primary_confidence >= 0.8:
                performance = "🌟 EXCELLENT"
            elif archetype_result.primary_confidence >= 0.6:
                performance = " GOOD"
            elif archetype_result.primary_confidence >= 0.4:
                performance = " ACCEPTABLE"
            else:
                performance = " NEEDS IMPROVEMENT"
            
            logger.info(f" Overall Performance: {performance}")
            
            logger.info(f"\n🌟 === Expert Heroic Archetype Model Demo Complete ===")
            logger.info(" All systems operational and production-ready!")
            
        except Exception as e:
            logger.critical(f" Expert archetype model demonstration failed: {e}", exc_info=True)
            sys.exit(1)

    # Run the async main function
    asyncio.run(run_demo())

