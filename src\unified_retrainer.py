import asyncio
import logging
from typing import Any, Dict, List, Optional, Callable
from datetime import datetime
from typing import TYPE_CHECKING
import pandas as pd
from src.cognitive_spires.prophecy_oracle import retrain_team_vs_team_model
from xgboost import XGBClassifier
from vault_oracle.TrainingGrounds.team_vs_team_training import AdvancedNBADataset, BettingMarketIntegration, ValidationFramework, NBADataLoader
from vault_oracle.observatory.expert_unified_monitor import ExpertUnifiedMonitor

"""
Unified Retrainer for HYPER MEDUSA NEURAL VAULT
Centralizes all retraining requests and orchestrates model retraining across the platform.
Integrates with feedback system, anomaly/drift detection, and all major learning engines.
"""


logger = logging.getLogger("unified_retrainer")

class UnifiedRetrainer:
    """
    Central orchestrator for all retraining requests.
    Handles retraining triggers, deduplication, and feedback integration.
    """
    def __init__(self):
        self.retrain_callbacks: List[Callable[[Dict[str, Any]], Any]] = []
        self.last_retrain_time: Optional[datetime] = None
        self.retrain_history: List[Dict[str, Any]] = []
        self.is_retraining: bool = False
        self.feedback_log: List[Dict[str, Any]] = []

    def register_retrain_callback(self, callback: Callable[[Dict[str, Any]], Any]):
        """Register a retrainable component (e.g., spire, engine, prophecy retrainer)."""
        self.retrain_callbacks.append(callback)
        logger.info(f"Registered retrain callback: {callback}")

    async def request_retraining(self, reason: str, context: Optional[Dict[str, Any]] = None):
        """Request retraining for all registered components."""
        if self.is_retraining:
            logger.warning("Retraining already in progress. Skipping duplicate request.")
            return
        self.is_retraining = True
        self.last_retrain_time = datetime.utcnow()
        retrain_context = context or {}
        retrain_context["reason"] = reason
        retrain_context["timestamp"] = self.last_retrain_time.isoformat()
        logger.info(f"[UnifiedRetrainer] Triggering retraining: {reason}")
        results = []
        for cb in self.retrain_callbacks:
            try:
                result = cb(retrain_context)
                if asyncio.iscoroutine(result):
                    result = await result
                results.append(result)
            except Exception as e:
                logger.error(f"Retrain callback failed: {e}")
                results.append({"error": str(e)})
        self.retrain_history.append({"context": retrain_context, "results": results})
        self.is_retraining = False
        return results

    def receive_feedback(self, feedback: Dict[str, Any]):
        """Receive feedback from any component (drift detector, cortex, spire, etc)."""
        self.feedback_log.append(feedback)
        logger.info(f"[UnifiedRetrainer] Feedback received: {feedback}")
        # Optionally trigger retraining based on feedback
        if feedback.get("trigger_retrain"):
            asyncio.create_task(self.request_retraining("feedback", context=feedback))

    def get_status(self) -> Dict[str, Any]:
        return {
            "last_retrain_time": self.last_retrain_time,
            "is_retraining": self.is_retraining,
            "retrain_history_count": len(self.retrain_history),
            "feedback_count": len(self.feedback_log),
        }

# --- Unified retrainable components registry ---
if TYPE_CHECKING:
    pass

# Example: Import retrain entrypoints from prophecy_oracle
try:
    # retrain_team_vs_team_model already imported above
    pass
except ImportError:
    retrain_team_vs_team_model = None

# Register retrainable components
unified_retrainer = UnifiedRetrainer()

def register_retrainable_components():
    # Register prophecy retrainer (team-vs-team)
    if retrain_team_vs_team_model:
        unified_retrainer.register_retrain_callback(lambda ctx: retrain_team_vs_team_model(
            seasons=ctx.get('seasons', [2022, 2023, 2024]),
            odds_data_path=ctx.get('odds_data_path', 'data/nba_odds.csv'),
            model_class=ctx.get('model_class', XGBClassifier)
        ))
    # Register other retrainable systems here as needed

register_retrainable_components()

# Example callable entry point for retraining (for CLI or script use)
def retrain_all(reason: str = 'manual', context: dict = None):
    """Trigger retraining for all registered components synchronously."""
    return asyncio.run(unified_retrainer.request_retraining(reason, context or {}))

# Health check utility
def check_system_health() -> dict:
    try:
        monitor = ExpertUnifiedMonitor()
        return monitor.get_health_metrics()
    except Exception as e:
        return {"status": "unhealthy", "error": str(e)}
