# src/cognitive_spires/oracle_vision.py

"""
OracleVision.py
---------------
The All-Seeing Eye of Hyper Medusa Neural Vault

Wields the following divine gifts:
 • Nike's Victory Sight (game outcome visions)
 • Prometheus' Foresight (player feat prophecies)
 • <PERSON><PERSON>' Gambit Wisdom (risk/reward calculus)
 • The Fates' Combo Weave (entwined destiny patterns)
 • Chronos' Drift Detection (temporal vision decay)
 • Athena's Alert Oracle (strategic warning system)
"""

import sys
import os
import time
from datetime import datetime
from typing import List, Optional, Dict, Any
import pandas as pd
from pydantic import BaseModel
import asyncio
import logging
from xgboost import XGBClassifier
import traceback

# Add PROJECT_ROOT and insert into sys.path early
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if PROJECT_ROOT not in sys.path:
    sys.path.insert(0, PROJECT_ROOT)

logger = logging.getLogger(__name__)
if not logger.handlers:
    logging.basicConfig(level=logging.INFO, format="%(asctime)s 👁️ %(levelname)s - %(message)s")

# Assuming these are correctly located and importable within src or other paths
try:
    from src.mnemosyne_archive.mnemosyne_archive_keeper import MnemosyneArchiveKeeper as DBManager
    from vault_oracle.core.oracle_focus import oracle_focus
    from vault_oracle.analysis.unified_temporal_anomaly import unified_temporal_anomaly_detector
    from vault_oracle.interfaces.expert_messaging_orchestrator import ExpertMessagingOrchestrator
    from vault_oracle.observatory.expert_unified_monitor import ExpertUnifiedMonitor
    from vault_oracle.TrainingGrounds.team_vs_team_training import (
        AdvancedNBADataset,
        BettingMarketIntegration,
        ValidationFramework,
        NBADataLoader,
    )
    # Assuming cognitive spires are in a package
    from src.cognitive_spires.NikeVictoryOracle_Expert import NikeVictoryOracle_Expert as NikeVictoryOracle
    from src.cognitive_spires.ProphecyOrchestrator_Expert import ProphecyOrchestrator_Expert as HeroicProphecyEngine
    from src.cognitive_spires.FateWeaver_Expert import FateWeaver_Expert as FateWeaver
    from src.cognitive_spires.ChronosMonitor import ChronosMonitor
    from src.cognitive_spires.AthenaStrategyEngine_Expert import AthenaStrategyEngine_Expert as AthenaAlertSystem
    logger.info("MEDUSA VAULT: All OracleVision dependencies loaded successfully.")
except ImportError as e:
    logger.critical(f"TITAN PROCESSING FAILED: import required modules for OracleVision: {e}")
    raise

# Sacred Constants
PATH_TO_FATES_LOOM = "models/fates_lstm.pth"
HERMES_WEIGHTS = {"labor": 0.6, "rest": 0.4, "favor": 1.2, "fatigue_penalty": 0.9}
OLYMPIAN_THRESHOLDS = {"victory_certainty": 0.95, "heroic_omen": 4.5, "fate_shift": 0.15}

class DivineVision(BaseModel):
    """Pydantic model for a complete, structured prophecy."""
    titan_clash_id: str
    home_team: str
    away_team: str
    nike_sight: Dict[str, Any]
    promethean_visions: List[Dict[str, Any]]
    fate_weavings: List[Dict[str, Any]]
    athena_omens: List[Dict[str, Any]]
    chronos_pulse: Optional[float] = None
    vision_id: str
    generated_at: datetime

# --- Restored & Enhanced Helper Functions ---
def apply_olympian_adjustments(hero_visions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Applies Hermes' Gambit Wisdom to adjust prophecies based on player load and divine favor.
    This is a more complete implementation.
    """
    logger.info("Applying Olympian adjustments to hero visions...")
    adjusted_visions = []
    for vision in hero_visions:
        adjusted_vision = vision.copy()
        # Apply fatigue penalty for high minutes played
        if adjusted_vision.get("minutes_played", 0) > 38:
            adjusted_vision["prophecy_value"] *= HERMES_WEIGHTS["fatigue_penalty"]
        # Apply divine favor bonus
        if adjusted_vision.get("divine_favor", False):
            adjusted_vision["prophecy_value"] *= HERMES_WEIGHTS["favor"]
        adjusted_visions.append(adjusted_vision)
    return adjusted_visions

def detect_fate_drift(promethean_visions: List[Dict[str, Any]]):
    """Detects temporal anomalies in player performance prophecies."""
    if not promethean_visions or len(promethean_visions) < 5:
        logger.info("No sufficient data for fate drift detection.")
        return {'drift': False, 'drift_score': 0.0}
    
    # This assumes unified_temporal_anomaly_detector is a functional module
    drift_result = unified_temporal_anomaly_detector.detect_drift(promethean_visions)
    if drift_result.get('drift', False):
        logger.warning(f"Fate drift detected! Drift score: {drift_result.get('drift_score', 0.0):.3f}")
    return drift_result

def _persist_divine_vision(vision: DivineVision, db_manager: DBManager):
    """Persists the generated DivineVision into the Mnemosyne Archive."""
    logger.info(f"Persisting divine vision {vision.vision_id} to Mnemosyne Archive...")
    try:
        record = {
            "vision_id": vision.vision_id,
            "titan_clash_id": vision.titan_clash_id,
            "home_team": vision.home_team,
            "away_team": vision.away_team,
            "nike_sight_json": str(vision.nike_sight), # Store as JSON string
            "promethean_visions_json": str(vision.promethean_visions),
            "fate_weavings_json": str(vision.fate_weavings),
            "athena_omens_json": str(vision.athena_omens),
            "chronos_pulse": vision.chronos_pulse,
            "generated_at": vision.generated_at
        }
        db_manager.weave_fate_threads("divine_visions", record, conflict_essence=["vision_id"])
        logger.info(f"Successfully persisted vision {vision.vision_id}.")
    except Exception as e:
        logger.error(f"Failed to persist divine vision {vision.vision_id}: {e}", exc_info=True)


# --- Custom Exceptions ---
class ApollyonsFolly(Exception):
    """Represents a vision consumed by chaos."""
    pass

class CelestialInterruption(Exception):
    """Represents an interruption due to a celestial event."""
    pass

@oracle_focus
def divine_gaze(input_data: pd.DataFrame, mode: str = "chronicle") -> DivineVision:
    """
    The All-Seeing Eye's cosmic perception.
    Generates a DivineVision from input data (historical or snapshot).
    """
    logger.info(f"Initiating divine gaze in '{mode}' mode...")
    visions: Dict[str, Any] = {}
    sacred_scrolls = input_data.copy()

    # Instantiate the cognitive spires
    victory_oracle = NikeVictoryOracle.load_from_olympus()
    hero_prophecy_engine = HeroicProphecyEngine.awaken()
    fate_loom_instance_or_func = FateWeaver.thread_needle()
    athena_system = AthenaAlertSystem()

    game_id_val = input_data["titan_clash_id"].iloc[0] if not input_data.empty and "titan_clash_id" in input_data.columns else "unknown"
    home_team_val = input_data["home_team"].iloc[0] if not input_data.empty and "home_team" in input_data.columns else "unknown"
    away_team_val = input_data["away_team"].iloc[0] if not input_data.empty and "away_team" in input_data.columns else "unknown"

    try:
        # --- Nike's Victory Sight ---
        if victory_oracle:
            visions["nike_sight"] = victory_oracle.discern(sacred_scrolls)
            logger.info(f"Nike's sight discerned: {visions.get('nike_sight', 'failed')}")
        else:
            visions["nike_sight"] = {}
            logger.warning("TITAN WARNING: NikeVictoryOracle not available.")

        # --- Prometheus' Foresight ---
        if hero_prophecy_engine:
            hero_visions = hero_prophecy_engine.envision(sacred_scrolls)
            visions["promethean_visions"] = apply_olympian_adjustments(hero_visions)
            logger.info(f"Promethean visions generated for {len(visions.get('promethean_visions', []))} heroes.")
        else:
            visions["promethean_visions"] = []
            logger.warning("HeroicProphecyEngine not available.")

        # --- The Fates' Combo Weave ---
        if fate_loom_instance_or_func:
            if hasattr(fate_loom_instance_or_func, "entwine"):
                visions["fate_weavings"] = fate_loom_instance_or_func.entwine(visions["promethean_visions"])
            elif callable(fate_loom_instance_or_func):
                visions["fate_weavings"] = fate_loom_instance_or_func(visions["promethean_visions"])
            else:
                visions["fate_weavings"] = []
            logger.info(f"Fate weavings generated: {len(visions.get('fate_weavings', []))} combos.")
        else:
            visions["fate_weavings"] = []
            logger.warning("TITAN WARNING: FateWeaver not available.")

        # --- Athena's Alert Oracle ---
        if athena_system:
            visions["athena_omens"] = athena_system.scry(
                visions.get("nike_sight", {}),
                visions.get("promethean_visions", [])
            )
            logger.info(f"Athena's omens generated: {len(visions.get('athena_omens', []))} alerts.")
        else:
            visions["athena_omens"] = []
            logger.warning("TITAN WARNING: AthenaAlertSystem not available.")

        # --- Chronos' Drift Detection ---
        if mode == "living":
            visions["chronos_pulse"] = time.time()
            detect_fate_drift(visions["promethean_visions"])

        # --- Construct the final DivineVision object ---
        now = datetime.now()
        vision_id = f"vision_{game_id_val}_{int(now.timestamp())}"
        divine_vision_obj = DivineVision(
            titan_clash_id=game_id_val,
            home_team=home_team_val,
            away_team=away_team_val,
            nike_sight=visions.get("nike_sight", {}),
            promethean_visions=visions.get("promethean_visions", []),
            fate_weavings=visions.get("fate_weavings", []),
            athena_omens=visions.get("athena_omens", []),
            chronos_pulse=visions.get("chronos_pulse"),
            vision_id=vision_id,
            generated_at=now
        )
        logger.info(f"MEDUSA VAULT: Divine vision {vision_id} generated successfully.")
        return divine_vision_obj

    except (ApollyonsFolly, Exception) as e:
        logger.critical(f"Vision consumed by chaos or other error: {e}", exc_info=True)
        raise CelestialInterruption(e) from e

async def living_prophecy_weave(titan_clash_id: str) -> DivineVision:
    """Asynchronously generates a live prophecy for an active game."""
    logger.info(f"Initiating living prophecy weave for game: {titan_clash_id}")
    try:
        # Asynchronously gather visions from different oracles
        victory_sight_task = NikeVictoryOracle.live_gaze(titan_clash_id)
        hero_visions_task = HeroicProphecyEngine.scry_active_battle(titan_clash_id)
        
        victory_sight, hero_visions = await asyncio.gather(victory_sight_task, hero_visions_task)

        omens: List[Dict[str, Any]] = []
        for vision in hero_visions:
            certainty = vision.get("certainty", 0.0)
            if isinstance(certainty, (int, float)) and certainty >= OLYMPIAN_THRESHOLDS.get("victory_certainty", 0.95):
                omens.append({
                    "type": "divine_favor",
                    "hero": vision.get("player_name", "Unknown Hero"),
                    "message": f"{vision.get('stat_type', 'Certainty')} certainty reaches Olympian heights",
                })

        if ChronosMonitor:
            temporal_shifts = ChronosMonitor.detect_anomalies(titan_clash_id)
            omens.extend(temporal_shifts)
        else:
            logger.warning("ChronosMonitor not available.")

        fate_weavings_list: List[Dict[str, Any]] = FateWeaver.quickweave(hero_visions) if FateWeaver else []

        now = datetime.now()
        vision_id = f"live_vision_{titan_clash_id}_{int(now.timestamp())}"
        
        divine_vision_obj = DivineVision(
            titan_clash_id=titan_clash_id,
            home_team=victory_sight.get("home_team", "Unknown"),
            away_team=victory_sight.get("away_team", "Unknown"),
            nike_sight=victory_sight,
            promethean_visions=hero_visions,
            fate_weavings=fate_weavings_list,
            athena_omens=omens,
            vision_id=vision_id,
            generated_at=now
        )
        logger.info(f"Living prophecy weave complete for game {titan_clash_id}.")
        return divine_vision_obj

    except Exception as e:
        logger.error(f"An error occurred during living prophecy weaving for game {titan_clash_id}: {e}", exc_info=True)
        raise

class RealNBADataLoader(NBADataLoader):
    """A concrete implementation for loading NBA data from a local Parquet file."""
    def load_raw_data(self, seasons: List[int]) -> pd.DataFrame:
        path = "data/nba_team_matchups.parquet"
        logger.info(f"Loading raw data for seasons {seasons} from {path}...")
        if not os.path.exists(path):
            raise FileNotFoundError(f"Data file not found at {path}")
        df = pd.read_parquet(path)
        return df[df["season"].isin(seasons)]

    def get_live_data(self, titan_clash_id: str) -> Dict[str, Any]:
        logger.warning(f"Live data fetch not implemented for {titan_clash_id}. Returning empty dict.")
        return {}

def retrain_team_vs_team_model(seasons: List[int], odds_data_path: str, model_class: Any):
    """Full pipeline for retraining the main team vs. team prediction model."""
    logger.info(f"Starting retraining for seasons: {seasons}")
    data_loader = RealNBADataLoader()
    team_dataset = AdvancedNBADataset(seasons=seasons, data_loader=data_loader)
    X = team_dataset.get_feature_matrix()
    y = team_dataset.get_targets("win")

    if not os.path.exists(odds_data_path):
        raise FileNotFoundError(f"Odds data file not found: {odds_data_path}")
    odds_data = pd.read_csv(odds_data_path)
    betting_features = BettingMarketIntegration(odds_data)
    # This assumes a method to merge betting features with X
    X_full = pd.concat([X, betting_features.features], axis=1).dropna()
    y = y.loc[X_full.index] # Align target with features

    val_framework = ValidationFramework()
    logger.info("Starting cross-validation...")
    for fold, (train_idx, test_idx) in enumerate(val_framework.cross_validate(None, X_full, y, groups=team_dataset.base_df.loc[X_full.index, "season"])):
        logger.info(f"--- Fold {fold+1} ---")
        X_train, X_test = X_full.iloc[train_idx], X_full.iloc[test_idx]
        y_train, y_test = y.iloc[train_idx], y.iloc[test_idx]
        
        model = model_class()
        model.fit(X_train, y_train)
        y_pred_proba = model.predict_proba(X_test)[:, 1]
        
        metrics = val_framework.evaluate_model(y_test, y_pred_proba, task_type="classification")
        logger.info(f"Retrain split metrics: {metrics}")
    logger.info("MEDUSA VAULT: Team-vs-Team model retraining complete.")

def check_system_health() -> dict:
    """Performs a health check on all integrated systems."""
    monitor = ExpertUnifiedMonitor()
    return monitor.get_health_metrics()

class ProphecyOracle:
    """
    Main ProphecyOracle class that provides access to all prophecy functions.
    This class serves as the main interface for prophecy generation and management.
    """

    def __init__(self):
        """Initialize the ProphecyOracle."""
        self.logger = logging.getLogger(__name__)
        self.logger.info("🧠 MEDUSA VAULT: ProphecyOracle initialized")

    def divine_gaze(self, input_data, mode: str = "chronicle"):
        """Generate a divine vision from input data."""
        return divine_gaze(input_data, mode)

    async def living_prophecy_weave(self, titan_clash_id: str):
        """Generate a live prophecy for an active game."""
        return await living_prophecy_weave(titan_clash_id)

    def retrain_model(self, seasons, odds_data_path, model_class):
        """Retrain the team vs team model."""
        return retrain_team_vs_team_model(seasons, odds_data_path, model_class)

    def check_health(self):
        """Check system health."""
        return check_system_health()

if __name__ == "__main__":
    logger.info("="*50)
    logger.info("   ORACLE VISION - EXPERT SYSTEM DEMONSTRATION   ")
    logger.info("="*50)
    
    # --- Setup ---
    messaging = ExpertMessagingOrchestrator()
    db_config = {"vault_type": "sqlite", "vault_paths": {"ORACLE_MEMORY": "data/oracle_vision_demo.db"}}
    db_manager = DBManager(config=db_config)
    
    # --- Health Check & Alerting ---
    logger.info("\n--- 1. System Health Check ---")
    health_status = check_system_health()
    logger.info(f"System Health: {health_status}")
    messaging.send_alert(f"OracleVision demonstration started. Health: {health_status.get('status', 'UNKNOWN')}", "INFO")
    
    # --- Retraining Demonstration ---
    logger.info("\n--- 2. Model Retraining Pipeline ---")
    try:
        retrain_team_vs_team_model(
            seasons=[2022, 2023],
            odds_data_path="data/nba_odds.csv",
            model_class=XGBClassifier,
        )
    except FileNotFoundError as e:
        logger.warning(f"Skipping retraining demonstration: {e}")
    except Exception as e:
        logger.error(f"An error occurred during retraining: {e}", exc_info=True)

    # --- Divine Gaze (Historical) Demonstration ---
    logger.info("\n--- 3. Divine Gaze (Historical Chronicle) ---")
    try:
        # Create more realistic sample data
        sample_data = pd.DataFrame({
            "titan_clash_id": ["game1_2023"], "home_team": ["LAL"], "away_team": ["BOS"],
            "player_id": [201939], "minutes_played": [40], "divine_favor": [True]
        })
        vision = divine_gaze(sample_data, mode="chronicle")
        logger.info(f"Successfully generated a divine vision: {vision.vision_id}")
        logger.info(f"Nike's Sight (Victory Prob): {vision.nike_sight.get('victory_probability')}")
        
        # --- Persistence Demonstration ---
        logger.info("\n--- 4. Persisting Vision to Mnemosyne Archive ---")
        _persist_divine_vision(vision, db_manager)

    except Exception as e:
        logger.error(f"An error occurred during the divine_gaze demonstration: {e}", exc_info=True)
    
    # --- Living Prophecy (Async) Demonstration ---
    logger.info("\n--- 5. Living Prophecy Weave (Live Game) ---")
    try:
        # This would typically be run in an async context
        live_vision = asyncio.run(living_prophecy_weave("live_game_id_12345"))
        logger.info(f"Successfully generated a live vision: {live_vision.vision_id}")
    except Exception as e:
        logger.error(f"An error occurred during the living prophecy demonstration: {e}", exc_info=True)

    db_manager.close_memory_gates()
    logger.info("\nOracleVision main execution block finished.")
