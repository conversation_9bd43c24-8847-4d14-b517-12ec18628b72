import logging

try:
    from .ChronosFatigueOracle_Expert import ChronosFatigue<PERSON><PERSON>le_Expert as ChronosOracle
    from .NikeVictoryOracle_Expert import NikeVictory<PERSON><PERSON>le_Expert as <PERSON><PERSON><PERSON><PERSON>
    from .AthenaStrategyEngine_Expert import AthenaStrategyEngine_Expert as <PERSON><PERSON><PERSON><PERSON>
    from .MetisOracle_Expert import <PERSON>is<PERSON><PERSON>le_Expert as Metis<PERSON><PERSON>le
    from .AresDefenseOracle_Expert import AresDefenseOracle_Expert as AresOracle
    from .FateForge_Expert import FateForge_Expert as FateForge
    from src.weavers.FateWeaver_Expert import Fate<PERSON>eaver_Expert as FateWeaver
    from src.weavers.GorgonWeave_Expert import GorgonWeave_Expert as GorgonWeave
    from .HephaestusForge_Expert import HephaestusForge_Expert as HephaestusForge
    EXPERT_MODULES_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Expert modules not available: {e}")
    EXPERT_MODULES_AVAILABLE = False

# Additional expert modules (optional)
try:
    from src.weavers.HeroicDeedWeaver_Expert import HeroicDeedWeaver_Expert as HeroicDeedWeaver
    from .OlympianCouncil_Expert import OlympianCouncil_Expert as OlympianCouncil
    from .PrometheusRealm_Expert import PrometheusRealm_Expert as PrometheusRealm
    from .ProphecyOrchestrator_Expert import ProphecyOrchestrator_Expert as ProphecyOrchestrator
    from src.weavers.SerpentWeave_Expert import SerpentWeave_Expert as SerpentWeave
    from .CognitiveSpiresFactory_Expert import CognitiveSpiresFactory_Expert
    from .ChronosMonitor import ChronosMonitor
except ImportError:
    pass  # Optional modules

# src/cognitive_spires/__init__.py

#!/usr/bin/env python3
"""
__init__.py for src.cognitive_spires
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
This package contains the expert-level cognitive and prophetic spires of the Oracle.
All spires are now at production/expert level.
"""

# Configure logger for this package
logger = logging.getLogger(__name__)
if not logger.handlers:
 handler = logging.StreamHandler()
 formatter = logging.Formatter(
 "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
 datefmt="%Y-%m-%d %H:%M:%S",
 )
 handler.setFormatter(formatter)
 logger.addHandler(handler)
 logger.setLevel(logging.INFO)

# Import Expert-Level Cognitive Spires (Production Ready)
logger.info(" MEDUSA VAULT: Initializing Expert-Level Cognitive Spires...")

# Core Expert Spires (The Original Five)
try:
 logger.info(" MEDUSA VAULT: ChronosOracle (Expert) imported successfully")
except ImportError as e:
 logger.error(f" ChronosOracle Expert import failed: {e}")
 ChronosOracle = None

try:
 logger.info(" MEDUSA VAULT: NikeOracle (Expert) imported successfully")
except ImportError as e:
 logger.error(f" NikeOracle Expert import failed: {e}")
 NikeOracle = None

try:
 logger.info(" MEDUSA VAULT: AthenaOracle (Expert) imported successfully")
except ImportError as e:
 logger.error(f" AthenaOracle Expert import failed: {e}")
 AthenaOracle = None

try:
 logger.info(" MEDUSA VAULT: MetisOracle (Expert) imported successfully")
except ImportError as e:
 logger.error(f" MetisOracle Expert import failed: {e}")
 MetisOracle = None

try:
 logger.info(" MEDUSA VAULT: AresOracle (Expert) imported successfully")
except ImportError as e:
 logger.error(f" AresOracle Expert import failed: {e}")
 AresOracle = None

# Advanced Expert Spires
try:
 # Deprecated: FateForge is now part of UnifiedModelForge with FateArchetypeStrategy
 logger.info(" MEDUSA VAULT: FateForge (Expert) imported successfully")
except ImportError as e:
 logger.error(f" FateForge Expert import failed: {e}")
 FateForge = None

try:
 logger.info(" MEDUSA VAULT: FateWeaver (Expert) imported successfully")
except ImportError as e:
 logger.error(f" FateWeaver Expert import failed: {e}")
 FateWeaver = None

try:
 logger.info(" MEDUSA VAULT: GorgonWeave (Expert) imported successfully")
except ImportError as e:
 logger.error(f" GorgonWeave Expert import failed: {e}")
 GorgonWeave = None

# UNIFIED FORGE MIGRATION: Remove direct imports of DivineModelForge_Expert and HephaestusForge_Expert
"""
try:
 logger.info(" MEDUSA VAULT: HephaestusForge (Expert) imported successfully")
except ImportError as e:
 logger.error(f" HephaestusForge Expert import failed: {e}")
 HephaestusForge = None
"""

# try:
#  from .DivineModelForge_Expert import DivineModelForge_Expert as DivineModelForge
#  logger.info(" MEDUSA VAULT: DivineModelForge (Expert) imported successfully")
# except ImportError as e:
#  logger.error(f" DivineModelForge Expert import failed: {e}")
#  DivineModelForge = None

try:
 logger.info(" MEDUSA VAULT: HeroicDeedWeaver (Expert) imported successfully")
except ImportError as e:
 logger.error(f" HeroicDeedWeaver Expert import failed: {e}")
 HeroicDeedWeaver = None

try:
 logger.info(" MEDUSA VAULT: OlympianCouncil (Expert) imported successfully")
except ImportError as e:
 logger.error(f" OlympianCouncil Expert import failed: {e}")
 OlympianCouncil = None

try:
 logger.info(" MEDUSA VAULT: PrometheusRealm (Expert) imported successfully")
except ImportError as e:
 logger.error(f" PrometheusRealm Expert import failed: {e}")
 PrometheusRealm = None

try:
 logger.info(" MEDUSA VAULT: ProphecyOrchestrator (Expert) imported successfully")
except ImportError as e:
 logger.error(f" ProphecyOrchestrator Expert import failed: {e}")
 ProphecyOrchestrator = None

try:
 logger.info(" MEDUSA VAULT: SerpentWeave (Expert) imported successfully")
except ImportError as e:
 logger.error(f" SerpentWeave Expert import failed: {e}")
 SerpentWeave = None

# Import the CognitiveSpiresFactory_Expert
try:
 logger.info(" MEDUSA VAULT: CognitiveSpiresFactory_Expert imported successfully")
except ImportError as e:
 logger.error(f" CognitiveSpiresFactory_Expert import failed: {e}")
 CognitiveSpiresFactory_Expert = None

# Legacy components for backward compatibility (only ChronosMonitor retained)
try:
 logger.info(" MEDUSA VAULT: ChronosMonitor (Legacy) imported for backward compatibility")
except ImportError as e:
 logger.warning(f" ChronosMonitor import failed: {e}")
 ChronosMonitor = None

# Compatibility aliases
try:
    AthenaAlertSystem = AthenaOracle # Legacy alias
except NameError:
    AthenaAlertSystem = None  # Fallback if AthenaOracle not available

logger.info(" MEDUSA VAULT: Expert-Level Cognitive Spires initialization complete!")

# Count successfully imported expert spires
expert_spires = []
spire_names = ['ChronosOracle', 'NikeOracle', 'AthenaOracle', 'MetisOracle', 'AresOracle',
               'FateForge', 'FateWeaver', 'GorgonWeave',
               'HeroicDeedWeaver', 'OlympianCouncil', 'PrometheusRealm', 'ProphecyOrchestrator', 'SerpentWeave']

for spire_name in spire_names:
    if spire_name in globals():
        expert_spires.append(globals()[spire_name])

successful_imports = len(expert_spires)
logger.info(f" Successfully imported {successful_imports}/{len(spire_names)} expert spires")


# Define CosmicCollapse exception
class CosmicCollapse(Exception):
    """
    Custom exception for catastrophic failures within the Oracle system,
    indicating a complete breakdown or unrecoverable state.
    """

    def __init__(self, message="A catastrophic cosmic collapse has occurred."):
        self.message = message
        super().__init__(self.message)


# __all__ defines what symbols are exported when someone does 'from package import *'
__all__ = [
 # Core Expert Spires (The Original Five)
 "ChronosOracle",
 "NikeOracle", 
 "AthenaOracle",
 "MetisOracle",
 "AresOracle",
 
 # Advanced Expert Spires
 # "FateForge",  # Deprecated: Use UnifiedModelForge with FateArchetypeStrategy
 "FateWeaver",
 "GorgonWeave",
 # "HephaestusForge",  # Migrated to UnifiedModelForge
 # "DivineModelForge",  # Migrated to UnifiedModelForge
 "HeroicDeedWeaver",
 "OlympianCouncil",
 "PrometheusRealm",
 "ProphecyOrchestrator",
 "SerpentWeave",
 
 # Factory class
 "CognitiveSpiresFactory_Expert",
 
 # Legacy compatibility (minimal)
 "ChronosMonitor",
 "AthenaAlertSystem",
 
 # Exception classes
 "CosmicCollapse",
]
