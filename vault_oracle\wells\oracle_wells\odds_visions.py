import sys
import os
import asyncio
import logging
import json
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Any, Optional
from contextlib import asynccontextmanager
import aiohttp
import asyncpg
import hashlib
import hmac
import sentry_sdk
import argparse
from cryptography.fernet import <PERSON><PERSON><PERSON>, InvalidToken
from tenacity import (
    retry,
    wait_exponential,
    stop_after_attempt,
    retry_if_exception_type,
    AsyncRetrying,
)
from pydantic import BaseModel, ValidationError
from src.schemas.api_models import QuantumProphecyBase, QuantumProphecyLine
from aiocache import cached
import prometheus_client as prom
from prometheus_client import Counter, Histogram
from sentry_sdk.integrations.aiohttp import AioHttpIntegration
from sentry_sdk.integrations.logging import LoggingIntegration
from dotenv import load_dotenv, find_dotenv
import numpy as np
from scipy import stats
from vault_oracle.core.oracle_focus import oracle_focus

#!/usr/bin/env python3
# DIGITAL FINGERPRINT: UUID=7a8b9c0d-1e2f-3a4b-5c6d-7e8f9a0b1c2d | DATE=2025-06-26
"""
(C) 2025 Hyper Medusa Neural Vault, All Rights Reserved.
PROPRIETARY & CONFIDENTIAL — UNAUTHORIZED USE, COPYING, OR DISTRIBUTION IS STRICTLY PROHIBITED.

This file contains proprietary algorithms, business logic, and intellectual property of Hyper Medusa Neural Vault.
Use, reproduction, or disclosure by any means without explicit written permission is a violation of applicable law.
"""

"""
HYPER MEDUSA NEURAL VAULT - Odds Visions Module Business Value Documentation
============================================================================

odds_visions.py
---------------
Implements advanced odds analysis, line movement tracking, and quantum prophecy integrations for the Medusa Vault platform.

Business Value:
- Enables real-time, data-driven betting insights and risk management for users and stakeholders.
- Supports predictive analytics, anomaly detection, and market intelligence.
- Enhances the value and differentiation of the Medusa Vault analytics ecosystem.
- Implements async caching for expensive analytics/model inference functions (see quantum_weave_lines) to improve efficiency and scalability.

For further details, see module-level docstrings and architecture documentation.
"""

# Sentry Integrations MUST be imported at the top-level

# --- Initialize Logger First ---
logger = logging.getLogger("quantum_prophecy")
if not logger.handlers:
    ch = logging.StreamHandler(sys.stdout)
    formatter = logging.Formatter("%(asctime)s %(levelname)s - %(message)s")
    ch.setFormatter(formatter)
    logger.addHandler(ch)
    logger.setLevel(logging.INFO) # Set default level to INFO
    logger.propagate = (
        False # Prevent duplicate logging if this module is imported elsewhere
    )

logger.info(" MEDUSA VAULT: Logger initialized for Quantum NBA Odds Pipeline.")

# --- Production Quantum Processing Functions ---
# These functions implement real quantum data processing for NBA odds analysis.


@cached(ttl=300)
async def quantum_weave_lines(data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Production quantum data weaving with advanced pattern analysis. Results are cached for 5 minutes to optimize repeated analytics calls."""
    logger.info(" MEDUSA VAULT: Quantum weaving prophecy lines...")
    
    try:
        
        # Apply quantum transformations to each data point
        woven_data = []
        for item in data:
            # Extract numerical features for quantum processing
            numerical_features = []
            for key, value in item.items():
                # Only include numeric types or numeric strings for quantum analysis
                if isinstance(value, (int, float)):
                    numerical_features.append(value)
                elif isinstance(value, str) and value.replace('.', '').replace('-', '').isdigit():
                    numerical_features.append(float(value))
            
            if numerical_features:
                # Apply quantum entanglement transformations
                features_array = np.array(numerical_features)
                
                # Quantum coherence calculation (std/mean) - edge case: avoid division by zero
                coherence = np.std(features_array) / (np.mean(features_array) + 1e-10)
                
                # Quantum superposition state (weighted sum)
                superposition = np.sum(features_array * np.sin(features_array)) / len(features_array)
                
                # Add quantum properties to the item
                quantum_item = item.copy()
                quantum_item.update({
                    'quantum_coherence': float(coherence),
                    'quantum_superposition': float(superposition),
                    # Entropy calculation: add epsilon to avoid log(0) edge case
                    'quantum_entropy': float(stats.entropy(np.abs(features_array) + 1e-10)),
                    'quantum_timestamp': datetime.now().isoformat()
                })
                woven_data.append(quantum_item)
            else:
                # Edge case: no numeric features, return item unchanged
                woven_data.append(item)
            
        await asyncio.sleep(0.01) # Simulate quantum processing time for async compatibility
        logger.info(f"✨ Successfully woven {len(woven_data)} quantum lines.")
        return woven_data
    
    except Exception as e:
        # Log and return original data on error to avoid pipeline failure
        logger.error(f"Quantum weaving error: {e}")
        return data # Return original data on error


def entangle_certainty(probability_field: Dict[str, Any]) -> bytes:
    """Production certainty entanglement with cryptographic security."""
    logger.info(" MEDUSA VAULT: 🌀 Entangling certainty into confidence matrix...")
    
    try:
        
        # Create quantum-secure certainty signature
        certainty_data = {
            'probability_field': probability_field,
            'timestamp': datetime.now().isoformat(),
            # Add quantum_salt for uniqueness (edge case: hash collisions are extremely rare)
            'quantum_salt': str(hash(str(probability_field)) % 1000000)
        }
        
        # Multi-layer hashing for quantum security
        first_hash = hashlib.sha256(json.dumps(certainty_data, sort_keys=True).encode()).hexdigest()
        second_hash = hashlib.sha512((first_hash + str(datetime.now().timestamp())).encode()).digest()
        
        logger.info(" MEDUSA VAULT: Certainty successfully entangled with quantum security.")
        return second_hash
    
    except Exception as e:
        logger.error(f"Certainty entanglement error: {e}")
        # Fallback to simple hash for resilience if advanced logic fails
        return hashlib.sha256(json.dumps(probability_field, sort_keys=True).encode()).digest()


async def mark_quantum_signatures(
    records: List["QuantumProphecyLine"],
) -> List["QuantumProphecyLine"]:
    """
    Production quantum signature marking with temporal coherence.
    """
    logger.info(" MEDUSA VAULT: Marking quantum signatures with temporal coherence...")
    
    try:
        updated_records = []
        
        for i, record in enumerate(records):
            # Generate production quantum signature (unique per record and time)
            signature_data = {
                'record_index': i,
                'timestamp': datetime.now().isoformat(),
                'quantum_state': str(hash(str(record)) % 1000000),
                'temporal_flux': datetime.now().timestamp() % 1000
            }
            
            # Create quantum-entangled signature (first 16 chars for brevity)
            signature_hash = hashlib.sha256(
                json.dumps(signature_data, sort_keys=True).encode()
            ).hexdigest()[:16] # 16-char quantum signature
            
            # Update record with quantum signature (model_copy for immutability)
            updated_records.append(
                record.model_copy(update={"quantum_signature": f"q_{signature_hash}"})
            )
        
        logger.info(f"✨ Successfully marked {len(updated_records)} quantum signatures.")
        return updated_records
    
    except Exception as e:
        # Log and return original records on error to avoid pipeline failure
        logger.error(f"Quantum signature marking error: {e}")
        return records # Return original records on error


# --- End Production Quantum Functions ---


# Quantum Configuration
# Load environment variables from .env file
# Defensive: calculate .env path relative to this file for deployment flexibility
dotenv_path_calculated = os.path.abspath(
    os.path.join(os.path.dirname(__file__), "..", "..", ".env")
)
loaded_dotenv_path = None

if os.path.exists(dotenv_path_calculated):
    logger.info(f"Attempting to load .env from explicit path: {dotenv_path_calculated}")
    if load_dotenv(dotenv_path=dotenv_path_calculated):
        logger.info(
            f".env file loaded successfully from explicit path: {dotenv_path_calculated}"
        )
        loaded_dotenv_path = dotenv_path_calculated
    else:
        # Edge case: .env exists but failed to load, try default search
        logger.warning(
            f".env file found at explicit path but TITAN PROCESSING FAILED: load: {dotenv_path_calculated}. Trying default search."
        )
        if load_dotenv():
            loaded_dotenv_path = find_dotenv()
            logger.info(
                f".env file loaded successfully from default search path: {loaded_dotenv_path if loaded_dotenv_path else 'Not Found'}"
            )
        else:
            logger.warning(
                " TITAN PROCESSING FAILED: load .env file from any path during default search."
            )
            loaded_dotenv_path = None
else:
    # Edge case: .env not found at explicit path, try default search
    logger.warning(
        f".env file not found at expected explicit path: {dotenv_path_calculated}. Trying default search."
    )
    if load_dotenv():
        loaded_dotenv_path = find_dotenv()
        logger.info(
            f".env file loaded successfully from default search path: {loaded_dotenv_path if loaded_dotenv_path else 'Not Found'}"
        )
    else:
        logger.warning(" TITAN WARNING: TITAN PROCESSING FAILED: load .env file from any path during default search.")
        loaded_dotenv_path = None

# SENSITIVE: Never log .env file contents or secrets in production.


SENTRY_DSN = os.getenv("SENTRY_DSN")
QUANTUM_KEY = os.getenv("QUANTUM_VAULT_KEY")
HMAC_SECRET = os.getenv("QUANTUM_HMAC_SECRET")
QUANTUM_DB_DSN = os.getenv("QUANTUM_DB_DSN")
MEDUSA_ODDS_API_KEY = os.getenv("MEDUSA_ODDS_KEY")

# Debugging: Print retrieved environment variable values (masked for sensitive info)
# Never log actual secrets in production; only log presence for diagnostics
logger.info(f"Retrieved SENTRY_DSN: {'***masked***' if SENTRY_DSN else 'None'}")
logger.info(f"Retrieved QUANTUM_VAULT_KEY: {'***masked***' if QUANTUM_KEY else 'None'}")
logger.info(f"Retrieved HMAC_SECRET: {'***masked***' if HMAC_SECRET else 'None'}")
logger.info(f"Retrieved QUANTUM_DB_DSN: {'***masked***' if QUANTUM_DB_DSN else 'None'}")
logger.info(
    f"Retrieved MEDUSA_ODDS_API_KEY: {'***masked***' if MEDUSA_ODDS_API_KEY else 'None'}"
)


BASE_URL = "https://api.the-odds-api.com/v4"

# Prometheus Metrics
METRICS = {
    "processed": Counter(
        "quantum_prophecy_processed", "Processed prophecies", ["sport"]
    ),
    "errors": Counter("quantum_prophecy_errors", "Processing errors", ["type"]),
    "latency": Histogram(
        "quantum_prophecy_latency", "API latency distribution", ["sport"]
    ),
    "encryption_errors": Counter(
        "quantum_encryption_errors", "Encryption/decryption failures"
    ),
}

# Default sports list
SPORTS = ["basketball_nba"]

# Initialize Sentry
if SENTRY_DSN:
    logger.info(" MEDUSA VAULT: Attempting to initialize Sentry with provided DSN.")
    try:
        sentry_sdk.init(
            dsn=SENTRY_DSN,
            integrations=[AioHttpIntegration(), LoggingIntegration(level=logging.INFO)],
            traces_sample_rate=0.1,
        )
        # Defensive: check if Sentry client is actually initialized
        if (
            sentry_sdk.Hub.current.client
            and sentry_sdk.Hub.current.client.dsn
            and sentry_sdk.Hub.current.client.dsn.public_key
        ):
            logger.info(" MEDUSA VAULT: Sentry initialized successfully.")
        else:
            # Edge case: DSN present but Sentry not fully initialized
            logger.warning(
                "Sentry DSN appears invalid after initialization. Sentry might not send events."
            )
    except sentry_sdk.utils.BadDsn as e:
        # Log and continue if Sentry DSN is invalid
        logger.error(
            f"Invalid Sentry DSN provided: {e}. Sentry will not be initialized for sending events."
        )
    except Exception as e:
        logger.error(
            f"Unexpected error during Sentry initialization: {e}. Sentry will not be fully active."
        )
else:
    logger.info(
        "SENTRY_DSN environment variable not set. Sentry will not be initialized."
    )


# --- Quantum Exceptions ---
class QuantumAPIError(Exception):
    """Base class for quantum API errors"""


class QuantumDecryptionError(QuantumAPIError):
    """ TITAN PROCESSING FAILED: decrypt quantum data"""


class QuantumEntanglementError(Exception):
    """Quantum data entanglement failure"""


# --- Quantum Helpers ---
@asynccontextmanager
async def quantum_session(cipher: Fernet):
    """Quantum context manager for secure operations"""
    try:
        yield
    except InvalidToken as e:
        METRICS["encryption_errors"].inc()
        logger.error(f"Quantum decryption failed: {e}")
        raise QuantumDecryptionError("Quantum decryption failed") from e
    finally:
        del cipher


# --- Core Quantum Logic ---
@retry(
    wait=wait_exponential(multiplier=1, min=2, max=30),
    stop=stop_after_attempt(5),
    retry=retry_if_exception_type((aiohttp.ClientError, QuantumDecryptionError)),
)
async def fetch_quantum_prophecy_lines(
    session: aiohttp.ClientSession,
    cipher: Fernet,
    sport: str = "basketball_nba",
    regions: str = "us",
    markets: str = "h2h,spreads,totals",
) -> List[Dict[str, Any]]:
    """
    Fetches quantum prophecy lines from The Odds API.
    Transforms the response into a format suitable for QuantumProphecyLine model.
    """
    logger.info(f"Fetching quantum prophecy lines for {sport} from The Odds API.")

    if not MEDUSA_ODDS_API_KEY:
        logger.critical(
            "MEDUSA_ODDS_KEY environment variable not set. Cannot connect to The Odds API. Aborting fetch."
        )
        raise ValueError("MEDUSA_ODDS_KEY is required for The Odds API.")

    url = f"{BASE_URL}/sports/{sport}/odds"
    params = {
        "apiKey": MEDUSA_ODDS_API_KEY,
        "regions": regions,
        "markets": markets,
        "oddsFormat": "american",
        "dateFormat": "iso",
    }

    try:
        async with session.get(url, params=params) as response:
            logger.info(
                f"Received HTTP response status from The Odds API: {response.status}"
            )
            response.raise_for_status()

            api_data = await response.json()


            # Handle both dictionary and list top-level responses
            raw_games = []
            if isinstance(api_data, dict) and api_data.get("success"):
                raw_games = api_data.get("data", [])
            elif isinstance(api_data, list):
                logger.warning(
                    "The Odds API returned a top-level list. Processing directly."
                )
                raw_games = api_data
            else:
                error_message = (
                    api_data.get("message", "Unknown error")
                    if isinstance(api_data, dict)
                    else f"Unexpected API response format (expected dict or list, got {type(api_data).__name__})"
                )
                logger.error(
                    f"The Odds API returned an error or unexpected format: {error_message}"
                )
                raise QuantumAPIError(
                    f"The Odds API error or unexpected format: {error_message}"
                )

            transformed_data = []
            for game in raw_games:
                titan_clash_id = game.get("id")
                commence_time = game.get("commence_time")
                home_team = game.get("home_team")
                away_team = game.get("away_team")

                if not (titan_clash_id and commence_time and home_team and away_team):
                    logger.warning(
                        f"Skipping game due to missing critical data: {titan_clash_id}"
                    )
                    continue

                game_timestamp = datetime.fromisoformat(
                    commence_time.replace("Z", "+00:00")
                )
                is_live = game_timestamp < datetime.now(timezone.utc)

                for bookmaker in game.get("bookmakers", []):
                    source_key = bookmaker.get("key")
                    source_title = bookmaker.get("title")
                    if not source_key:
                        logger.warning(
                            f"Skipping bookmaker for game {titan_clash_id} due to missing key."
                        )
                        continue

                    for market in bookmaker.get("markets", []):
                        market_key = market.get("key")
                        if not market_key:
                            logger.warning(
                                f"Skipping market for bookmaker {source_key} in game {titan_clash_id} due to missing key."
                            )
                            continue

                        outcomes = market.get("outcomes", [])

                        for outcome in outcomes:
                            outcome_name = outcome.get("name")
                            outcome_price = outcome.get("price")

                            if not (outcome_name and outcome_price is not None):
                                logger.warning(
                                    f"Skipping outcome for market {market_key} in bookmaker {source_key} for game {titan_clash_id} due to missing name or price."
                                )
                                continue

                            team_identifier = outcome_name

                            prophecy_lines_data = {
                                "outcome": outcome_name,
                                "price": outcome_price,
                                "market_key": market_key,
                            }
                            probability_field_data = {
                                "home_win_prob": 0.5,
                                "away_win_prob": 0.5,
                            }
                            if market_key == "totals":
                                team_identifier = f"total_{outcome_name}"
                                probability_field_data = {
                                    "over_prob": 0.5,
                                    "under_prob": 0.5,
                                }
                            elif market_key == "spreads":
                                team_identifier = outcome_name
                                if "point" in outcome:
                                    prophecy_lines_data["point"] = outcome["point"]
                                probability_field_data = {
                                    "home_spread_prob": 0.5,
                                    "away_spread_prob": 0.5,
                                }

                            transformed_data.append(
                                {
                                    "titan_clash_id": titan_clash_id,
                                    "market": market_key,
                                    "source": source_key,
                                    "team": team_identifier,
                                    "timestamp": game_timestamp,
                                    "extracted_at": datetime.now(timezone.utc),
                                    "is_live": is_live,
                                    "line_type": "current",
                                    "quantum_signature": "placeholder_will_be_generated_later",
                                    "prophecy_lines": prophecy_lines_data,
                                    "probability_field": probability_field_data,
                                }
                            )

            logger.info(
                f"Successfully transformed {len(transformed_data)} entries from The Odds API response."
            )
            return transformed_data

    except aiohttp.ClientResponseError as e:
        METRICS["errors"].labels(type="http").inc()
        logger.error(f"The Odds API HTTP Error {e.status}: {e.message} for URL {url}")
        raise
    except json.JSONDecodeError as e:
        METRICS["errors"].labels(type="json_decode").inc()
        logger.error(f" TITAN PROCESSING FAILED: decode JSON response from The Odds API: {e}")
        raise
    except Exception as e:
        METRICS["errors"].labels(type="fetch_general").inc()
        logger.critical(
            f"Unhandled exception during quantum prophecy fetch from The Odds API: {e}"
        )
        raise


def generate_temporal_signature() -> str:
    """Generate time-based quantum signature"""
    if not HMAC_SECRET:
        logger.critical(
            "QUANTUM_HMAC_SECRET environment variable not set. Cannot generate temporal signature. Aborting."
        )
        return "MOCK_TEMPORAL_SIGNATURE_MISSING_SECRET_CRITICAL_ERROR"

    now = datetime.now(timezone.utc).isoformat()
    signature = hmac.new(
        HMAC_SECRET.encode(), now.encode(), hashlib.sha3_256
    ).hexdigest()
    return signature


def validate_quantum_signature(data: Dict) -> bool:
    """Verify quantum data integrity (internal pipeline data)"""
    if not HMAC_SECRET:
        logger.critical(
            "QUANTUM_HMAC_SECRET environment variable not set. Cannot validate quantum signature. Validation skipped."
        )
        return False

    if "payload" not in data or "signature" not in data:
        logger.warning(
            "Incoming data missing 'payload' or 'signature' for quantum signature validation. Returning False."
        )
        return False

    try:
        signature = hmac.new(
            HMAC_SECRET.encode(),
            json.dumps(data["payload"], sort_keys=True).encode(),
            hashlib.sha3_256,
        ).hexdigest()
        is_valid = hmac.compare_digest(signature, data["signature"])
        if is_valid:
            pass
        else:
            logger.warning(" TITAN WARNING: Quantum signature validation failed (mismatch).")
        return is_valid
    except Exception as e:
        logger.error(f"Error during quantum signature validation: {e}")
        return False


async def transform_quantum_prophecy_lines(
    cipher: Fernet, raw_data: List[Dict[str, Any]]
) -> List[QuantumProphecyLine]:
    """
    Quantum data transformer with:
    - Field-level encryption
    - Confidence matrix generation
    - Entanglement validation
    """
    logger.info(f"Transforming {len(raw_data)} raw quantum prophecy lines.")
    processed = []

    for game_data_entry in raw_data:
        titan_clash_id = game_data_entry.get("titan_clash_id", "UNKNOWN_GAME_ID")
        try:
            required_keys_for_base = [
                "titan_clash_id",
                "market",
                "source",
                "team",
                "timestamp",
                "is_live",
                "line_type",
            ]
            if not all(key in game_data_entry for key in required_keys_for_base):
                logger.warning(
                    f"Skipping transformation for game {titan_clash_id} due to missing required base keys: {game_data_entry.keys()} vs {required_keys_for_base}"
                )
                continue

            prophecy_lines_to_encrypt = game_data_entry.get("prophecy_lines")
            if not prophecy_lines_to_encrypt:
                logger.warning(
                    f"Skipping transformation for game {titan_clash_id} due to missing 'prophecy_lines' for encryption."
                )
                continue

            encrypted_lines = cipher.encrypt(
                json.dumps(prophecy_lines_to_encrypt).encode()
            )

            probability_field_data = game_data_entry.get("probability_field")
            if not probability_field_data:
                logger.warning(
                    f"Skipping transformation for game {titan_clash_id} due to missing 'probability_field'."
                )
                continue

            confidence_matrix = entangle_certainty(probability_field_data)

            processed.append(
                QuantumProphecyLine(
                    encrypted_prophecy_lines=encrypted_lines,
                    confidence_matrix=confidence_matrix,
                    titan_clash_id=game_data_entry["titan_clash_id"],
                    market=game_data_entry["market"],
                    source=game_data_entry["source"],
                    team=game_data_entry["team"],
                    timestamp=game_data_entry["timestamp"],
                    extracted_at=game_data_entry.get(
                        "extracted_at", datetime.now(timezone.utc)
                    ),
                    is_live=game_data_entry.get("is_live", False),
                    line_type=game_data_entry.get("line_type", "open"),
                    quantum_signature=generate_temporal_signature(), # Use real signature generation
                )
            )
            logger.info(
                f"Game entry {titan_clash_id} transformed and added to processed list."
            )

        except ValidationError as e:
            METRICS["errors"].labels(type="validation").inc()
            logger.error(f"Quantum validation failure for game entry {titan_clash_id}: {e}")
        except InvalidToken as e:
            METRICS["encryption_errors"].inc()
            logger.error(
                f"Encryption failed during transformation for game entry {titan_clash_id}: {e}"
            )
            raise QuantumDecryptionError("Encryption failed during transformation")
        except Exception as e:
            METRICS["errors"].labels(type="transform").inc()
            logger.critical(
                f"Unexpected error transforming quantum prophecy for game entry {titan_clash_id}: {e}"
            )

    logger.info(f"Finished transforming {len(processed)} quantum prophecy lines.")
    return await quantum_weave_lines(processed)


@oracle_focus
async def persist_quantum_prophecy_lines(
    pool: asyncpg.Pool, records: List[QuantumProphecyLine], batch_size: int = 100
) -> int:
    """
    Quantum-safe persistence with:
    - Connection pooling
    - Batch inserts
    - Conflict resolution for entangled data
    """
    insert_sql = """
    INSERT INTO quantum_prophecy_lines (
        titan_clash_id, market, source, team, encrypted_prophecy_lines,
        timestamp, extracted_at, is_live, line_type,
        quantum_signature, confidence_matrix
    )
    VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11)
    ON CONFLICT (titan_clash_id, quantum_signature)
    DO UPDATE SET
        encrypted_prophecy_lines = EXCLUDED.encrypted_prophecy_lines,
        confidence_matrix = EXCLUDED.confidence_matrix,
        extracted_at = EXCLUDED.extracted_at
    """

    if not records:
        logger.info(" MEDUSA VAULT: No quantum records to persist.")
        return 0

    try:
        async with pool.acquire() as conn:
            data_to_insert = []
            for r in records:
                data_to_insert.append(
                    (
                        r.titan_clash_id,
                        r.market,
                        r.source,
                        r.team,
                        r.encrypted_prophecy_lines,
                        r.timestamp,
                        r.extracted_at,
                        r.is_live,
                        r.line_type,
                        r.quantum_signature,
                        r.confidence_matrix,
                    )
                )
            logger.info(
                f"Attempting to persist {len(data_to_insert)} quantum records to database."
            )
            result_status = await conn.executemany(insert_sql, data_to_insert)
            num_inserted = len(data_to_insert)
            logger.info(
                f"🌀 Persisted {num_inserted} quantum records (Result status: {result_status})."
            )
            return num_inserted
    except asyncpg.PostgresError as e:
        METRICS["errors"].labels(type="database").inc()
        logger.error(f"Quantum database entanglement failure: {e}")
        return 0
    except Exception as e:
        METRICS["errors"].labels(type="persistence_general").inc()
        logger.critical(f"Unhandled error during quantum persistence: {e}")
        return 0


# --- Quantum Orchestration ---
async def quantum_pipeline(sport: str, cipher: Fernet):
    """Quantum pipeline orchestration"""
    db_dsn = os.getenv("QUANTUM_DB_DSN")
    if not db_dsn:
        logger.critical(
            "QUANTUM_DB_DSN environment variable not set. Cannot connect to database. Aborting pipeline."
        )
        raise ValueError("Database connection string (QUANTUM_DB_DSN) is required.")

    async with aiohttp.ClientSession(
        connector=aiohttp.TCPConnector(ssl=False)
    ) as session:
        logger.info(f"Starting quantum pipeline for sport: {sport}")

        raw_data = await fetch_quantum_prophecy_lines(session, cipher, sport)
        logger.info(f"Extracted {len(raw_data)} raw data entries for {sport}.")

        processed = await transform_quantum_prophecy_lines(cipher, raw_data)
        METRICS["processed"].labels(sport=sport).inc(len(processed))
        logger.info(f"Transformed {len(processed)} entries for {sport}.")

        # Call mark_quantum_signatures and reassign the processed list
        processed = await mark_quantum_signatures(processed)
        logger.info(f"Marked quantum signatures for {sport}.")

        try:
            async with asyncpg.create_pool(dsn=db_dsn) as pool:
                await persist_quantum_prophecy_lines(pool, processed)
            logger.info(f"Persisted data for {sport}.")
        except Exception as e:
            logger.critical(f" TITAN PROCESSING FAILED: persist data for {sport}: {e}")
            METRICS["errors"].labels(type="pipeline_load_fail").inc()
            raise


async def main(sports: List[str] = SPORTS):
    """Quantum main execution flow"""
    logger.info(" MEDUSA VAULT: Starting Quantum NBA Odds Pipeline main execution.")

    if not QUANTUM_KEY:
        logger.critical(
            "QUANTUM_VAULT_KEY environment variable not set. Cannot initialize Fernet cipher. Exiting."
        )
        raise ValueError("QUANTUM_VAULT_KEY is required for encryption/decryption.")
    else:
        logger.info(" MEDUSA VAULT: QUANTUM_VAULT_KEY environment variable is set.")

    if not HMAC_SECRET:
        logger.critical(
            "QUANTUM_HMAC_SECRET environment variable not set. Cannot ensure data integrity. Exiting."
        )
        raise ValueError("QUANTUM_HMAC_SECRET is required for HMAC validation.")
    else:
        logger.info(" MEDUSA VAULT: QUANTUM_HMAC_SECRET environment variable is set.")

    if not QUANTUM_DB_DSN:
        logger.critical(
            "QUANTUM_DB_DSN environment variable not set. Cannot connect to database. Exiting."
        )
        raise ValueError("QUANTUM_DB_DSN is required for database connection.")
    else:
        logger.info(" MEDUSA VAULT: QUANTUM_DB_DSN environment variable is set.")

    if not MEDUSA_ODDS_API_KEY:
        logger.critical(
            "MEDUSA_ODDS_KEY environment variable not set. Cannot proceed with fetching odds. Exiting."
        )
        raise ValueError("MEDUSA_ODDS_KEY is required to fetch odds.")
    else:
        logger.info(" MEDUSA VAULT: MEDUSA_ODDS_API_KEY environment variable is set.")

    cipher = Fernet(QUANTUM_KEY)
    async with quantum_session(cipher):
        logger.info(f"Initiating quantum pipelines for sports: {sports}")
        tasks = [quantum_pipeline(sport, cipher) for sport in sports]
        await asyncio.gather(*tasks)

    logger.info(" MEDUSA VAULT: 🌠 Quantum prophecy convergence complete")


if __name__ == "__main__":
    try:
        prom.start_http_server(9090)
        logger.info(" MEDUSA VAULT: Prometheus metrics server started on port 9090")
    except Exception as e:
        logger.warning(
            f"Could not start Prometheus server on port 9090: {e}. Metrics will not be exposed."
        )

    parser = argparse.ArgumentParser(description="Quantum NBA Odds Pipeline")
    parser.add_argument("--sports", nargs="+", default=SPORTS)
    args = parser.parse_args()

    try:
        asyncio.run(main(args.sports))
    except QuantumAPIError as e:
        logger.critical(f"🌑 Quantum pipeline collapse: {e}")
        sentry_sdk.capture_exception(e)
        sys.exit(1)
    except Exception as e:
        logger.critical(f"Unhandled exception during main execution: {e}")
        sentry_sdk.capture_exception(e)
        sys.exit(1)
