from typing import Any, Dict, List, Optional
import numpy as np
import warnings
import random
from collections import defaultdict, deque
from datetime import datetime, timezone

"""
UnifiedTemporalAnomalyDetector: Unified interface for all temporal anomaly and drift detection.
Merges logic from TemporalAnomalyDetector, ExpertTemporalAnalyzer, and ExpertTemporalFluxStabilizer.
"""

# Import enums and data classes from legacy modules if needed
# from .temporal_stabilizer import TemporalAnomalyType, TemporalDataPoint, EnhancedAnomaly
# from .temporal_stabilizer import BasketballSeasonPhase, QuantumTemporalState
# For this migration, we will use dicts for anomalies for compatibility

class UnifiedTemporalAnomalyDetector:
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        self.anomaly_threshold = self.config.get('anomaly_threshold', 2.0)
        self.quantum_coherence_threshold = self.config.get('quantum_coherence_threshold', 0.5)
        self.game_day_sensitivity = self.config.get('game_day_sensitivity', 1.0)
        self.analysis_window_size = self.config.get('analysis_window_size', 50)
        # ...initialize ML models if available...
        self.ml_models = {}
        # ...other state as needed...

    def detect_anomalies(self, data: Any, context: Optional[Dict] = None) -> List[Dict]:
        """
        Main entry point for temporal anomaly detection.
        Args:
            data: List of values or rich data points (dicts or objects)
            context: Optional context (e.g., feature name, metrics)
        Returns:
            List of detected anomalies (dicts)
        """
        # Accept both simple and rich data
        if not data or len(data) < 5:
            return []
        # If data is a list of dicts/objects with 'value', treat as rich data
        if isinstance(data[0], dict) and 'value' in data[0]:
            values = np.array([d['value'] for d in data])
        else:
            values = np.array(data)
        anomalies = []
        # --- Statistical Z-Score ---
        mean_val = np.mean(values)
        std_val = np.std(values)
        for i, value in enumerate(values):
            if std_val > 0:
                z_score = abs((value - mean_val) / std_val)
                if z_score > self.anomaly_threshold:
                    anomalies.append({
                        'index': i,
                        'type': 'statistical_outlier',
                        'severity': min(1.0, z_score / (self.anomaly_threshold * 2)),
                        'description': f'Z-score {z_score:.2f} exceeds threshold',
                        'value': value,
                        'expected': mean_val,
                        'z_score': z_score
                    })
        # --- Trend/Change Point Detection (simple variance-based) ---
        window_size = min(10, len(values) // 4)
        if window_size >= 1:
            for i in range(window_size, len(values) - window_size):
                before = values[i-window_size:i]
                after = values[i:i+window_size]
                if np.std(before) > 0 and np.std(after) > 0:
                    diff = abs(np.mean(before) - np.mean(after))
                    if diff > std_val * 1.5:
                        anomalies.append({
                            'index': i,
                            'type': 'trend_change',
                            'severity': min(1.0, diff / std_val),
                            'description': f'Trend change at index {i}',
                            'value': values[i],
                            'expected': mean_val,
                            'trend_diff': diff
                        })
        # --- Seasonal/Hourly Pattern Anomalies (if timestamp info present) ---
        if isinstance(data[0], dict) and 'timestamp' in data[0]:
            hours = [d['timestamp'].hour for d in data]
            hourly_patterns = defaultdict(list)
            for i, hour in enumerate(hours):
                hourly_patterns[hour].append(values[i])
            for i, (d, value, hour) in enumerate(zip(data, values, hours)):
                if len(hourly_patterns[hour]) > 3:
                    hour_mean = np.mean(hourly_patterns[hour])
                    hour_std = np.std(hourly_patterns[hour])
                    if hour_std > 0:
                        z_score = abs((value - hour_mean) / hour_std)
                        if z_score > 2.0:
                            anomalies.append({
                                'index': i,
                                'type': 'seasonal_disruption',
                                'severity': min(1.0, z_score / 4.0),
                                'description': f'Hourly anomaly at hour {hour}',
                                'value': value,
                                'expected': hour_mean,
                                'z_score': z_score
                            })
        # --- Basketball Context Sensitivity ---
        if isinstance(data[0], dict) and 'basketball_context' in data[0]:
            context_groups = defaultdict(list)
            for i, d in enumerate(data):
                context_groups[d['basketball_context']].append((i, d['value']))
            for context_key, group in context_groups.items():
                if len(group) < 3:
                    continue
                group_values = np.array([v for _, v in group])
                group_mean = np.mean(group_values)
                group_std = np.std(group_values)
                sensitivity_multiplier = self.game_day_sensitivity
                for idx, value in group:
                    if group_std > 0:
                        z_score = abs((value - group_mean) / group_std)
                        adjusted_threshold = self.anomaly_threshold / sensitivity_multiplier
                        if z_score > adjusted_threshold:
                            anomalies.append({
                                'index': idx,
                                'type': 'basketball_context_anomaly',
                                'severity': min(1.0, z_score / (adjusted_threshold * 2)),
                                'description': f'Basketball context anomaly: z={z_score:.2f}',
                                'value': value,
                                'expected': group_mean,
                                'z_score': z_score,
                                'basketball_context': context_key
                            })
        # --- Quantum State Transition Anomalies ---
        if isinstance(data[0], dict) and 'quantum_state' in data[0]:
            quantum_states = [d['quantum_state'] for d in data if d['quantum_state']]
            for i in range(1, len(quantum_states)):
                prev_state = quantum_states[i-1]
                curr_state = quantum_states[i]
                if (prev_state, curr_state) in {('COHERENT', 'DECOHERENT'), ('STABILIZING', 'DECOHERENT'), ('ENTANGLED', 'COLLAPSED'), ('SUPERPOSITION', 'COLLAPSED')}:
                    anomalies.append({
                        'index': i,
                        'type': 'quantum_transition',
                        'severity': 0.7,
                        'description': f'Quantum state transition: {prev_state} → {curr_state}',
                        'quantum_signature': f'{prev_state}->{curr_state}'
                    })
            # Quantum coherence patterns
            window_size = min(10, len(data))
            for i in range(window_size, len(data) + 1):
                window_data = data[i-window_size:i]
                coherent_count = sum(1 for d in window_data if d['quantum_state'] in ['COHERENT', 'STABILIZING'])
                coherence_ratio = coherent_count / window_size
                values = [d['value'] for d in window_data]
                stability = 1.0 / (1.0 + np.std(values)) if len(values) > 1 else 1.0
                coherence_score = (coherence_ratio * 0.7) + (stability * 0.3)
                if coherence_score < self.quantum_coherence_threshold:
                    anomalies.append({
                        'index': i-1,
                        'type': 'quantum_coherence',
                        'severity': 1.0 - coherence_score,
                        'description': f'Low quantum coherence: {coherence_score:.3f}',
                        'coherence_score': coherence_score
                    })
        # --- Deduplicate by index/type ---
        seen = set()
        unique_anomalies = []
        for a in anomalies:
            key = (a['index'], a['type'])
            if key not in seen:
                seen.add(key)
                unique_anomalies.append(a)
        # --- Rank by severity ---
        unique_anomalies.sort(key=lambda x: x.get('severity', 0), reverse=True)
        return unique_anomalies

    def detect_drift(self, data: Any, context: Optional[Dict] = None) -> Dict:
        """
        Detects temporal drift in the data.
        Returns a dict with drift metrics.
        """
        if not data or len(data) < 5:
            return {'drift': False, 'drift_score': 0.0}
        if isinstance(data[0], dict) and 'value' in data[0]:
            values = np.array([d['value'] for d in data])
        else:
            values = np.array(data)
        # Simple drift: compare mean of first and last window
        window = max(5, len(values)//5)
        start_mean = np.mean(values[:window])
        end_mean = np.mean(values[-window:])
        drift_score = abs(end_mean - start_mean) / (np.std(values) + 1e-6)
        return {'drift': drift_score > 1.0, 'drift_score': drift_score}

    def analyze_temporal_patterns(self, data: Any, context: Optional[Dict] = None) -> Dict:
        """
        Advanced temporal pattern analysis (optional).
        """
        # Placeholder for pattern analysis logic
        return {}

    def send_feedback(self, feedback_type: str, details: dict):
        """
        Receive feedback from other system components (e.g., War Council, Feature Generator, Cortex).
        Args:
            feedback_type: Type of feedback (e.g., 'drift_detected', 'threshold_tune', 'error_fix', etc.)
            details: Additional context or instructions
        """
        # Log feedback
        # Example: self-healing/self-tuning logic
        if feedback_type == 'threshold_tune' and 'new_threshold' in details:
            self.anomaly_threshold = details['new_threshold']
        elif feedback_type == 'reset':
            # Reset internal state if needed
            self.anomaly_threshold = 0.7  # Reset to default
            self.context_cache.clear()
        # Extend with more feedback types as needed

# Optionally: Provide a singleton/global instance
unified_temporal_anomaly_detector = UnifiedTemporalAnomalyDetector()
