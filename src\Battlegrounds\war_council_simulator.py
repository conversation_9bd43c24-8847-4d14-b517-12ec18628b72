import sys
import os
import logging
import io
from typing import Dict, List, Tuple, Any, Optional, Union
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from scipy import stats
import random
import time
import asyncio
from dataclasses import dataclass, field
from concurrent.futures import Thread<PERSON>oolExecutor
from pathlib import Path
import json
import collections
from src.features.feature_feedback import FeatureFeedback
from src.features.feature_alchemist import SelfLearningFeatureAlchemist
from vault_oracle.core.medusa_core import unified_retrainer
from src.model_forge.UnifiedModelForge import get_unified_model_forge
from src.schemas.api_models import PlayerProfile
from src.mnemosyne_archive.mnemosyne_archive_keeper import MnemosyneArchive<PERSON>eeper
from vault_oracle.core.OracleMemory import OracleMemory
from nba_api.stats.endpoints import TeamGame<PERSON>og
from vault_oracle.wells.nba_api_connector import BasketballDataConnector
from src.cognitive_spires import ChronosOracle, HeroicProphecyEngine, <PERSON><PERSON><PERSON>ver
from nba_api.stats.endpoints import <PERSON><PERSON><PERSON><PERSON><PERSON>
from nba_api.stats.static import teams as teams_static, players as players_static
from vault_oracle.core.oracle_focus import oracle_focus
from vault_oracle.core.ichor_vitality import ExpertQuantumEntanglementManager as QuantumEntanglementManager
from vault_oracle.core.vault_loader import VaultLoader
from sklearn.cluster import KMeans
from src.cognitive_spires.CognitiveSpiresFactory_Expert import CognitiveSpiresFactory_Expert
from vault_oracle.analysis.unified_temporal_anomaly import unified_temporal_anomaly_detector
from src.features.feature_alchemist import feature_alchemist
from src.neural_cortex.neural_basketball_core import NeuralBasketballCore
from src.cognitive_basketball_cortex.cortex_core import CognitiveBasketballCortex
import traceback
from nba_api.stats.endpoints import PlayerDashPTShots, PlayerCareerStats
# DIGITAL FINGERPRINT: UUID=REPLACE_WITH_UUID | DATE=2025-06-29
"""
(C) 2025 Hyper Medusa Neural Vault, All Rights Reserved.
PROPRIETARY & CONFIDENTIAL — UNAUTHORIZED USE, COPYING, OR DISTRIBUTION IS STRICTLY PROHIBITED.

This file contains proprietary algorithms, business logic, and intellectual property of Hyper Medusa Neural Vault.
Use, reproduction, or disclosure by any means without explicit written permission is a violation of applicable law.
"""

"""
HYPER MEDUSA NEURAL VAULT - War Council Simulator Business Value Documentation
=============================================================================

war_council_simulator.py
------------------------
Expert-level simulation and ensemble orchestration for basketball scenario analysis and strategic decision support.

Business Value:
- Enables advanced scenario simulation and ensemble analysis for basketball intelligence.
- Supports extensibility for new simulation modules, spire integrations, and strategic plugins.
- Accelerates the development of new ensemble strategies and business logic.


Extension Points for Plugins & Custom Simulation Logic:
------------------------------------------------------
- Add new simulation modules or scenario engines by extending the War Council Simulator.
- Register simulation plugins via a plugin registry or callback system.
- Use the config system to enable/disable features per environment.

For further details, see module-level docstrings and architecture documentation.
"""

"""
🏀 HYPER MEDUSA NEURAL VAULT - War Council Simulator
===================================================

Expert-level scenario simulation, ensemble orchestration, and strategic council analytics for basketball intelligence.
"""

# UNIFIED FORGE MIGRATION: Use UnifiedModelForge for all model forging

# Configure logger
logger = logging.getLogger(__name__)
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter(
    "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
    )
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)

# Ensure feature_alchemist is globally available for feedback
feature_alchemist = SelfLearningFeatureAlchemist(model_trainer=None, data_source=None)

# Define MockSpire for fallback cases
# TODO: Replace with production implementation
# TODO: Replace with production implementation
class MockSpire:
    def __init__(self, name: str = "MockSpire", *args, **kwargs):
        self.name = name
    
    async def predict_async(self, *args, **kwargs):
        # A simple mock async prediction
        return {'prediction': 0.5, 'confidence': 0.5, 'spire_name': self.name}
    
    def predict(self, *args, **kwargs):
        # A simple mock sync prediction
        return {'prediction': 0.5, 'confidence': 0.5, 'spire_name': self.name}

# Core project imports

# Expert-Level Cognitive Spires Imports
try:
    from src.cognitive_spires import (
        # Core Expert Spires
    ChronosFatigueOracle_Expert,
    AthenaStrategyEngine_Expert,
    AresDefenseOracle_Expert,
    NikeVictoryOracle_Expert,
    MetisOracle_Expert,
 
 # Advanced Expert Spires
    ProphecyOrchestrator_Expert,
    HephaestusForge_Expert,
    SerpentWeave_Expert,
    FateForge_Expert,
    FateWeaver_Expert,
    GorgonWeave_Expert,
 # Ensemble Systems
    OlympianCouncil_Expert,
    PrometheusRealm_Expert,
    HeroicDeedWeaver_Expert,
    DivineModelForge_Expert
)
    EXPERT_SPIRES_AVAILABLE = True
    logger.info(" Expert-Level Cognitive Spires imported successfully")
 
except ImportError as spire_error:
    logger.error(f" TITAN PROCESSING FAILED: import Expert Cognitive Spires: {spire_error}")
    EXPERT_SPIRES_AVAILABLE = False
    # Fallback to any legacy spires if available
    try:
        logger.warning(" Using legacy cognitive spires as fallback")
    except ImportError:
        logger.critical(" No cognitive spires available - system severely degraded")
 
    # Use global MockSpire for fallback
    ChronosFatigueOracle_Expert = MockSpire
    AthenaStrategyEngine_Expert = MockSpire
    NikeVictoryOracle_Expert = MockSpire
    MetisOracle_Expert = MockSpire # Add Metis Oracle to fallback
    SerpentWeave_Expert = MockSpire # Add SerpentWeave to fallback
    HeroicDeedWeaver_Expert = MockSpire # Add HeroicDeedWeaver to fallback
    OlympianCouncil_Expert = MockSpire # Add OlympianCouncil to fallback


try:
    from nba_api.stats.endpoints import (
        LeagueGameLog,
        PlayByPlay,
        CommonTeamRoster,
        teamdashboardbygeneralsplits,
        ScoreboardV2,
        leaguegamefinder,
        playerdashboardbygeneralsplits,
        playergamelog,
        teamgamelog as nba_teamgamelog_module,
    )
except ImportError as e:
    raise ImportError(f" TITAN PROCESSING FAILED: import NBA API components: {e}")

try:
    from vault_oracle.core.oracle_focus import oracle_focus
except ImportError as e:
    raise ImportError(f" TITAN PROCESSING FAILED: import oracle_focus: {e}")

# --- Logging Configuration ---
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
if not logger.handlers:
    try:
        utf8_stdout = io.TextIOWrapper(sys.stdout.buffer, encoding="utf-8", line_buffering=True)
        ch = logging.StreamHandler(utf8_stdout)
    except Exception as e:
        logger.warning(f"Could not set UTF-8 encoding for sys.stdout: {e}")
        ch = logging.StreamHandler(sys.stdout)
    formatter = logging.Formatter("𓀀 %(asctime)s,%(msecs)03d 𓁟 %(levelname)s 𓀁 %(message)s")
    ch.setFormatter(formatter)
    logger.addHandler(ch)
    logger.propagate = False

FERNET_KEY = "8JO1x8W458-UtL15oKDt-AhKFsbhErCWOpBmbWAspic="
MEMORY_ARCHIVE_PATH = "./prod_data/team_analytics_archive.db"

_NBA_API_AVAILABLE = True
logger.info(" MEDUSA VAULT: NBA API components imported successfully (real data).")


# EXPERT SYSTEM DATA STRUCTURES
# ------------------------------

@dataclass
class GameContext:
    """Comprehensive game context for expert predictions"""
    titan_clash_id: str
    home_team: str # mythic_roster_id (str)
    away_team: str # mythic_roster_id (str)
    game_date: datetime
    season: str
    is_playoff: bool = False
    is_back_to_back: bool = False
    rest_days_home: int = 1
    rest_days_away: int = 1
    is_rivalry: bool = False
    national_tv: bool = False
    referee_crew: List[str] = field(default_factory=list)
    weather_conditions: Optional[Dict[str, Any]] = None
    venue_info: Optional[Dict[str, Any]] = None
    betting_lines: Optional[Dict[str, float]] = None


@dataclass 
class TeamAnalysis:
    """Expert-level team analysis result"""
    mythic_roster_id: str
    team_name: str
    primary_strengths: List[str]
    weaknesses: List[str]
    fatigue_level: float # 0-1 scale
    momentum_score: float # -1 to 1 scale
    injury_impact: float # 0-1 scale
    chemistry_rating: float # 0-1 scale
    recent_form: List[float] # Last 10 games performance
    matchup_advantages: Dict[str, float]
    confidence_factors: Dict[str, float]
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class ExpertPrediction:
    """Comprehensive expert prediction result"""
    prediction_id: str
    game_context: GameContext
    home_team_analysis: TeamAnalysis
    away_team_analysis: TeamAnalysis
    primary_prediction: float # Win probability for home team
    confidence: float # Confidence level 0-1
    uncertainty: float # Uncertainty measure 0-1
    contributing_spires: List[str] # Which expert spires contributed
    spire_predictions: Dict[str, Dict[str, float]] # Individual spire results
    ensemble_weights: Dict[str, float] # Final ensemble weights
    key_factors: List[str] # Most important prediction factors
    risk_assessment: str # LOW, MEDIUM, HIGH
    recommendations: List[str] # Actionable recommendations
    quality_score: float # Overall prediction quality 0-1
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class WarCouncilConfig:
    """Configuration for the War Council system"""
    enable_fatigue_analysis: bool = True
    enable_momentum_tracking: bool = True
    enable_strategy_analysis: bool = True
    enable_defense_analysis: bool = True
    enable_victory_analysis: bool = True
    enable_wisdom_validation: bool = True
    enable_ensemble_orchestration: bool = True
    enable_fate_weaving: bool = True
    enable_pattern_recognition: bool = True
    min_confidence_threshold: float = 0.3
    max_uncertainty_threshold: float = 0.4
    ensemble_size: int = 5
    async_processing: bool = True
    cache_predictions: bool = True
    enable_monitoring: bool = True


# SACRED DATA CATEGORIES
# ------------------------------
class HoopsPantheon:
    @oracle_focus
    def __init__(self):
        self.data_wells = {
            "advanced_metrics": {
                "endpoints": [
                    "leaguedashptplayer",
                    "leaguedashptteam",
                    "hustlestatsplayer",
                    "hustlestatsteam",
                    "synergyplaytypes",
                ],
                "cache_ttl": timedelta(hours=1),
            },
            "team_warfare": {
                "endpoints": [
                    "teamdashboardbygeneralsplits",
                    "teamvsplayer",
                    "teamestimatedmetrics",
                    "teamhistoricalratings",
                ],
                "cache_ttl": timedelta(hours=4),
            },
            "career_chronology": {
                "endpoints": [
                    "playercareerstats",                    "playerprofilev2",
                    "teamhistoricalleaders",
                ],
                "cache_ttl": timedelta(days=7),
            },
            "synergy_nexus": {
                "endpoints": [
                    "playerdashptpass",
                    "playerdashptshotdefend",
                    "playerdashptreboundlogs",
                    "teamdashptpass",
                ],
                "cache_ttl": timedelta(hours=6),
            },
        }

        self.metric_manifest = {
            "advanced_player": [
                "player_impact_estimate",
                "net_rating",
                "contested_shots_percent",
                "defensive_versatility",
                "adjusted_assist_ratio",
                "screen_assist_impact",
            ],
            "team_essence": [
                "elemental_balance",
                "olympian_bond_strength",
                "temporal_cohesion",
                "clash_prophecy_score",
            ],
            "career_arc": [
                "career_high_metrics",
                "age_curve_adjusted_stats",
                "peak_performance_window",
                "injury_derailed_metrics",
            ],
            "team_synergy": [
                "on_off_net_rating",
                "lineup_efficiency_percentiles",
                "assist_gravity_index",
    "defensive_communication_score",
    "rotational_synergy_index",
    ],
    }


# ------------------------------
# 🧙♂️ TEAM OLYMPIAN ANALYTICS
# ------------------------------
class TeamOlympianAnalytics:
    """Mythic team analysis powered by NBA API with elemental decay tracking"""
 
    ELEMENTAL_HALF_LIFE = {
    "fire": timedelta(hours=6),
    "earth": timedelta(days=2),
    "water": timedelta(days=1),
    "air": timedelta(hours=12),
    }

    ELEMENTAL_MAP = {
    "fire": "offensive_pace",
    "water": "defensive_fluidity",
    "earth": "rebound_strength",
    "air": "transition_speed",
    }

    def __init__(self):
        self.synergy_cache = {}
        # Create default config for OracleMemory
        oracle_config = {
        "memory_path": "prod_data/oracle_memory.db",
        "max_entries": 10000
        }
        self.chronovault = OracleMemory(oracle_config) # Use real OracleMemory with config
        # Initialize required dependencies for MnemosyneArchiveKeeper
 
        # Load config and create proper dependencies
        vault_config = VaultLoader.load_config()
        quantum_entangler = QuantumEntanglementManager(config=vault_config)
 
        # Pass the constants to the MnemosyneArchiveKeeper with proper dependencies
        self.reality_weaver = MnemosyneArchiveKeeper(
        config=vault_config,
        scroll_path=MEMORY_ARCHIVE_PATH,
        quantum_entangler=quantum_entangler
        )
        # teams_static is conditionally imported as real or SmartMockStaticTeams
        # Store team data mapped by string ID for consistency with NBA API usage
        self.team_data = {str(team["id"]): team for team in teams_static.get_teams()}
        self.all_nba_teams = teams_static.get_teams()
        self.all_nba_players = players_static.get_players()

    def get_team_essence(self, mythic_roster_id: str) -> Dict:
        """Full mythological team profile with live NBA data"""
        return {
        "elemental_profile": self.elemental_analysis(mythic_roster_id),
        "war_council_report": self.generate_war_report(mythic_roster_id),
        "synergy_nexus": self.calculate_olympian_bonds(mythic_roster_id),
        }

    def elemental_analysis(self, mythic_roster_id: str) -> Dict:
        """Quantify elemental playstyle composition with decay rates using real NBA stats"""
        try:
            # Fetch real team stats from NBA API, ensure mythic_roster_id is string
            season = BasketballDataConnector().current_season
            team_stats_instance = TeamGameLog(team_id=mythic_roster_id, season=season)
            team_stats_df_list = team_stats_instance.get_data_frames()
            
            if not team_stats_df_list: # Check if the list is empty
                logger.warning(
                f"No data frames returned for mythic_roster_id {mythic_roster_id} in season {season}. Returning default elemental balance."
                )
                team_stats_df = pd.DataFrame()
            else:
                team_stats_df = team_stats_df_list[0]

            if team_stats_df.empty:
                logger.warning(
                f"No team stats found for mythic_roster_id {mythic_roster_id} in season {season}. Returning default elemental balance."
                )
                elemental_balance = {
                "Fire": 0.25,
                "Water": 0.25,
                "Earth": 0.25,
                "Air": 0.25,
                }
                primary_element = "Balanced"
            else:
                # Example mapping: Fire = PTS, Water = STL, Earth = REB, Air = AST
                total_pts = team_stats_df["PTS"].mean() if "PTS" in team_stats_df else 0
                total_stl = team_stats_df["STL"].mean() if "STL" in team_stats_df else 0
                total_reb = team_stats_df["REB"].mean() if "REB" in team_stats_df else 0
                total_ast = team_stats_df["AST"].mean() if "AST" in team_stats_df else 0
                stat_sum = total_pts + total_stl + total_reb + total_ast
                if stat_sum == 0:
                    elemental_balance = {
                    "Fire": 0.25,
                    "Water": 0.25,
                    "Earth": 0.25,
                    "Air": 0.25,
                    }
                    primary_element = "Balanced"
                else:
                    elemental_balance = {
                    "Fire": total_pts / stat_sum,
                    "Water": total_stl / stat_sum,
                    "Earth": total_reb / stat_sum,
                    "Air": total_ast / stat_sum,
                    }
                    primary_element = max(elemental_balance, key=elemental_balance.get)
            return {
            "primary_element": primary_element,
            "elemental_balance": elemental_balance,
            "elemental_decay": self._calculate_all_elemental_decays(mythic_roster_id),
            "elemental_weakness": self._find_elemental_vulnerability(mythic_roster_id),
            }
        except Exception as e:
            logger.error(
            f" MEDUSA ERROR: elemental_analysis for mythic_roster_id {mythic_roster_id}: {e}", exc_info=True
            )
            return {
            "primary_element": "Error",
            "elemental_balance": {
            "Fire": 0.25,
            "Water": 0.25,
            "Earth": 0.25,
            "Air": 0.25,
            },
            "elemental_decay": self._calculate_all_elemental_decays(mythic_roster_id),
            "elemental_weakness": self._find_elemental_vulnerability(mythic_roster_id),
            }

    def calculate_olympian_bonds(self, mythic_roster_id: str) -> Dict:
        """Team chemistry analysis using recent rotations"""
        if self._check_roster_stability(mythic_roster_id):
            return self.synergy_cache.get(mythic_roster_id, {})

        lineup_data = self._get_lineup_chronology(mythic_roster_id) # This method is not defined
        bonds = {
        "assist_web": self._calculate_assist_nexus(lineup_data),
        "defensive_harmony": self._measure_defensive_synergy(lineup_data),
        "chronosync": self._temporal_cohesion_index(mythic_roster_id),
        }
        self.synergy_cache[mythic_roster_id] = bonds
        return bonds

    def _extract_substitution_waves(self, mythic_roster_id: str) -> pd.DataFrame:
        """Get substitution patterns from NBA play-by-play data"""
        titan_clash_id = self._get_last_game(mythic_roster_id)
        if not titan_clash_id or titan_clash_id == "NO_GAME_ID_FOUND":
            return pd.DataFrame(columns=["PERIOD", "PCTIMESTRING", "PLAYER1_ID"])
        
        pbp_instance = PlayByPlay(game_id=titan_clash_id) # Use game_id parameter
        pbp_data_list = pbp_instance.get_data_frames()
        
        if not pbp_data_list: # Check if the list is empty
            logger.warning(
            f"No data frames returned for PlayByPlay data for game_id {titan_clash_id}. Returning empty DataFrame."
            )
            return pd.DataFrame(columns=["PERIOD", "PCTIMESTRING", "PLAYER1_ID"])
        
        pbp_data = pbp_data_list[0]

        # Ensure columns exist before attempting to access them
        required_cols = ["PERIOD", "PCTIMESTRING", "PLAYER1_ID", "EVENTMSGTYPE"]
        if not all(col in pbp_data.columns for col in required_cols):
            logger.warning(
            f"Missing one or more required columns in PlayByPlay data: {required_cols}. Returning empty DataFrame."
            )
            return pd.DataFrame(columns=["PERIOD", "PCTIMESTRING", "PLAYER1_ID"])

        return pbp_data[pbp_data["EVENTMSGTYPE"] == 8][
        ["PERIOD", "PCTIMESTRING", "PLAYER1_ID"]
        ]

    def _temporal_cohesion_index(self, mythic_roster_id: str) -> float:
        """Calculate lineup consistency using substitution entropy"""
        substitutions = self._extract_substitution_waves(mythic_roster_id)
        if substitutions.empty:
            return 0.0

        # Ensure 'PLAYER1_ID' is in substitutions before groupby
        if (
        "PLAYER1_ID" not in substitutions.columns
        or "PCTIMESTRING" not in substitutions.columns
        ):
            logger.warning(
            "Missing 'PLAYER1_ID' or 'PCTIMESTRING' for temporal cohesion index. Returning 0.0."
            )
            return 0.0

        rotation_patterns = substitutions.groupby("PCTIMESTRING")["PLAYER1_ID"].apply(
        tuple
        )
        if rotation_patterns.empty:
            return 0.0

        if len(rotation_patterns.unique()) <= 1:
            return 0.0

        entropy = stats.entropy(rotation_patterns.value_counts(normalize=True))
        # Handle case where log(0) or log(1) could occur if there's only one unique pattern
        if len(rotation_patterns.unique()) == 0: # This condition is redundant given the check above
            return 0.0
        return 1 - (entropy / np.log(len(rotation_patterns.unique())))

    def _calculate_all_elemental_decays(self, mythic_roster_id: str) -> Dict[str, float]:
        """Calculate decay factors for all elements"""
        last_game_date = self._get_last_game_date(mythic_roster_id)
        if not last_game_date:
            return {element: 1.0 for element in self.ELEMENTAL_HALF_LIFE}

        time_since = datetime.now() - last_game_date
        return {
        element: 0.5 ** (time_since.total_seconds() / hl.total_seconds())
        for element, hl in self.ELEMENTAL_HALF_LIFE.items()
        }

    def _get_last_game_date(self, mythic_roster_id: str) -> Optional[datetime]:
        """Get most recent game timestamp from NBA API"""
        season = BasketballDataConnector().current_season
        # Ensure team_id is passed as string to TeamGameLog
        games_instance = TeamGameLog(team_id=mythic_roster_id, season=season)
        games_df_list = games_instance.get_data_frames()
        
        if not games_df_list:
            logger.warning(
            f"No data frames returned for TeamGameLog for team {mythic_roster_id} in {season}. Cannot determine last game date."
            )
            return None

        games_df = games_df_list[0]

        if games_df.empty or "GAME_DATE" not in games_df.columns:
            logger.warning(
            f"No game data or 'GAME_DATE' column found for team {mythic_roster_id} in {season}. Cannot determine last game date."
            )
            return None
        return pd.to_datetime(games_df["GAME_DATE"].max())

    def _check_roster_stability(self, mythic_roster_id: str) -> bool:
        """Verify roster consistency using NBA API data"""
        # CommonTeamRoster is conditionally imported as real or SmartMockCommonTeamRoster
        roster_instance = CommonTeamRoster(team_id=mythic_roster_id)
        roster_df_list = roster_instance.get_data_frames()

        if not roster_df_list:
            logger.warning(
            f"No data frames returned for CommonTeamRoster for team {mythic_roster_id}. Assuming instability."
            )
            return False

        roster_df = roster_df_list[0]

        if roster_df.empty or "PLAYER_ID" not in roster_df.columns:
            logger.warning(
            f"No roster data or 'PLAYER_ID' column found for team {mythic_roster_id}. Assuming instability."
            )
            return False

        current_roster = roster_df["PLAYER_ID"].tolist()
        cached_roster = self.synergy_cache.get(f"roster_{mythic_roster_id}", [])
        # Update cache for next check
        self.synergy_cache[f"roster_{mythic_roster_id}"] = current_roster
        return current_roster == cached_roster

    def _get_last_game(self, mythic_roster_id: str) -> str:
        """Retrieve most recent game ID from cached data"""
        season = BasketballDataConnector().current_season
        if hasattr(self.chronovault, "recall_memory"):
            last_game_id = self.chronovault.recall_memory(
            f"last_game_{mythic_roster_id}", time_window=timedelta(days=7)
            )
            if last_game_id:
                return last_game_id
            else:
                logger.warning(
                "chronovault.recall_memory not available or returned None. Attempting to get last game ID from NBA API."
                )
                try:
                    # Ensure the team ID is correctly accessed from self.team_data (which uses string keys)
                    # and passed as string to TeamGameLog
                    team_id_for_api = self.team_data.get(mythic_roster_id, {}).get("id") or int(mythic_roster_id) # Fallback to int conversion
                    game_log_instance = TeamGameLog(
                        team_id=str(team_id_for_api), # Ensure it's a string
                        season=season,
                    )
                    game_log_df_list = game_log_instance.get_data_frames()
                    
                    if not game_log_df_list:
                        logger.error(f"No data frames returned for TeamGameLog for team {mythic_roster_id}.")
                        return "NO_GAME_ID_FOUND"

                    game_log = game_log_df_list[0]

                    if not game_log.empty and "GAME_ID" in game_log.columns:
                        game_log["GAME_DATE_DT"] = pd.to_datetime(game_log["GAME_DATE"])
                        most_recent_game = game_log.sort_values(
                        by="GAME_DATE_DT", ascending=False
                        ).iloc[0]
                        return most_recent_game["GAME_ID"]
                except Exception as e:
                    logger.error(
                    f" TITAN PROCESSING FAILED: get last game ID from NBA API for team {mythic_roster_id}: {e}",
                    exc_info=True,
                    )
                    return "NO_GAME_ID_FOUND" # Last resort fallback
        return "NO_GAME_ID_FOUND" # Fallback if chronovault is not available

    # Dummy methods for TeamOlympianAnalytics (these implement logic, not external data)
    def _dominant_element(self, mythic_roster_id: str) -> str:
        """Determine the dominant element for a team based on real stats."""
        analysis = self.elemental_analysis(mythic_roster_id) # mythic_roster_id is already str
        return analysis["primary_element"]

    def _elemental_ratios(self, mythic_roster_id: str) -> Dict:
        """Return the elemental ratios for a team based on real stats."""
        analysis = self.elemental_analysis(mythic_roster_id) # mythic_roster_id is already str
        return analysis["elemental_balance"]

    def _find_elemental_vulnerability(self, mythic_roster_id: str) -> str:
        """Find the weakest element for a team based on real stats."""
        analysis = self.elemental_analysis(mythic_roster_id) # mythic_roster_id is already str
        balance = analysis["elemental_balance"]
        return min(balance, key=balance.get)

    # _get_lineup_chronology is undefined, adding a mock implementation
    def _get_lineup_chronology(self, mythic_roster_id: str) -> pd.DataFrame:
        """Mock method for getting lineup chronology."""
        logger.warning(f"'_get_lineup_chronology' not implemented. Returning empty DataFrame for {mythic_roster_id}.")
        return pd.DataFrame(columns=['PLAYER1_ID', 'ASSISTED_BY', 'STL', 'BLK', 'DREB'])

    def _calculate_assist_nexus(self, lineup_data: pd.DataFrame) -> Dict:
        """Calculate assist network metrics from lineup data."""
        if (
        lineup_data is None
        or lineup_data.empty
        or "ASSISTED_BY" not in lineup_data.columns
        or "PLAYER1_ID" not in lineup_data.columns # Ensure PLAYER1_ID also exists for groupby
        ):
            return {"assist_web": {}}
        assist_counts = (
        lineup_data.groupby(["PLAYER1_ID", "ASSISTED_BY"])
        .size()
        .reset_index(name="count")
        )
        if assist_counts.empty:
            return {"assist_web": {}}
        
        assist_web_pivot = (
        assist_counts.pivot(
        index="PLAYER1_ID", columns="ASSISTED_BY", values="count"
        )
        .fillna(0)
        )
        # Convert to dictionary and then to regular dict for clean output
        return {"assist_web": assist_web_pivot.to_dict('index')}

    def _measure_defensive_synergy(self, lineup_data: pd.DataFrame) -> Dict:
        """Estimate defensive synergy using steals, blocks, and defensive rebounds."""
        if lineup_data is None or lineup_data.empty:
            return {"defensive_harmony": 0.0}
        # Example: sum of STL, BLK, DREB per player
        for col in ["STL", "BLK", "DREB"]:
            if col not in lineup_data.columns:
                lineup_data[col] = 0
        
        if lineup_data.empty: # Check after adding columns if it somehow becomes empty
            return {"defensive_harmony": 0.0}

        defensive_score_series = (
        lineup_data[["PLAYER1_ID", "STL", "BLK", "DREB"]]
        .groupby("PLAYER1_ID")
        .sum()
        .sum(axis=1)
        )
        if defensive_score_series.empty:
            return {"defensive_harmony": 0.0}
        
        defensive_score = defensive_score_series.mean()
        return {"defensive_harmony": defensive_score}

    def generate_war_report(self, mythic_roster_id: str) -> Dict:
        """Generate a real war report for a team using NBA API and database stats."""
        try:
            # Get team info
            team_info = next(
            (t for t in self.all_nba_teams if str(t["id"]) == str(mythic_roster_id)), None
            )
            if not team_info:
                logger.warning(f"No NBA team found for mythic_roster_id {mythic_roster_id}.")
                return {"error": f"No NBA team found for mythic_roster_id {mythic_roster_id}"}
            # Get recent games
            recent_games = self.query_recent_games(
            team_info["abbreviation"], lookback_days=10
            ) # query_recent_games is not defined, will cause error. Using a mock or db.query_recent_games if available
            
            # Placeholder for query_recent_games. Assuming it's a method of BasketballDataConnector or similar.
            # If not, this will need a concrete implementation or mock.
            if hasattr(self.chronovault, 'query_recent_games'): # Checking if this method exists in chronovault
                 recent_games = self.chronovault.query_recent_games(team_info["abbreviation"], lookback_days=10)
            elif hasattr(self.data_connector, 'query_recent_games'): # Checking for data_connector
                 recent_games = self.data_connector.query_recent_games(team_info["abbreviation"], lookback_days=10)
            else:
                 logger.warning("query_recent_games method not found in available objects. Using empty list.")
                 recent_games = []

            # Get team stats
            season = str(datetime.now().year - 1) + "-" + str(datetime.now().year)[-2:]
            team_stats = self.query_team_stats(team_info["abbreviation"], season=season) # query_team_stats is not defined
            
            # Placeholder for query_team_stats. Similar to query_recent_games.
            if hasattr(self.chronovault, 'query_team_stats'):
                 team_stats = self.chronovault.query_team_stats(team_info["abbreviation"], season=season)
            elif hasattr(self.data_connector, 'query_team_stats'):
                 team_stats = self.data_connector.query_team_stats(team_info["abbreviation"], season=season)
            else:
                 logger.warning("query_team_stats method not found in available objects. Using empty dict.")
                 team_stats = {}

            # Get elemental affinities (if available)
            elemental_affinities = self.query_player_elemental_affinities(
            team_info["abbreviation"]
            ) # query_player_elemental_affinities is not defined
            
            # Placeholder for query_player_elemental_affinities.
            if hasattr(self.chronovault, 'query_player_elemental_affinities'):
                elemental_affinities = self.chronovault.query_player_elemental_affinities(team_info["abbreviation"])
            elif hasattr(self.data_connector, 'query_player_elemental_affinities'):
                elemental_affinities = self.data_connector.query_player_elemental_affinities(team_info["abbreviation"])
            else:
                logger.warning("query_player_elemental_affinities method not found. Using empty dict.")
                elemental_affinities = {}

            return {
            "team": team_info,
            "recent_games": recent_games,
            "team_stats": team_stats,
            "elemental_affinities": elemental_affinities,
            }
        except Exception as e:
            logger.error(
            f"Error generating war report for team {mythic_roster_id}: {e}", exc_info=True
            )
            return {"error": str(e)}

    def _derive_team_from_player(self, hero_id: str) -> str:
        """Return the real mythic_roster_id for a player using NBA API static data."""
        try:
            # Use BasketballDataConnector to get player's current team
            connector = BasketballDataConnector()
            # Assuming get_player_info can return team_id or team abbreviation that can be mapped to team_id
            player_info_data = connector.get_player_info(int(hero_id))
            if player_info_data and isinstance(player_info_data, list) and not player_info_data[0].empty:
                # Assuming the first DataFrame has 'TEAM_ID' or similar
                team_id = player_info_data[0].iloc[0].get("TEAM_ID")
                if team_id:
                    return str(team_id)
            
            # Fallback to static players data for initial lookup if connector doesn't directly provide team_id
            player = next(
                (p for p in self.all_nba_players if str(p["id"]) == str(hero_id)), None
            )
            if player:
                # If player found, but no direct team ID from current season data,
                # we might infer from player's last active team or historical data
                # For now, if get_player_info didn't give it, we don't have it easily.
                logger.warning(f"No current team found for hero_id {hero_id} via API connector. Cannot derive team.")
                return ""

            logger.warning(f"No player found for hero_id {hero_id} in static data or API.")
            return ""
        except Exception as e:
            logger.error(
            f"Error deriving team from player {hero_id}: {e}", exc_info=True
            )
            return ""

        """Calculate a synergy fingerprint using player and team stats."""
        try:
            player_tracking_stats = raw_data.get("player", {}).get("tracking", {})
            team_stats = raw_data.get("team", {})
            # Example: combine player and team stats into a synergy vector
            synergy = {
            k: player_tracking_stats.get(k, 0) + team_stats.get(k, 0)
            for k in set(player_tracking_stats) | set(team_stats)
            }
            return synergy
        except Exception as e:
            logger.error(f"Error calculating synergy fingerprint: {e}", exc_info=True)
            return {}

    def _create_prophecy_vectors(self, raw_data: Dict) -> Dict:
        """Create ML-ready feature vectors from player and team context."""
        try:
            player_tracking_stats = raw_data.get("player", {}).get("tracking", {})
            team_stats = raw_data.get("team", {})
            # Example: concatenate player tracking and team stats
            player_vec = list(player_tracking_stats.values())
            team_vec = list(team_stats.values())
            return {"feature_vector": player_vec + team_vec}
        except Exception as e:
            logger.error(f"Error creating prophecy vectors: {e}", exc_info=True)
            return {}

    def _temporal_encode(self, raw_data: Dict) -> Dict:
        """Create temporal embeddings using rolling averages of player stats over recent games."""
        try:

            player_tracking_stats = raw_data.get("player", {}).get("tracking", {})
            hero_id = player_tracking_stats.get("PLAYER_ID")
            if not hero_id:
                logger.warning("No PLAYER_ID found for temporal encoding. Returning default.")
                return {"temporal_embedding": [0.0]}
            
            game_log_instance = PlayerGameLog(player_id=hero_id) # Use player_id parameter
            game_log_df_list = game_log_instance.get_data_frames()

            if not game_log_df_list:
                logger.warning(f"No data frames returned for PlayerGameLog for player {hero_id}. Returning default.")
                return {"temporal_embedding": [0.0]}

            game_log = game_log_df_list[0]

            if game_log.empty:
                logger.warning(f"Empty game log for player {hero_id}. Returning default temporal embedding.")
                return {"temporal_embedding": [0.0]}
            # Example: rolling average of points, assists, rebounds over last 5 games
            stats = ["PTS", "AST", "REB"]
            # Ensure columns exist before accessing them for rolling average
            for stat in stats:
                if stat not in game_log.columns:
                    game_log[stat] = 0 # Fill with 0 if missing

            rolling = (
            game_log[stats].rolling(window=5).mean().iloc[-1].fillna(0).tolist()
            )
            return {"temporal_embedding": rolling}
        except Exception as e:
            logger.error(f" MEDUSA ERROR: temporal encoding: {e}", exc_info=True)
            return {"temporal_embedding": [0.0]}

    def _archetype_predict(self, raw_data: Dict) -> str:
        """Predict player archetype using a simple clustering model (e.g., KMeans on stats)."""
        try:

            player_tracking_stats = raw_data.get("player", {}).get("tracking", {})
            if not player_tracking_stats:
                return "Unknown"
            # Example: Use PTS, AST, REB for clustering
            features = np.array(
            [[player_tracking_stats.get("PTS", 0), player_tracking_stats.get("AST", 0), player_tracking_stats.get("REB", 0)]]
            )
            # Dummy centroids for archetypes: [Scorer, Playmaker, Rebounder]
            centroids = np.array([[25, 3, 5], [10, 8, 4], [8, 2, 12]])
            # Ensure n_init is set for KMeans in newer sklearn versions
            kmeans = KMeans(n_clusters=3, init=centroids, n_init=1)
            kmeans.fit(centroids)
            label = kmeans.predict(features)[0]
            return ["Scorer", "Playmaker", "Rebounder"][label]
        except Exception as e:
            logger.error(f" MEDUSA ERROR: archetype prediction: {e}", exc_info=True)
            return "Unknown"


# ------------------------------
# 🧬 GENETIC FEATURE ENGINEERING (UPDATED)
# ------------------------------
class ProphecyFeatureForge:
    """Transforms raw stats into prophetic features with team context"""

    @oracle_focus
    def __init__(self):
        self.team_oracle = TeamOlympianAnalytics()

    @oracle_focus
    def forge_features(self, hero_id: str) -> Dict:
        """Create ML-ready features with team synergy context using real NBA data"""
        # Get player tracking stats
        try:

            tracking_list = (
            PlayerDashPTShots(player_id=hero_id) # Use player_id parameter
            .get_data_frames()
            )
            tracking = tracking_list[0].to_dict(orient="records")[0] if tracking_list and not tracking_list[0].empty else {}

            career_list = (
            PlayerCareerStats(player_id=hero_id) # Use player_id parameter
            .get_data_frames()
            )
            if career_list and not career_list[0].empty:
                career = career_list[0].to_dict(orient="records")[0]
            else:
                career = {}
        except Exception as e:
            logger.error(
            f"Error fetching player stats for hero_id {hero_id}: {e}",
            exc_info=True,
            )
            tracking = {}
            career = {}
        raw_data = {
        "player": {"tracking": tracking, "career": career},
        "team": self._get_team_context(hero_id),
        }
        return {
        "temporal_embeddings": self._temporal_encode(raw_data),
        "archetype_cluster": self._archetype_predict(raw_data),
        "prophecy_vectors": self._create_prophecy_vectors(raw_data),
        }

    @oracle_focus
    def _get_team_context(self, hero_id: str) -> Dict:
        """Retrieve team essence for player context using real NBA data"""
        mythic_roster_id = self._derive_team_from_player(hero_id)
        if not mythic_roster_id:
            logger.warning(f"Could not derive team for player {hero_id}. Returning empty team essence.")
            return {} # Return empty dict if team not found
        return self.team_oracle.get_team_essence(mythic_roster_id)

    def _derive_team_from_player(self, hero_id: str) -> str:
        """Return the real mythic_roster_id for a player using NBA API static data and BasketballDataConnector."""
        try:
            # First, try to get current team from BasketballDataConnector
            connector = BasketballDataConnector()
            player_info_data = connector.get_player_info(int(hero_id))
            if player_info_data and isinstance(player_info_data, list) and not player_info_data[0].empty:
                team_id = player_info_data[0].iloc[0].get("TEAM_ID")
                if team_id:
                    return str(team_id)
            
            # Fallback to static players data for ID mapping, though it doesn't give current team
            player = next(
            (
            p
            for p in players_static.get_players()
            if str(p["id"]) == str(hero_id)
            ),
            None,
            )
            if player:
                # Static data does not directly have 'mythic_roster_id'.
                # This part is effectively just confirming player existence, not finding their team.
                # If the connector didn't find the team, then we likely don't have it easily.
                logger.warning(f"Could not derive current team for player {hero_id} from API connector or static data.")
                return ""
            return ""
        except Exception as e:
            logger.error(
            f"Error deriving team from player {hero_id}: {e}", exc_info=True
            )
            return ""

        """Combine player and team stats into a synergy vector."""
        try:
            player_stats = raw_data.get("player", {}).get("tracking", {})
            team_stats = raw_data.get("team", {})
            synergy = {
            k: player_stats.get(k, 0) + team_stats.get(k, 0)
            for k in set(player_stats.keys()) | set(team_stats.keys()) # Use .keys() for sets
            }
            return synergy
        except Exception as e:
            logger.error(f"Error calculating synergy fingerprint: {e}", exc_info=True)
            return {}

    def _create_prophecy_vectors(self, raw_data: Dict) -> Dict:
        """Concatenate player tracking and team stats into a feature vector."""
        try:
            player_stats = raw_data.get("player", {}).get("tracking", {})
            team_stats = raw_data.get("team", {})
            player_vec = list(player_stats.values())
            team_vec = list(team_stats.values())
            return {"feature_vector": player_vec + team_vec}
        except Exception as e:
            logger.error(f"Error creating prophecy vectors: {e}", exc_info=True)
            return {}

    def _temporal_encode(self, raw_data: Dict) -> Dict:
        """Create temporal embeddings using rolling averages of player stats over recent games."""
        try:

            player_stats = raw_data.get("player", {}).get("tracking", {})
            hero_id = player_stats.get("PLAYER_ID")
            if not hero_id:
                logger.warning("No PLAYER_ID found for temporal encoding. Returning default.")
                return {"temporal_embedding": [0.0]}
            
            game_log_instance = PlayerGameLog(player_id=hero_id)
            game_log_df_list = game_log_instance.get_data_frames()

            if not game_log_df_list:
                logger.warning(f"No data frames returned for PlayerGameLog for player {hero_id}. Returning default.")
                return {"temporal_embedding": [0.0]}

            game_log = game_log_df_list[0]

            if game_log.empty:
                logger.warning(f"Empty game log for player {hero_id}. Returning default temporal embedding.")
                return {"temporal_embedding": [0.0]}
            # Example: rolling average of points, assists, rebounds over last 5 games
            stats = ["PTS", "AST", "REB"]
            # Ensure columns exist before accessing them for rolling average
            for stat in stats:
                if stat not in game_log.columns:
                    game_log[stat] = 0 # Fill with 0 if missing
            rolling = (
            game_log[stats].rolling(window=5).mean().iloc[-1].fillna(0).tolist()
            )
            return {"temporal_embedding": rolling}
        except Exception as e:
            logger.error(f" MEDUSA ERROR: temporal encoding: {e}", exc_info=True)
            return {"temporal_embedding": [0.0]}

    def _archetype_predict(self, raw_data: Dict) -> str:
        """Predict player archetype using a simple clustering model (e.g., KMeans on stats)."""
        try:

            stats = raw_data.get("player", {}).get("tracking", {})
            if not stats:
                return "Unknown"
            # Example: Use PTS, AST, REB for clustering
            features = np.array(
            [[stats.get("PTS", 0), stats.get("AST", 0), stats.get("REB", 0)]]
            )
            # Dummy centroids for archetypes: [Scorer, Playmaker, Rebounder]
            centroids = np.array([[25, 3, 5], [10, 8, 4], [8, 2, 12]])
            kmeans = KMeans(n_clusters=3, init=centroids, n_init=1)
            kmeans.fit(centroids)
            label = kmeans.predict(features)[0]
            return ["Scorer", "Playmaker", "Rebounder"][label]
        except Exception as e:
            logger.error(f" MEDUSA ERROR: archetype prediction: {e}", exc_info=True)
            return "Unknown"


# ------------------------------
# 🧙♂️ STRATEGY ENGINE (ENHANCED)
# ------------------------------
# --- Define ELEMENTAL_STRATEGIES before it is used ---
ELEMENTAL_STRATEGIES = {
    "fire": {
    "base_weights": {
    "OFF_RATING": 0.3,
    "PACE": 0.2,
    "PTS": 0.2,
    "FG3_PCT": 0.1,
    "TOV": 0.1,
    "FTM": 0.1,
    },
    "dynamic_adjustments": {
    "hot_streak": {
    "threshold": 1.05,
    "adjustments": {"OFF_RATING": 0.1, "PACE": 0.05},
    },
    "run_and_gun": {
    "threshold": 102.0,
    "adjustments": {"PACE": 0.1, "TOV": -0.05},
    },
    },
    },
    "earth": {
    "base_weights": {
    "REB": 0.4,
    "OREB_PCT": 0.2,
    "PTS_PAINT": 0.2,
    "OPP_PTS_2ND_CHANCE": 0.1,
    "BLK": 0.1,
    },
    "dynamic_adjustments": {
    "cold_snap": {
    "threshold": 0.95,
    "adjustments": {"REB": 0.1, "OREB_PCT": 0.05},
    },
    },
    },
    "water": {
    "base_weights": {
    "DEF_RATING": 0.3,
    "STL": 0.2,
    "BLK": 0.2,
    "OPP_DEFRTG_DIFF": 0.2,
    "AST_RATIO": 0.1,
    },
    "dynamic_adjustments": {
    "hot_streak": {
    "threshold": 1.05,
    "adjustments": {"DEF_RATING": 0.1, "STL": 0.05},
    },
    },
    },
    "air": {
    "base_weights": {
    "AST_RATIO": 0.3,
    "PACE": 0.2,
    "EFG_PCT": 0.2,
    "OFF_RATING": 0.2,
    "TOV": 0.1,
    },
    "dynamic_adjustments": {
    "run_and_gun": {
    "threshold": 102.0,
    "adjustments": {"PACE": 0.1, "AST_RATIO": 0.05},
    },
    },
    },
}

class StrategyEngine:
    def __init__(self, db_connector: BasketballDataConnector):
        """
 Initializes the strategy engine, linking it to the NBA database connector
 and setting up data structures for player profiles and team stats.
        """
        if not isinstance(db_connector, BasketballDataConnector):
            raise TypeError(
            f"Expected NBADataConnector, but received {type(db_connector)}"
            )

        self.db = db_connector
        self.player_profiles: Dict[int, PlayerProfile] = {}
        self.team_stats: Dict[str, float] = {}
        self.opponent_stats: Dict[str, float] = {}

        logger.info(
        "StrategyEngine initialized. Data loading managed dynamically by WarCouncilApp.run()"
        )

    def _get_generic_opponent_stats(self) -> Dict[str, float]:
        """Provides a set of generic/average NBA opponent stats as a fallback."""
        generic_stats = {
        "DEF_RATING": 110.0,
        "OFF_RATING": 110.0,
        "PTS": 110.0,
        "PTS_PAINT": 48.0,
        "REB": 43.0,
        "TOV": 14.0,
        "PACE": 98.0,
        "EFG_PCT": 0.52,
        "OPP_DEFRTG_DIFF": 0.0,
        "OPP_PTS_2ND_CHANCE": 0.0,
        "STL": 7.0,
        "BLK": 5.0,
        "OREB_PCT": 0.25,
        "FT_PCT": 0.75,
        "AST_RATIO": 0.18,
        "FG3_PCT": 0.35,
        "FTM": 15.0, # Added more stats for completeness based on ELEMENTAL_STRATEGIES
        }
        logger.info(" MEDUSA VAULT: Returning generic opponent stats as a fallback.")
        return generic_stats

    def calculate_dynamic_weights(
        self,
        element: str,
        recent_games: List[Dict],
        team_stats: Dict[str, float],
        opponent_stats: Dict[str, float],
    ) -> Dict[str, float]:
        """
 Calculates context-aware weights for strategy evaluation based on team, opponent, and recent performance.
        """

        if element not in ELEMENTAL_STRATEGIES:
            logger.error(f"Invalid elemental strategy: {element}")
            raise ValueError(f"'{element}' is not a recognized elemental strategy.")


        calculator = DynamicWeightCalculator(
        team_stats=team_stats,
        recent_games=recent_games,
        opponent_stats=opponent_stats,
        )

        weights = calculator.get_weights(element)


        return weights

    @oracle_focus
    def simulate_lineup_change(
        self,
        team_abbreviation: str,
        player_out_id: int,
        player_in_id: int,
        player_elemental_profiles: Dict[int, PlayerProfile],
        current_team_stats: Dict[str, float],
        opponent_stats: Dict[str, float],
    ) -> Dict[str, Any]:
        """
 Simulates a lineup change and evaluates its elemental impact.
 Requires pre-loaded player profiles, team stats, and opponent stats.
        """
        player_out_name = (
        self.db._get_player_name(player_out_id) or f"ID:{player_out_id}"
        )
        player_in_name = self.db._get_player_name(player_in_id) or f"ID:{player_in_id}"

        self.player_profiles = player_elemental_profiles
        self.team_stats = current_team_stats
        self.opponent_stats = opponent_stats

        if (
        player_out_id not in self.player_profiles
        or player_in_id not in self.player_profiles
        ):
            logger.error(
            f"Player profile missing: {player_out_name if player_out_id not in self.player_profiles else player_in_name} "
            f"({player_out_id if player_out_id not in self.player_profiles else player_in_id})"
            )
            raise ValueError(
            f"One or both players not found in team roster or profiles."
            )

        try:
            # Assuming db.query_recent_games exists in BasketballDataConnector
            recent_games = self.db.query_recent_games(
            team_abbreviation, lookback_days=7
            ) or [self.team_stats]
        except Exception as e:
            logger.error(
            f" TITAN PROCESSING FAILED: fetch recent games for team {team_abbreviation}: {e}",
            exc_info=True,
            )
            recent_games = [self.team_stats]

        impact_results = {}
        optimal_strategy = None
        max_advantage = -float("inf")

        for element_name in ELEMENTAL_STRATEGIES:
            try:
                weights = self.calculate_dynamic_weights(
                element_name, recent_games, current_team_stats, opponent_stats
                )
                impact = self._calculate_elemental_impact(
                player_out_id, player_in_id, element_name, weights
                )
                impact_results[element_name] = impact
                if impact["net_advantage"] > max_advantage:
                    max_advantage = impact["net_advantage"]
                    optimal_strategy = element_name
            except Exception as e:
                logger.error(
                f"Error calculating elemental impact for '{element_name}': {e}",
                exc_info=True,
                )
                impact_results[element_name] = {
                "affinity_change": 0.0,
                "stat_impact": 0.0,
                "net_advantage": -1000.0,
                "weights_used": {},
                }

        return {
        "elemental_impacts": impact_results,
        "optimal_strategy": optimal_strategy or "No Clear Optimal Strategy",
        "projected_advantage": max_advantage if optimal_strategy else 0.0,
        "timestamp": datetime.now().isoformat(),
        }

    def _calculate_elemental_impact(
        self,
        player_out_id: int,
        player_in_id: int,
        element: str,
        weights: Dict[str, float],
    ) -> Dict[str, float]:
        """Core elemental impact calculation engine."""
        affinity_attribute = f"{element.lower()}_affinity"
        if not hasattr(
        self.player_profiles[player_out_id], affinity_attribute
        ) or not hasattr(self.player_profiles[player_in_id], affinity_attribute):
            logger.error(
            f"Player elemental affinity attribute '{affinity_attribute}' not found for impact calculation."
            )
            return {
            "affinity_change": 0.0,
            "stat_impact": 0.0,
            "net_advantage": 0.0,
            "weights_used": weights,
            }
        out_affinity = getattr(self.player_profiles[player_out_id], affinity_attribute)
        in_affinity = getattr(self.player_profiles[player_in_id], affinity_attribute)
        affinity_delta = in_affinity - out_affinity
        stat_impact = 0.0
        for stat, weight in weights.items():
            # Calculate stat impact by iterating through weights and fetching relevant opponent or team stats
            # Prioritize opponent stats for defensive metrics, otherwise use team stats or default to 0.0
            if "OPP_" in stat:
                # Handle 'OPP_DEFRTG_DIFF' - this is a calculated stat, not directly from opponent.
                # If it's a specific calculated diff, use the team_stats or calculate it.
                # Otherwise, assume it's an opponent's raw stat.
                if stat == "OPP_DEFRTG_DIFF":
                    # This implies DEF_RATING - OFF_RATING for the OPPONENT.
                    # We might not have this directly in self.opponent_stats, so use opponent's raw if available.
                    stat_value = self.opponent_stats.get(
                    "DEF_RATING", 0.0
                    ) - self.opponent_stats.get("OFF_RATING", 0.0)
                else:
                    stat_value = self.opponent_stats.get(
                    stat.replace("OPP_", ""), self.opponent_stats.get(stat, 0.0)
                    )
            else:
                stat_value = self.team_stats.get(stat, 0.0)
            stat_impact += weight * stat_value

        return {
        "affinity_change": affinity_delta,
        "stat_impact": stat_impact,
        "net_advantage": affinity_delta * stat_impact,
        "weights_used": weights,
        }


class DynamicWeightCalculator:
    """Real-time weight adjustment system based on game context."""

    def __init__(
        self,
        team_stats: Dict[str, float],
        recent_games: List[Dict[str, float]],
        opponent_stats: Dict[str, float],
    ):
        self.team_stats = team_stats
        self.opponent_stats = opponent_stats
        self.recent_avg = self._calculate_recent_averages(recent_games)

    def get_weights(self, element: str) -> Dict[str, float]:
        if element not in ELEMENTAL_STRATEGIES:
            logger.error(f"Invalid elemental strategy requested: {element}")
            raise ValueError(f"'{element}' is not a valid elemental strategy.")

        config = ELEMENTAL_STRATEGIES[element]
        weights = config["base_weights"].copy()

        for adj_name, rules in config.get("dynamic_adjustments", {}).items():
            try:
                condition_met = False
                if adj_name == "hot_streak":
                    condition_met = self._is_on_hot_streak(rules["threshold"])
                elif adj_name == "cold_snap":
                    condition_met = self._is_on_cold_snap(rules["threshold"])
                elif adj_name == "run_and_gun":
                    condition_met = self._is_run_and_gun(rules["threshold"])
                elif "condition" in rules:
                    condition_met = rules["condition"](
                    self.team_stats, self.opponent_stats
                    )
                if condition_met:
                    self._apply_adjustments(weights, rules["adjustments"])
            except Exception as e:
                logger.warning(
                f"Error applying dynamic adjustment '{adj_name}' for '{element}': {e}",
                exc_info=True,
                )
        return self._normalize_weights(weights) # Return after loop, outside try-except

    def _is_on_hot_streak(self, threshold: float) -> bool:
        recent_off = self.recent_avg.get("OFF_RATING", 0)
        season_off = self.team_stats.get("OFF_RATING", 0)
        return season_off > 0 and recent_off > (season_off * threshold)

    def _is_on_cold_snap(self, threshold: float) -> bool:
        recent_def = self.recent_avg.get("DEF_RATING", 0)
        season_def = self.team_stats.get("DEF_RATING", 0)
        return season_def > 0 and recent_def < (season_def * threshold)

    def _is_run_and_gun(self, threshold: float) -> bool:
        recent_pace = self.recent_avg.get("PACE", 0)
        return recent_pace > threshold

    def _apply_adjustments(
        self, weights: Dict[str, float], adjustments: Dict[str, float]
    ):
        for stat, change in adjustments.items():
            weights[stat] = weights.get(stat, 0.0) + change

    def _normalize_weights(self, weights: Dict[str, float]) -> Dict[str, float]:
        total = sum(weights.values())
        if total == 0:
            logger.warning(
            "Attempted to normalize weights that sum to zero. Returning original weights."
            )
            return weights
        return {k: v / total for k, v in weights.items()}

    def _calculate_recent_averages(
        self, games: List[Dict[str, float]]
    ) -> Dict[str, float]:
        if not games:
            logger.info(
            "No recent games available. Using season team stats as fallback."
            )
            return self.team_stats.copy()
        all_keys = set(self.team_stats.keys())
        for game in games:
            all_keys.update(game.keys())
        recent_avg_stats = {}
        for stat in all_keys:
            values = [game.get(stat, self.team_stats.get(stat, 0.0)) for game in games]
            numeric_values = [v for v in values if isinstance(v, (int, float))]
            if numeric_values:
                recent_avg_stats[stat] = np.mean(numeric_values)
            else:
                recent_avg_stats[stat] = self.team_stats.get(stat, 0.0)
        return recent_avg_stats


# --- STUB: WarCouncilSimulator ---
class WarCouncilSimulator_Expert:
    """
 Expert-Level War Council Simulator

 Analogy Mapping:
 - Medusa (The Queen): Final decision-maker, receives only the unified report from the War Council.
 - War Council (Chief Advisor): Central coordinator, gathers all insights from the right hands and Town Folks, prepares recommendations for Medusa.
 - Right Hands (Neural Contacts & Basketball Intelligence Cortex): Specialist modules that process raw data and provide insights to the War Council.
 - Town Folks: Other modules, users, or services that provide feedback, requests, or results to the War Council, never touching raw data directly.

 Responsibilities:
 - Aggregate all expert and Town Folks input.
 - Prepare a unified council recommendation.
 - Present the recommendation to Medusa (the Queen).
 - Log all deliberations, disagreements, and Town Folks input for transparency.
    """
 
    def __init__(self, config: Optional[WarCouncilConfig] = None):
        """
 Initialize the Expert War Council system
 
 Args:
 config: Configuration for the war council system
        """
        logger.info(" Initializing WarCouncilSimulator_Expert...")
 
        self.config = config or WarCouncilConfig()
 
        # Initialize expert spires if available
        if EXPERT_SPIRES_AVAILABLE:
            self._initialize_expert_spires()
        else:
            logger.error(" MEDUSA ERROR: Expert spires not available - using degraded mode")
            self._initialize_fallback_spires()
 
        # Initialize supporting systems
        self._initialize_supporting_systems()
        # Performance tracking
        self.prediction_history: List[ExpertPrediction] = []
        self.performance_metrics: Dict[str, List[float]] = {}
        self.rolling_performance = collections.deque(maxlen=100)  # For meta-learning
        self.ensemble_weight_history = []
        self.council_minutes_log = []
        self.last_ground_truth_eval = None
 
        # Threading for async operations
        self.executor = ThreadPoolExecutor(max_workers=8)
        logger.info(" MEDUSA VAULT: WarCouncilSimulator_Expert initialized successfully")
 
        # Collaboration enhancements attributes
        self.shared_context = {}
        self.last_advisor_disagreement = None
        self.collaboration_log = []
        self.town_folk_inputs: List[Dict] = []  # Store all Town Folks' input
        self.council_recommendations: List[Dict] = []  # Store all recommendations presented to Medusa
 
    def _initialize_expert_spires(self):
        """Initialize all expert-level cognitive spires"""
        logger.info(" MEDUSA VAULT: Initializing Expert Cognitive Spires...")
 
        try: # Use CognitiveSpiresFactory_Expert for proper initialization
            self.spires_factory = CognitiveSpiresFactory_Expert()
 
            # Initialize all spires through the factory synchronously
            initialization_status = self.spires_factory.initialize_all_spires_sync()
 
            # Get initialized spires
            all_spires = self.spires_factory.get_all_spires()
 
            # Assign spires to instance variables
            self.chronos_oracle = all_spires.get('chronos_fatigue')
            self.athena_engine = all_spires.get('athena_strategy')
            self.ares_oracle = all_spires.get('ares_defense')
            self.nike_oracle = all_spires.get('nike_victory')
            self.metis_oracle = all_spires.get('metis_wisdom')
            self.prophecy_orchestrator = all_spires.get('prophecy_orchestrator')
            # Remove or comment out direct references to DivineModelForge_Expert and HephaestusForge_Expert
            # self.hephaestus_forge = all_spires.get('hephaestus_forge')
            # self.divine_forge = all_spires.get('divine_model_forge')
            self.serpent_weave = all_spires.get('serpent_weave')
            self.fate_forge = all_spires.get('fate_forge')
            self.fate_weaver = all_spires.get('fate_weaver')
            self.gorgon_weave = all_spires.get('gorgon_weave')
            self.olympian_council = all_spires.get('olympian_council')
            self.prometheus_realm = all_spires.get('prometheus_realm')
            self.heroic_weaver = all_spires.get('heroic_deed_weaver')
 
            successful_spires = sum(1 for spire in all_spires.values() if spire is not None)
            successful_initializations = sum(initialization_status.values())
 
            logger.info(f" Expert spires initialized via factory: {successful_spires}/{len(all_spires)} available")
            logger.info(f" Spire initialization status: {successful_initializations}/{len(initialization_status)} successful")
 
            # Log details of failed spires
            failed_spires = [name for name, success in initialization_status.items() if not success]
            if failed_spires:
                logger.warning(f" TITAN PROCESSING FAILED: initialize spires: {failed_spires}")
 
        except Exception as e:
            logger.error(f" TITAN PROCESSING FAILED: initialize expert spires: {e}")
            self._initialize_fallback_spires()
 
    def _initialize_fallback_spires(self):
        """Initialize fallback/mock spires if expert spires fail"""
        logger.warning(" Initializing fallback spire system...")
        # Create minimal mock spires
        self.chronos_oracle = MockSpire("Chronos")
        self.athena_engine = MockSpire("Athena")
        self.ares_oracle = MockSpire("Ares")
        self.nike_oracle = MockSpire("Nike")
        self.metis_oracle = MockSpire("Metis")
        self.olympian_council = MockSpire("OlympianCouncil")
        self.serpent_weave = MockSpire("SerpentWeave") # Add fallback for serpent_weave
        self.heroic_weaver = MockSpire("HeroicWeaver") # Add fallback for heroic_weaver
        self.fate_weaver = MockSpire("FateWeaver") # Add fallback for fate_weaver
 
        logger.warning(" Using mock spires - predictions will be degraded")
 
    def _initialize_supporting_systems(self):
        """Initialize supporting analytics and data systems"""
        # Initialize data analytics components
        self.team_analytics = TeamOlympianAnalytics()
        self.hoops_pantheon = HoopsPantheon()
 
        # Initialize memory and archive systems with proper config
        oracle_config = {
        "memory_path": "prod_data/oracle_memory.db",
        "max_entries": 10000
        }
        self.oracle_memory = OracleMemory(oracle_config)
        # Initialize archive keeper with proper dependencies
 
        vault_config = VaultLoader.load_config()
        quantum_entangler = QuantumEntanglementManager(config=vault_config)
 
        self.archive_keeper = MnemosyneArchiveKeeper(
        config=vault_config,
        scroll_path="./prod_data/war_council_archive.db",
        quantum_entangler=quantum_entangler
        )
 
        # Initialize data connector
        self.data_connector = BasketballDataConnector()
 
 
    async def predict_game_async(self,
    game_context: GameContext,
    detailed_analysis: bool = True) -> ExpertPrediction:
        """
 Generate comprehensive expert prediction for a game
 
 Args:
 game_context: Game context and metadata
 detailed_analysis: Whether to perform detailed analysis
 
 Returns:
 ExpertPrediction with comprehensive results
        """
        logger.info(f" Generating expert prediction for {game_context.away_team} @ {game_context.home_team}")
 
        prediction_id = f"pred_{game_context.titan_clash_id}_{int(datetime.now().timestamp())}"
 
        # Gather team data in parallel
        team_data_tasks = [
        self._analyze_team_async(game_context.home_team, game_context, is_home=True),
        self._analyze_team_async(game_context.away_team, game_context, is_home=False)
        ]
 
        home_analysis, away_analysis = await asyncio.gather(*team_data_tasks)
 
        # Run all expert spire predictions in parallel
        spire_predictions = await self._run_all_spires_async(
        game_context, home_analysis, away_analysis
        )
 
        # Orchestrate ensemble prediction
        ensemble_result = await self._orchestrate_ensemble_async(
        spire_predictions, game_context, home_analysis, away_analysis
        )
 
        # Apply fate weaving adjustments
        if self.config.enable_fate_weaving and hasattr(self, 'fate_weaver'):
            weaving_result = await self._apply_fate_weaving_async(
            ensemble_result, game_context, home_analysis, away_analysis
            )
        else:
            weaving_result = ensemble_result
 
        # Create comprehensive prediction
        expert_prediction = ExpertPrediction(
        prediction_id=prediction_id,
        game_context=game_context,
        home_team_analysis=home_analysis,
        away_team_analysis=away_analysis,
        primary_prediction=weaving_result['final_prediction'],
        confidence=weaving_result['final_confidence'],
        uncertainty=weaving_result.get('uncertainty', 0.3),
        contributing_spires=list(spire_predictions.keys()),
        spire_predictions=spire_predictions,
        ensemble_weights=weaving_result.get('ensemble_weights', {}),
        key_factors=self._extract_key_factors(spire_predictions, weaving_result),
        risk_assessment=self._assess_risk(weaving_result),
        recommendations=self._generate_recommendations(weaving_result, home_analysis, away_analysis),
        quality_score=self._calculate_quality_score(weaving_result, spire_predictions)
        )
 
        # Store prediction
        self.prediction_history.append(expert_prediction)
 
        # --- Meta-learning, self-tuning, explainability, and self-eval hooks ---
        self._update_performance_metrics(expert_prediction)
        self._meta_learn_and_self_tune()
        weights = self._scenario_aware_weighting(game_context)
        self._log_council_minutes(expert_prediction, spire_predictions, weights)
 
        logger.info(f"✨ Expert prediction complete: {expert_prediction.primary_prediction:.3f} confidence: {expert_prediction.confidence:.3f}")
        return expert_prediction
 
    async def _analyze_team_async(self,
    mythic_roster_id: str,
    game_context: GameContext,
    is_home: bool) -> TeamAnalysis:
        """Analyze team comprehensively"""
 
        try:
            # Get team essence and recent performance
            team_essence = self.team_analytics.get_team_essence(mythic_roster_id)
            elemental_analysis = self.team_analytics.elemental_analysis(mythic_roster_id)
            olympian_bonds = self.team_analytics.calculate_olympian_bonds(mythic_roster_id)
 
            # Extract team name
            # Find team name from all_nba_teams using mythic_roster_id (string ID)
            team_name_info = next((t for t in self.team_analytics.all_nba_teams if str(t["id"]) == mythic_roster_id), None)
            team_name = team_name_info.get('full_name', f'Team_{mythic_roster_id}') if team_name_info else f'Team_{mythic_roster_id}'
 
            # Calculate fatigue level
            fatigue_level = self._calculate_team_fatigue(mythic_roster_id, game_context)
 
            # Calculate momentum
            momentum_score = self._calculate_team_momentum(mythic_roster_id)
 
            # Assess injury impact
            injury_impact = self._assess_injury_impact(mythic_roster_id)
 
            # Calculate chemistry
            chemistry_rating = olympian_bonds.get('chronosync', 0.7)
            fatigue_level = self._calculate_team_fatigue(mythic_roster_id, game_context)
 
            # Calculate momentum
            momentum_score = self._calculate_team_momentum(mythic_roster_id)
 
            # Assess injury impact
            injury_impact = self._assess_injury_impact(mythic_roster_id)
 
            # Calculate chemistry
            chemistry_rating = olympian_bonds.get('chronosync', 0.7)
 
            # Get recent form
            recent_form = [0.5] * 10 # Placeholder: last 10 games as neutral form
 
            # Matchup advantages (simplified)
            matchup_advantages = self._analyze_matchup_advantages(mythic_roster_id, game_context)
 
            # Confidence factors (simplified)
            confidence_factors = {
            "fatigue": 1 - fatigue_level,
            "momentum": momentum_score,
            "injury_impact": 1 - injury_impact,
            "chemistry_bonus": (chemistry_rating - 0.5) * 0.1
            }
 
            return TeamAnalysis(
                mythic_roster_id=mythic_roster_id,
                team_name=team_name,
                               primary_strengths=elemental_analysis.get('primary_element', 'Balanced'),
                weaknesses=elemental_analysis.get('elemental_weakness', 'None identified'),
                fatigue_level=fatigue_level,
                momentum_score=momentum_score,
                injury_impact=injury_impact,
                chemistry_rating=chemistry_rating,
                recent_form=recent_form,
                matchup_advantages=matchup_advantages,
                confidence_factors=confidence_factors

            )
       

        except Exception as e:
            logger.error(f" Error analyzing team {mythic_roster_id}: {e}")
            pass  # Add pass to satisfy try block

        # Return fallback analysis
        return TeamAnalysis(
            mythic_roster_id=mythic_roster_id,
            team_name=f'Team_{mythic_roster_id}',
            primary_strengths=['Unknown'],
            weaknesses=['Analysis failed'],
            fatigue_level=0.5,
            momentum_score=0.0,
            injury_impact=0.1,
            chemistry_rating=0.7,
            recent_form=[0.5] * 10,
            matchup_advantages={},
            confidence_factors={'error_mode': -0.2}
        )
 
    async def _run_all_spires_async(self,
    game_context: GameContext,
    home_analysis: TeamAnalysis,
    away_analysis: TeamAnalysis) -> Dict[str, Dict[str, float]]:
        """Run all expert spires in parallel"""
 
        spire_tasks = []
        spire_names = []
 
        # Core spires
        if hasattr(self, 'chronos_oracle') and self.config.enable_fatigue_analysis:
            spire_tasks.append(self._run_chronos_async(game_context, home_analysis, away_analysis))
            spire_names.append('chronos')
 
        if hasattr(self, 'athena_engine') and self.config.enable_strategy_analysis:
            spire_tasks.append(self._run_athena_async(game_context, home_analysis, away_analysis))
            spire_names.append('athena')
 
        if hasattr(self, 'ares_oracle') and self.config.enable_defense_analysis:
            spire_tasks.append(self._run_ares_async(game_context, home_analysis, away_analysis))
            spire_names.append('ares')
 
        if hasattr(self, 'nike_oracle') and self.config.enable_victory_analysis:
            spire_tasks.append(self._run_nike_async(game_context, home_analysis, away_analysis))
            spire_names.append('nike')
 
        if hasattr(self, 'metis_oracle') and self.config.enable_wisdom_validation:
            spire_tasks.append(self._run_metis_async(game_context, home_analysis, away_analysis))
            spire_names.append('metis')
 
        # Advanced spires
        if hasattr(self, 'serpent_weave') and self.config.enable_pattern_recognition:
            spire_tasks.append(self._run_serpent_weave_async(game_context, home_analysis, away_analysis))
            spire_names.append('serpent_weave')
 
        if hasattr(self, 'heroic_weaver') and self.config.enable_pattern_recognition:
            spire_tasks.append(self._run_heroic_weaver_async(game_context, home_analysis, away_analysis))
            spire_names.append('heroic_weaver')
 
        # Run all spires
        spire_results = await asyncio.gather(*spire_tasks, return_exceptions=True)
        spire_predictions = {}
        for name, result in zip(spire_names, spire_results):
            if isinstance(result, Exception):
                logger.error(f" Spire {name} failed: {result}")
                spire_predictions[name] = {'prediction': 0.5, 'confidence': 0.1, 'error': str(result)}
            else:
                spire_predictions[name] = result
                # --- Feedback wiring: send feedback if confidence is low ---
                if result.get('confidence', 1.0) < 0.3:
                    feedback = FeatureFeedback(name, result, result.get('confidence', 0.0), message="Low confidence from spire. Requesting feature improvement.")
                    feature_alchemist.receive_feedback(feedback)
                    # --- Unified feedback: also send to UnifiedRetrainer ---
                    unified_retrainer.receive_feedback({
                        'source': name,
                        'type': 'low_confidence',
                        'confidence': result.get('confidence', 0.0),
                        'details': result,
                        'trigger_retrain': True if result.get('confidence', 0.0) < 0.15 else False
                    })
        return spire_predictions
 
    async def _run_chronos_async(self, game_context, home_analysis, away_analysis):
        """Run Chronos fatigue analysis"""
        loop = asyncio.get_event_loop()
 
        def run_chronos():
            # Prepare fatigue data
            fatigue_data = {
            'home_fatigue': home_analysis.fatigue_level,
            'away_fatigue': away_analysis.fatigue_level,
            'game_context': game_context.__dict__
            }
 
            # Call the synchronous predict method of the spire
            return self.chronos_oracle.predict(fatigue_data)
 
        return await loop.run_in_executor(self.executor, run_chronos)
 
    async def _run_athena_async(self, game_context, home_analysis, away_analysis):
        """Run Athena strategy analysis"""
        loop = asyncio.get_event_loop()
 
        def run_athena():
            strategy_data = {
            'home_strengths': home_analysis.primary_strengths,
            'away_strengths': away_analysis.primary_strengths,
            'matchup_advantages': home_analysis.matchup_advantages,
            'game_context': game_context.__dict__
            }
            return self.athena_engine.predict(strategy_data)
 
        return await loop.run_in_executor(self.executor, run_athena)
 
    async def _run_ares_async(self, game_context, home_analysis, away_analysis):
        """Run Ares defense analysis"""
        loop = asyncio.get_event_loop()
 
        def run_ares():
            defense_data = {
            'home_defensive_rating': home_analysis.confidence_factors.get('defense', 0.5),
            'away_defensive_rating': away_analysis.confidence_factors.get('defense', 0.5),
            'game_context': game_context.__dict__
            }
            return self.ares_oracle.predict(defense_data)
 
        return await loop.run_in_executor(self.executor, run_ares)
 
    async def _run_nike_async(self, game_context, home_analysis, away_analysis):
        """Run Nike victory analysis"""
        loop = asyncio.get_event_loop()
 
        def run_nike():
            victory_data = {
            'home_momentum': home_analysis.momentum_score,
            'away_momentum': away_analysis.momentum_score,
            'home_form': home_analysis.recent_form,
            'away_form': away_analysis.recent_form,
            'game_context': game_context.__dict__
            }
            return self.nike_oracle.predict(victory_data)
 
        return await loop.run_in_executor(self.executor, run_nike)
 
    async def _run_metis_async(self, game_context, home_analysis, away_analysis):
        """Run Metis wisdom validation"""
        loop = asyncio.get_event_loop()
 
        def run_metis():
            wisdom_data = {
            'home_analysis': home_analysis.__dict__,
            'away_analysis': away_analysis.__dict__,
            'game_context': game_context.__dict__
            }
            return self.metis_oracle.predict(wisdom_data)
 
        return await loop.run_in_executor(self.executor, run_metis)
 
    async def _run_serpent_weave_async(self, game_context, home_analysis, away_analysis):
        """Run SerpentWeave pattern analysis"""
        loop = asyncio.get_event_loop()
 
        def run_serpent():
            pattern_data = {
            'home_patterns': home_analysis.recent_form,
            'away_patterns': away_analysis.recent_form,
            'game_context': game_context.__dict__
            }
            return self.serpent_weave.predict(pattern_data)
 
        return await loop.run_in_executor(self.executor, run_serpent)
 
    async def _run_heroic_weaver_async(self, game_context, home_analysis, away_analysis):
        """Run HeroicDeedWeaver analysis"""
        loop = asyncio.get_event_loop()
 
        def run_heroic():
            heroic_data = {
            'home_chemistry': home_analysis.chemistry_rating,
            'away_chemistry': away_analysis.chemistry_rating,
            'game_context': game_context.__dict__
            }
            return self.heroic_weaver.predict(heroic_data)
 
        return await loop.run_in_executor(self.executor, run_heroic)
 
    async def _orchestrate_ensemble_async(self,
    spire_predictions: Dict[str, Dict[str, float]],
    game_context: GameContext,
    home_analysis: TeamAnalysis,
    away_analysis: TeamAnalysis) -> Dict[str, Any]:
        """Orchestrate ensemble prediction from all spires"""
 
        if hasattr(self, 'olympian_council'):
            # Use OlympianCouncil for ensemble
            loop = asyncio.get_event_loop()
 
            def run_ensemble():
                ensemble_data = {
                'spire_predictions': spire_predictions,
                'game_context': game_context.__dict__,
                'home_analysis': home_analysis.__dict__,
                'away_analysis': away_analysis.__dict__
                }
                return self.olympian_council.predict(ensemble_data)
 
            result = await loop.run_in_executor(self.executor, run_ensemble)
            # --- Feedback wiring: send feedback if ensemble confidence is low ---
            if result.get('final_confidence', 1.0) < 0.3:
                feedback = FeatureFeedback('WarCouncilEnsemble', result, result.get('final_confidence', 0.0), message="Low ensemble confidence. Requesting feature improvement.")
                feature_alchemist.receive_feedback(feedback)
                # --- Unified feedback: also send to UnifiedRetrainer ---
                unified_retrainer.receive_feedback({
                    'source': 'WarCouncilEnsemble',
                    'type': 'low_ensemble_confidence',
                    'confidence': result.get('final_confidence', 0.0),
                    'details': result,
                    'trigger_retrain': True if result.get('final_confidence', 0.0) < 0.15 else False
                })
            return result
        else:
            # Fallback ensemble calculation
            predictions = []
            weights = []
 
            for spire_name, result in spire_predictions.items():
                if 'prediction' in result and 'confidence' in result:
                    predictions.append(result['prediction'])
                    weights.append(result['confidence'])
 
            if predictions:
                # Weighted average
                weights_array = np.array(weights)
                # Avoid division by zero if all weights sum to zero
                sum_weights = np.sum(weights_array)
                if sum_weights == 0:
                    weights_normalized = np.ones_like(weights_array) / len(weights_array) # Equal weighting if all confidence is 0
                else:
                    weights_normalized = weights_array / sum_weights
                
                final_prediction = np.average(predictions, weights=weights_normalized)
                final_confidence = np.mean(weights)
            else:
                final_prediction = 0.5
                final_confidence = 0.1
 
            return {
            'final_prediction': final_prediction,
            'final_confidence': final_confidence,
            'ensemble_weights': dict(zip(spire_predictions.keys(), weights_normalized if predictions else []))
            }
 
    async def _apply_fate_weaving_async(self,
    ensemble_result: Dict[str, Any],
    game_context: GameContext,
    home_analysis: TeamAnalysis,
    away_analysis: TeamAnalysis) -> Dict[str, Any]:
        """Apply fate weaving adjustments"""
 
        loop = asyncio.get_event_loop()
 
        def apply_weaving():
            base_prediction = {
            'confidence': ensemble_result['final_confidence'],
            'prediction': ensemble_result['final_prediction']
            }
 
            weaving_context = {
            'game_context': game_context.__dict__,
            'home_analysis': home_analysis.__dict__,
            'away_analysis': away_analysis.__dict__
            }
 
            # Apply fate weaving (this would be async in real implementation)
            # Ensure fate_weaver is initialized and has 'weave_fate' method
            if hasattr(self, 'fate_weaver') and callable(getattr(self.fate_weaver, 'weave_fate', None)):
                # Mock weave_fate return type to match expected structure
                weave_fate_result = self.fate_weaver.weave_fate(base_prediction, weaving_context)
                if isinstance(weave_fate_result, dict): # Check if it's a dict and convert to object
                    class FateWeaveResult:
                        def __init__(self, data):
                            self.__dict__.update(data)
                    adjustment = FateWeaveResult(weave_fate_result)
                else: # Assume it's already an object
                    adjustment = weave_fate_result
            else:
                logger.warning("FateWeaver or weave_fate method not available. No fate adjustments applied.")
                class DefaultAdjustment:
                    overall_confidence = base_prediction['confidence']
                    uncertainty_level = 0.3
                    __dict__ = {'overall_confidence': overall_confidence, 'uncertainty_level': uncertainty_level}
                adjustment = DefaultAdjustment()
 
            return {
            **ensemble_result,
            'final_confidence': adjustment.overall_confidence,
            'uncertainty': adjustment.uncertainty_level,
            'fate_adjustments': adjustment.__dict__
            }
 
        return await loop.run_in_executor(self.executor, apply_weaving)
 
    def _calculate_team_fatigue(self, mythic_roster_id: str, game_context: GameContext) -> float:
        """Calculate team fatigue level"""
        # Simplified fatigue calculation
        rest_days = game_context.rest_days_home if mythic_roster_id == game_context.home_team else game_context.rest_days_away
        back_to_back = game_context.is_back_to_back
 
        fatigue = 0.3 # Base fatigue
        if back_to_back:
            fatigue += 0.3
        fatigue -= rest_days * 0.1
 
        return max(0.0, min(1.0, fatigue))
 
    def _calculate_team_momentum(self, mythic_roster_id: str) -> float:
        """Calculate team momentum score"""
        # Simplified momentum calculation
        return np.random.uniform(-0.2, 0.2) # Placeholder
 
    def _assess_injury_impact(self, mythic_roster_id: str) -> float:
        """Assess injury impact on team"""
        # Simplified injury assessment
        return np.random.uniform(0.0, 0.3) # Placeholder
 
    def _get_recent_form(self, mythic_roster_id: str) -> List[float]:
        """Get recent team form"""
        # Simplified form calculation
        return [np.random.uniform(0.3, 0.7) for _ in range(10)] # Placeholder
 
    def _analyze_matchup_advantages(self, mythic_roster_id: str, game_context: GameContext) -> Dict[str, float]:
        """Analyze matchup advantages"""
        # Simplified matchup analysis
        return {
        'offensive_advantage': np.random.uniform(-0.1, 0.1),
        'defensive_advantage': np.random.uniform(-0.1, 0.1),
        'rebounding_advantage': np.random.uniform(-0.1, 0.1)
        }
 
    def _calculate_rest_advantage(self, mythic_roster_id: str, game_context: GameContext) -> float:
        """Calculate rest advantage"""
        rest_days = game_context.rest_days_home if mythic_roster_id == game_context.home_team else game_context.rest_days_away
        return min(0.05, rest_days * 0.01)
 
    def _extract_key_factors(self, spire_predictions: Dict, ensemble_result: Dict) -> List[str]:
        """Extract key prediction factors"""
        factors = []
 
        # Analyze spire contributions
        for spire_name, result in spire_predictions.items():
            confidence = result.get('confidence', 0)
            if confidence > 0.7:
                factors.append(f"High confidence from {spire_name}")
            elif confidence < 0.3:
                factors.append(f"Low confidence from {spire_name}")
 
        # Add ensemble factors
        final_confidence = ensemble_result.get('final_confidence', 0.5)
        if final_confidence > 0.8:
            factors.append("High ensemble confidence")
        elif final_confidence < 0.4:
            factors.append("Low ensemble confidence")
 
        return factors[:5] # Top 5 factors
 
    def _assess_risk(self, ensemble_result: Dict) -> str:
        """Assess prediction risk level"""
        confidence = ensemble_result.get('final_confidence', 0.5)
        uncertainty = ensemble_result.get('uncertainty', 0.3)
 
        if uncertainty > 0.4 or confidence < 0.3:
            return "HIGH"
        elif uncertainty > 0.25 or confidence < 0.5:
            return "MEDIUM"
        else:
            return "LOW"
 
    def _generate_recommendations(self, ensemble_result: Dict,
    home_analysis: TeamAnalysis,
    away_analysis: TeamAnalysis) -> List[str]:
        """Generate actionable recommendations"""
        recommendations = []
 
        confidence = ensemble_result.get('final_confidence', 0.5)
        prediction = ensemble_result.get('final_prediction', 0.5)
 
        if confidence < 0.4:
            recommendations.append("Low confidence - consider avoiding this bet")
 
        if prediction > 0.6:
            recommendations.append("Favor home team based on analysis")
        elif prediction < 0.4:
            recommendations.append("Favor away team based on analysis")
        else:
            recommendations.append("Game appears closely matched")
 
        # Add fatigue-based recommendations
        if home_analysis.fatigue_level > 0.7:
            recommendations.append("Home team shows high fatigue")
        if away_analysis.fatigue_level > 0.7:
            recommendations.append("Away team shows high fatigue")
 
        return recommendations[:5] # Top 5 recommendations
 
    def _calculate_quality_score(self, ensemble_result: Dict, spire_predictions: Dict) -> float:
        """Calculate overall prediction quality score"""
        confidence = ensemble_result.get('final_confidence', 0.5)
        uncertainty = ensemble_result.get('uncertainty', 0.3)
 
        # Count successful spire predictions
        # Check if result has 'confidence' key before accessing it
        successful_spires = sum(1 for result in spire_predictions.values() if result.get('confidence', 0) > 0.3)
        spire_success_rate = successful_spires / max(len(spire_predictions), 1)
 
        # Combine factors
        quality = (confidence * 0.4 + (1 - uncertainty) * 0.3 + spire_success_rate * 0.3)
        return max(0.0, min(1.0, quality))
 
    def simulate(self, games: List[GameContext]) -> List[ExpertPrediction]:
        """
 Simulate multiple games synchronously
 
 Args:
 games: List of game contexts to predict
 
 Returns:
 List of expert predictions
        """
        logger.info(f"🎮 Simulating {len(games)} games...")
 
        predictions = []
        for game in games:
            try:
                # Run async prediction in sync context
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                prediction = loop.run_until_complete(self.predict_game_async(game))
                predictions.append(prediction)
                loop.close()
            except Exception as e:
                logger.error(f" TITAN PROCESSING FAILED: predict game {game.titan_clash_id}: {e}")
 
        logger.info(f" Simulation complete: {len(predictions)} predictions generated")
        return predictions
 
    def get_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report"""
 
        if not self.prediction_history:
            return {"error": "No prediction history available"}
 
        recent_predictions = self.prediction_history[-50:] # Last 50 predictions
 
        # Calculate metrics
        avg_confidence = np.mean([p.confidence for p in recent_predictions])
        avg_uncertainty = np.mean([p.uncertainty for p in recent_predictions])
        avg_quality = np.mean([p.quality_score for p in recent_predictions])
 
        # Risk distribution
        risk_counts = {}
        for prediction in recent_predictions:
            risk = prediction.risk_assessment
            risk_counts[risk] = risk_counts.get(risk, 0) + 1
 
        # Spire contribution analysis
        spire_contributions = {}
        for prediction in recent_predictions:
            for spire in prediction.contributing_spires:
                spire_contributions[spire] = spire_contributions.get(spire, 0) + 1
 
        report = {
        'total_predictions': len(self.prediction_history),
        'recent_window': len(recent_predictions),
        'average_confidence': avg_confidence,
        'average_uncertainty': avg_uncertainty,
        'average_quality': avg_quality,
        'risk_distribution': risk_counts,
        'spire_contributions': spire_contributions,
        'expert_spires_available': EXPERT_SPIRES_AVAILABLE,
        'config': self.config.__dict__,
        'timestamp': datetime.now().isoformat() # Convert datetime to string for consistent output
        }
 
        return report

    def _meta_learn_and_self_tune(self):
        """
        Meta-learning and self-tuning logic for the War Council.
        - Analyzes recent prediction performance.
        - Queries the unified drift detector for drift/anomaly signals.
        - Triggers feedback/self-healing if degradation or drift is detected.
        """
        # Analyze rolling performance (e.g., accuracy, confidence, error rates)
        if not hasattr(self, 'prediction_history') or len(self.prediction_history) < 10:
            return  # Not enough data for meta-learning
        recent_preds = self.prediction_history[-20:]
        confidences = [p.confidence for p in recent_preds]
        qualities = [p.quality_score for p in recent_preds]
        avg_conf = sum(confidences) / len(confidences)
        avg_quality = sum(qualities) / len(qualities)

        # Example: If confidence or quality drops below threshold, check for drift
        if avg_conf < 0.5 or avg_quality < 0.5:
            logger.warning(f"[MetaLearning] Detected performance drop: avg_conf={avg_conf:.2f}, avg_quality={avg_quality:.2f}")
            # Query drift detector (assumes registration and attribute)
            if hasattr(self, '_feedback_participants'):
                for participant in self._feedback_participants:
                    if hasattr(participant, 'detect_drift'):
                        drift_result = participant.detect_drift(qualities)
                        if drift_result.get('drift'):
                            logger.warning(f"[MetaLearning] Drift detected by {participant.__class__.__name__}: {drift_result}")
                            # Trigger feedback for self-healing
                            self.send_feedback('drift_detected', {'drift_score': drift_result.get('drift_score', 0)})
                            break
            # Optionally, trigger retraining or threshold tuning
            self.send_feedback('performance_drop', {'avg_conf': avg_conf, 'avg_quality': avg_quality})
            # --- Unified feedback: also send to UnifiedRetrainer for meta-learning ---
            unified_retrainer.receive_feedback({
                'source': 'WarCouncilMetaLearning',
                'type': 'performance_drop',
                'avg_conf': avg_conf,
                'avg_quality': avg_quality,
                'trigger_retrain': avg_conf < 0.3 or avg_quality < 0.3
            })

        # Optionally, auto-tune ensemble weights or thresholds here
        # ... (custom logic can be added)

    def _collaboration_module(self, neural_result, expert_result, game_context):
        """
        Implements hybrid ensemble, cross-advisor feedback, consensus/disagreement detection,
        explainability bridge, scenario routing, joint feedback, and shared context.
        """
        # 1. Hybrid Ensemble with Dynamic Weighting
        scenario = getattr(game_context, 'scenario_type', 'default')
        neural_weight = 0.5
        expert_weight = 0.5
        if scenario == 'novel' or neural_result.get('explainability', 0) < 0.3:
            expert_weight = 0.7
            neural_weight = 0.3
        elif scenario == 'data_rich':
            neural_weight = 0.7
            expert_weight = 0.3
        # 2. Cross-Advisor Feedback
        if 'explanation' in neural_result and not neural_result['explanation']:
            expert_result['feedback_on_neural'] = 'Neural advisor lacks explanation.'
        if 'explanation' in expert_result and not expert_result['explanation']:
            neural_result['feedback_on_expert'] = 'Expert advisor lacks explanation.'
        # 3. Consensus and Disagreement Detection
        disagreement = abs(neural_result['prediction'] - expert_result['prediction']) > 0.3
        if disagreement:
            self.last_advisor_disagreement = {
                'neural': neural_result,
                'expert': expert_result,
                'context': game_context
            }
            self.collaboration_log.append({'type': 'disagreement', 'details': self.last_advisor_disagreement})
        # 4. Explainability Bridge
        if 'explanation' in neural_result and 'tags' in expert_result:
            neural_result['translated_tags'] = expert_result['tags']
        # 5. Scenario Routing
        if scenario == 'edge_case':
            final_advisor = expert_result
        elif scenario == 'bulk':
            final_advisor = neural_result
        else:
            # 1. Hybrid ensemble
            final_prediction = neural_weight * neural_result['prediction'] + expert_weight * expert_result['prediction']
            final_confidence = (neural_weight * neural_result.get('confidence', 0.5) + expert_weight * expert_result.get('confidence', 0.5))
            final_advisor = {
                'prediction': final_prediction,
                'confidence': final_confidence,
                'neural': neural_result,
                'expert': expert_result
            }
        # 6. Joint Feedback to UnifiedRetrainer
        if disagreement and (neural_result.get('confidence', 1.0) < 0.3 or expert_result.get('confidence', 1.0) < 0.3):
            unified_retrainer.receive_feedback({
                'source': 'WarCouncilCollaboration',
                'type': 'advisor_disagreement',
                'neural': neural_result,
                'expert': expert_result,
                'trigger_retrain': True
            })
        # 7. Shared Memory/Context
        self.shared_context['last_results'] = {'neural': neural_result, 'expert': expert_result}
        return final_advisor

    def generate_council_report(self, recommendation: dict, raw_data: dict, dissent: list = None, evidence: dict = None) -> dict:
        """
        Prepare a full council report for Medusa (the Queen), including:
        - The unified recommendation
        - The underlying raw data used for deliberation
        - Any dissenting opinions
        - Supporting evidence or references
        """
        report = {
            "recommendation": recommendation,
            "raw_data": raw_data,
            "dissent": dissent or [],
            "evidence": evidence or {},
            "timestamp": datetime.now().isoformat(),
        }
        # Log the report for transparency
        self.council_recommendations.append(report)
        logger.info("Council report generated for Medusa, including raw data and dissent.")
        return report

# Example usage (to be called wherever the council presents its output to Medusa):
# council_report = self.generate_council_report(recommendation, raw_data, dissent, evidence)
# Medusa (the Queen) can now review both the summary and the underlying data.

# --- Feedback integration example ---
# Assume FeatureFeedback and SelfLearningFeatureAlchemist are imported from feature_alchemist.py
# from src.features.feature_alchemist import FeatureFeedback, SelfLearningFeatureAlchemist

# Example: Cognitive Spire feedback
# def spire_predict(self, features, feature_alchemist):
#     prediction = self.model.predict(features)
#     performance = self.evaluate_performance(prediction)
#     if performance < 0.5:
#         feedback = FeatureFeedback(self.name, features, performance, message="Need new features!")
#         feature_alchemist.receive_feedback(feedback)
#     return prediction

# Example: War Council feedback (after collecting all spire results)
# def review_spire_performance(self, spire_results, feature_alchemist):
#     for spire_name, result in spire_results.items():
#         if result['confidence'] < 0.3:
#             feedback = FeatureFeedback(spire_name, result['features'], result['confidence'], message="Repeated low confidence. Please help.")
#             feature_alchemist.receive_feedback(feedback)

if __name__ == "__main__":
    # Example usage and testing
 
    # Create sample game context
    sample_game = GameContext(
    titan_clash_id="2025061501",
    home_team="1610612737", # Atlanta Hawks
    away_team="1610612738", # Boston Celtics
    game_date=datetime.now(),
    season="2024-25",
    is_playoff=False,
    rest_days_home=2,
    rest_days_away=1
    )
 
    # Initialize expert system
    config = WarCouncilConfig(async_processing=True, enable_monitoring=True)
    war_council = WarCouncilSimulator_Expert(config)
 
    # Import feedback participants

    # Instantiate cortexes (assuming default constructors; adjust if needed)
    neural_cortex = NeuralBasketballCore()
    basketball_cortex = CognitiveBasketballCortex()

    # Register all feedback participants with the War Council
    war_council.register_feedback_participant(unified_temporal_anomaly_detector)
    war_council.register_feedback_participant(feature_alchemist)
    war_council.register_feedback_participant(neural_cortex)
    war_council.register_feedback_participant(basketball_cortex)
 
    # Generate prediction
    try:
        prediction = asyncio.run(war_council.predict_game_async(sample_game))

        for rec in prediction.recommendations:
            print(f"Recommendation: {rec}")

    except Exception as e:
        traceback.print_exc() # Print full traceback for debugging
 
    # Generate performance report
    report = war_council.get_performance_report()
    for key, value in report.items():
        if key != 'config':
            print(f"{key}: {value}")

    # Example usage of UnifiedModelForge
    unified_forge = get_unified_model_forge()
    model = unified_forge.forge_model('hephaestus', input_dim=64, model_type='transformer')
