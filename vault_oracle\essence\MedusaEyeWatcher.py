
#!/usr/bin/env python3
"""
MedusaEyeWatcher.py v3.1
--------------------------
The All-Seeing Eye - Quantum Vision Upgrade.
Observes external data sources (NBA/WNBA), detects cosmic omens,
predicts cosmic storms, and monitors cognitive spire vitality.
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Optional, List, Tuple, Any
import aiohttp
from dataclasses import dataclass, field
from src.schemas.api_models import CosmicStorm
import os
import sys
from aiohttp import ClientSession
import numpy as np
from scipy.stats import zscore
from src.cognitive_spires.CognitiveSpiresFactory_Expert import CognitiveSpiresFactory_Expert




try: 
    import scipy.stats
except ImportError:
    raise ImportError(
        "scipy.stats is required for MedusaEyeWatcher. Please install scipy."
    )

# Add project root to sys.path for local imports if needed
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "../../"))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try: from vault_oracle.core.oracle_focus import oracle_focus
except ImportError:
    raise ImportError(
        "oracle_focus is required for MedusaEyeWatcher. Please ensure vault_oracle.core.oracle_focus is available."
    )

# Import Expert Messaging components for integration
try: 
    from vault_oracle.interfaces.expert_messaging_orchestrator import (
        ExpertMessagingOrchestrator,
        UnifiedMessage,
        MessageType,
        MessagePriority,
        DeliveryChannel
    )
    EXPERT_MESSAGING_AVAILABLE = True
except ImportError:
    EXPERT_MESSAGING_AVAILABLE = False
    # Create dummy classes to prevent import errors
    class ExpertMessagingOrchestrator: pass
    class UnifiedMessage: pass
    class MessageType: pass
    class MessagePriority: pass
    class DeliveryChannel: pass

logging.basicConfig(
    level=logging.INFO,
    format="\U0001f441\ufe0f %(asctime)s \u134fC %(levelname)s \u134fD %(message)s", # Custom format for MedusaEye
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger("MedusaEyeWatcher")
if not logger.handlers:
    handler = logging.StreamHandler(sys.stdout)
    handler.setFormatter(
        logging.Formatter(
            "\U0001f441\ufe0f %(asctime)s \u134fC %(levelname)s \u134fD %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )
    )
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)


class MedusaEyeWatcher:
    """👁️ The All-Seeing Eye v3.1 - Quantum Vision Upgrade"""
    @oracle_focus
    def __init__(self, oracle, prophecy_forge=None, data_source=None, scan_interval: int = 30):
        """
        Initializes the Medusa Eye Watcher.

        Args:
            oracle: An instance of the Oracle for dispatching alerts.
            prophecy_forge: An instance of the Prophecy Forge for predictions (optional - expert spires preferred).
            data_source: An instance of the Data Source.
            scan_interval: The interval in seconds between scan cycles.
        """
        # === Quantum Configuration ===
        self.scan_interval = scan_interval
        self.oracle = oracle # Instance of the Oracle
        self.session: Optional[ClientSession] = None # aiohttp ClientSession
        self.prophecy_forge = prophecy_forge # Instance of Prophecy Forge (legacy compatibility)
        self.data_source = data_source # Instance of Data Source

        # Expert spires for modern functionality
        self.expert_spires = None
        self.use_expert_spires = False

        self.archetype_history: Dict[str, List[str]] = (
            {}
        ) # Track player archetype evolution {hero_id: [archetype_history]}

        # === Divine Metrics 2.0 ===
        self.prophecy_depth: int = 0 # Cumulative omen count
        self.storm_counter: int = 0 # Total cosmic storms detected
        self.network_failures: int = 0 # Consecutive network failure count
        self.last_omen_time: Optional[datetime] = (
            None # Timestamp of the last detected omen
        )
        # Realm influence tracking, initialized with balanced values
        self.realm_balance: Dict[str, float] = {"nba": 0.5, "wnba": 0.5}

        # === Quantum Archives ===
        self.dream_archive: List[Any] = [] # Placeholder for archived data/dreams
        self.storms: List[CosmicStorm] = [] # Archive of detected cosmic storms
        self.lock = (
            asyncio.Lock()
        ) # Lock for protecting shared state (e.g., storms list)

        # New storm classification system: (min_omen_count, min_intensity)
        self.storm_classes: Dict[str, Tuple[int, float]] = {
            "alpha": (5, 0.5),
            "beta": (10, 0.7),
            "gamma": (15, 0.9),
        }

        # === Enhanced Realm Scanners ===
        # Mapping realm names to their respective async fetch functions
        self.realm_scanners: Dict[str, callable] = {
            "nba": self._fetch_quantum_nba_data,
            "wnba": self._fetch_quantum_wnba_data,
        }

        # === Divine Constants 2.0 ===
        self.MAX_NETWORK_FAILURES: int = (
            3 # Maximum consecutive network failures before reset
        )
        self.STORM_THRESHOLDS: Dict[str, int] = { # Omen count thresholds for storms
            "base": 5,
            "volatile": 3, # Threshold during high-urgency periods (emotional state)
        }
        self.MOOD_DECAY_RATES: Dict[str, float] = (
            { # Rate at which emotional state decays
                "calm": 0.05,
                "alert": 0.1,
                "excitement": 0.15,
            }
        )

        # === Emotional State Engine ===
        # State machine defining valid transitions between emotional states
        self._emotional_state_machine: Dict[str, List[str]] = {
            "calm": ["alert", "serene"],
            "alert": ["excitement", "concern", "calm"],
            "excitement": [
                "alert",
                "overload",
            ], # 'overload' state not fully implemented in snippet
            "concern": ["alert", "calm"],
        }
        self.emotional_state: str = "calm" # Current emotional state of the watcher
        self.urgency_level: float = 1.0 # Current urgency level (influences thresholds)

        # === Logger ===
        self.logger = logging.getLogger("MedusaEyeWatcher")

        # Initialize expert spires if available
        self._initialize_expert_spires()

    @oracle_focus
    def _initialize_expert_spires(self):
        """Initialize expert cognitive spires for modern functionality"""
        try:
            self.spires_factory = CognitiveSpiresFactory_Expert()

            # Initialize archetype and prediction spires
            spire_configs = {
                'heroic_archetype_model': {'enabled': True},
                'expert_archetype_assigner': {'enabled': True},
                'prophecy_orchestrator': {'enabled': True},
                'olympian_council': {'enabled': True}
            }

            for spire_name, config in spire_configs.items():
                try:
                    spire = self.spires_factory.create_spire_sync(spire_name, config)
                    if spire:
                        setattr(self, spire_name, spire)
                        self.logger.info(f" Expert spire initialized: {spire_name}")
                except Exception as e:
                    self.logger.warning(f" TITAN PROCESSING FAILED: initialize {spire_name}: {e}")
                    setattr(self, spire_name, None)

            # Check if we have enough expert spires to use them
            expert_spires_available = sum(1 for spire_name in spire_configs.keys()
                                        if getattr(self, spire_name, None) is not None)

            self.use_expert_spires = expert_spires_available >= 2
            if self.use_expert_spires:
                self.logger.info(" MEDUSA VAULT: 🌟 Expert spires enabled for enhanced functionality")
            else:
                self.logger.warning(" Limited expert spires - falling back to legacy prophecy_forge")

        except ImportError:
            self.logger.warning(" Expert spires not available - using legacy prophecy_forge")
            self.use_expert_spires = False
        except Exception as e:
            self.logger.error(f" Expert spires initialization failed: {e}")
            self.use_expert_spires = False

    @oracle_focus
    async def initialize_expert_messaging(self):
        """Initialize the Expert Messaging Orchestrator for enhanced alerting"""
        if EXPERT_MESSAGING_AVAILABLE:
            try:
                self.messaging_orchestrator = ExpertMessagingOrchestrator()
                await self.messaging_orchestrator.initialize_services()
                self.logger.info(" MEDUSA VAULT: 📡 Expert Messaging integration initialized for MedusaEye")
            except Exception as e:
                self.logger.warning(f" TITAN PROCESSING FAILED: initialize Expert Messaging: {e}")
                self.messaging_orchestrator = None
        else:
            self.logger.warning(" Expert Messaging not available - using fallback alerts only")

    @oracle_focus
    async def _fetch_quantum_wnba_data(self, session: ClientSession) -> Dict:
        """Quantum-enhanced WNBA scanner with error correction"""
        url = "https://api.divine-wnba.com/quantum-live"
        try:
            async with session.get(url) as response:
                response.raise_for_status()
                data = await response.json()
                # Real correction step could be added here if needed
                return data
        except aiohttp.ClientError as e:
            self.logger.warning(
                f"\U0001f30c Quantum WNBA interference fetching from {url}: {e}"
            )
            self.network_failures += 1
            return {}
        except Exception as e:
            self.logger.error(
                f"Unexpected error fetching WNBA data from {url}: {e}", exc_info=True
            )
            self.network_failures += 1
            return {}

    @oracle_focus
    async def _fetch_quantum_nba_data(self, session: ClientSession) -> Dict:
        """Temporal-stabilized NBA scanner"""
        url = "https://api.divine-nba.com/temporal-live"
        try:
            async with session.get(url) as response:
                response.raise_for_status()
                data = await response.json()
                # Real normalization step could be added here if needed
                return data
        except aiohttp.ClientError as e:
            self.logger.warning(f"\u26a1 NBA temporal rift fetching from {url}: {e}")
            self.network_failures += 1
            return {}
        except Exception as e:
            self.logger.error(
                f"Unexpected error fetching NBA data from {url}: {e}", exc_info=True
            )
            self.network_failures += 1
            return {}

    @oracle_focus
    async def scan_mortal_world(self, session: ClientSession) -> Dict:
        """
        Scans data from all configured realms.

        Args:
            session: The aiohttp ClientSession to use.

        Returns:
            A dictionary containing fetched data from each realm.
        """
        logger.info(" MEDUSA VAULT: Scanning mortal realms...")
        all_realm_data: Dict[str, Dict] = {}
        tasks = []
        for realm, scanner_func in self.realm_scanners.items():
            tasks.append(asyncio.create_task(scanner_func(session)))

        # Gather results from all scanning tasks
        results = await asyncio.gather(
            *tasks, return_exceptions=True
        ) # Return exceptions to avoid crashing on one failure

        for i, realm in enumerate(self.realm_scanners.keys()):
            result = results[i]
            if isinstance(result, Exception):
                self.logger.error(f" TITAN PROCESSING FAILED: scan realm '{realm}': {result}")
                all_realm_data[realm] = {} # Ensure realm key exists even on failure
            else:
                all_realm_data[realm] = result

        logger.info(" MEDUSA VAULT: Mortal realm scan complete.")
        return all_realm_data

    @oracle_focus
    async def detect_omens(self, events: Dict) -> Tuple[bool, Dict]:
        """
        Quantum Omen Detection with Multi-Realm Analysis.

        Analyzes data from multiple realms to detect significant omens
        and assess storm potential.

        Args:
            events: A dictionary containing event data from different realms.

        Returns:
            A tuple: (omens_detected, analysis_results)
            omens_detected: True if any omens were detected, False otherwise.
            analysis_results: A dictionary containing detailed analysis results.
        """
        logger.info(" MEDUSA VAULT: Detecting omens...")
        analysis = {
            "omens": 0,
            "realm_impact": {"nba": 0.0, "wnba": 0.0},
            "storm_potential": 0.0,
            "detected_omens_details": [], # Added for detailed omen info
        }

        # Multi-Realm Analysis
        for realm in ["nba", "wnba"]: # Iterate through expected realms
            realm_events = events.get(realm, {})
            if realm_events: # Only analyze if data exists for the realm
                realm_analysis = await self._analyze_realm(
                    realm, realm_events
                ) # Pass realm name
                analysis["omens"] += realm_analysis["omens"]
                analysis["realm_impact"][realm] = realm_analysis["impact"]
                analysis["storm_potential"] += realm_analysis["storm_potential"]
                analysis["detected_omens_details"].extend(
                    realm_analysis["detected_omens_details"]
                ) # Extend details

        # Archetype Shift Detection (Cross-realm)
        cross_realm_shift_detected, shift_details = (
            self._detect_cross_realm_archetype_shifts(events)
        )
        if cross_realm_shift_detected:
            analysis["omens"] += 1
            # Placeholder for OMEN_THRESHOLDS, assuming it's a class attribute
            # self.OMEN_THRESHOLDS is not defined in this class. Assuming it exists or mocking.
            analysis["storm_potential"] += 0.5 # Default weight if OMEN_THRESHOLDS is missing
            analysis["detected_omens_details"].append(
                {"type": "cross_realm_archetype_shift", "details": shift_details}
            )

        # Update Cosmic Balance based on realm impact
        self._update_realm_balance(analysis["realm_impact"])

        # Storm Prediction
        # Use the appropriate storm threshold based on emotional state (urgency level)
        current_storm_threshold = (
            self.STORM_THRESHOLDS["volatile"]
            if self.urgency_level > 1.0
            else self.STORM_THRESHOLDS["base"]
        )
        if (
            analysis["omens"] >= current_storm_threshold
            and analysis["storm_potential"] >= 1.0
        ): # Check both omen count and potential
            await self._predict_cosmic_storm(analysis)

        omens_detected = analysis["omens"] > 0
        if omens_detected:
            self.last_omen_time = datetime.now() # Update last omen time
            self.logger.info(
                f"Omens detected: {analysis['omens']}. Storm Potential: {analysis['storm_potential']:.2f}"
            )

            # Send omen detection alert through expert messaging if available
            if EXPERT_MESSAGING_AVAILABLE and analysis["detected_omens_details"]:
                await self.send_omen_detection_alert(analysis["detected_omens_details"])

        else:
            self.logger.info(" MEDUSA VAULT: No significant omens detected.")

        return omens_detected, analysis

    @oracle_focus
    async def _analyze_realm(self, realm_name: str, realm_data: Dict) -> Dict:
        """
        Deep analysis of a single realm's data to identify omens.

        Args:
            realm_name: The name of the realm being analyzed.
            realm_data: The data dictionary for the realm.

        Returns:
            A dictionary containing analysis results for the realm.
        """
        analysis = {
            "omens": 0,
            "impact": 0.0,
            "storm_potential": 0.0,
            "detected_omens_details": [],
        }

        # Injury Analysis
        injury_omens_count = 0
        significant_injuries = []
        for player in realm_data.get("players", []):
            if self._is_significant_injury(player):
                injury_omens_count += 1
                significant_injuries.append(player.get("id", "unknown"))

        if injury_omens_count > 0:
            analysis["omens"] += injury_omens_count
            # Placeholder for OMEN_THRESHOLDS, assuming it's a class attribute
            # self.OMEN_THRESHOLDS is not defined in this class. Assuming it exists or mocking.
            analysis["impact"] += (
                injury_omens_count * 0.2
            ) # Default weight if OMEN_THRESHOLDS is missing
            # Storm potential calculation based on omen count and weight
            analysis["storm_potential"] += (
                injury_omens_count * 0.2
            ) # Default weight if OMEN_THRESHOLDS is missing
            analysis["detected_omens_details"].append(
                {
                    "type": "injury",
                    "realm": realm_name,
                    "count": injury_omens_count,
                    "players": significant_injuries,
                }
            )
            self.logger.info(
                f"Detected {injury_omens_count} injury omens in {realm_name}."
            )

        # Betting Pattern Analysis (Line Move)
        odds_data = realm_data.get("odds", {})
        line_move = abs(odds_data.get("movement", 0))
        # Placeholder for OMEN_THRESHOLDS
        line_move_threshold = 0.05 # Default threshold
        if hasattr(self, 'OMEN_THRESHOLDS') and "line_move" in self.OMEN_THRESHOLDS:
            line_move_threshold = self.OMEN_THRESHOLDS["line_move"]["threshold"]

        if line_move > line_move_threshold:
            analysis["omens"] += 1
            # Impact and storm potential based on line move magnitude and weight
            line_move_weight = 0.3 # Default weight
            if hasattr(self, 'OMEN_THRESHOLDS') and "line_move" in self.OMEN_THRESHOLDS:
                line_move_weight = self.OMEN_THRESHOLDS["line_move"]["weight"]

            line_move_impact = line_move * line_move_weight
            analysis["impact"] += line_move_impact
            analysis[
                "storm_potential"
            ] += line_move_impact # Use impact for storm potential contribution
            analysis["detected_omens_details"].append(
                {"type": "line_move", "realm": realm_name, "movement": line_move}
            )
            self.logger.info(
                f"Detected significant line move in {realm_name}: {line_move}."
            )

        # Volume Analysis (Volume Surge)
        volume_data = realm_data.get(
            "volume_analysis", {}
        ) # Assuming volume analysis is nested
        volume_zscore = volume_data.get(
            "zscore", 0
        ) # Assuming zscore is provided or calculated elsewhere

        # Placeholder for OMEN_THRESHOLDS
        volume_surge_threshold = 2.0 # Default threshold
        if hasattr(self, 'OMEN_THRESHOLDS') and "volume_surge" in self.OMEN_THRESHOLDS:
            volume_surge_threshold = self.OMEN_THRESHOLDS["volume_surge"]["threshold"]

        if volume_zscore > volume_surge_threshold:
            analysis["omens"] += 1
            # Impact and storm potential based on volume zscore and weight
            volume_surge_weight = 0.4 # Default weight
            if hasattr(self, 'OMEN_THRESHOLDS') and "volume_surge" in self.OMEN_THRESHOLDS:
                volume_surge_weight = self.OMEN_THRESHOLDS["volume_surge"]["weight"]

            volume_impact = (
                volume_zscore * volume_surge_weight
            )
            analysis["impact"] += volume_impact
            analysis[
                "storm_potential"
            ] += volume_impact # Use impact for storm potential contribution
            analysis["detected_omens_details"].append(
                {"type": "volume_surge", "realm": realm_name, "zscore": volume_zscore}
            )
            self.logger.info(
                f"Detected significant volume surge in {realm_name}: Z-score {volume_zscore}."
            )

        self.logger.info(
            f"Realm analysis complete for {realm_name}. Omens: {analysis['omens']}, Impact: {analysis['impact']:.2f}, Storm Potential: {analysis['storm_potential']:.2f}"
        )

        # Send realm analysis update through expert messaging if available
        if EXPERT_MESSAGING_AVAILABLE and analysis["omens"] > 0:
            await self.send_realm_analysis_update(realm_name, analysis)

        return analysis

    @oracle_focus
    def _is_significant_injury(self, player: Dict) -> bool:
        """
        Determine if an injury is significant enough to be considered an omen.
        Checks player status and potential archetype impact.

        Args:
            player: Dictionary containing player data.

        Returns:
            True if the injury is significant, False otherwise.
        """
        # Base significance: player status is 'out' or similar
        base_significance = player.get("status", "").lower() in [
            "out",
            "injured",
            "doubtful",
        ]
        hero_id = player.get("id")
        archetype = player.get("archetype")

        if base_significance and hero_id and archetype: # Check if archetype impact is above threshold
            try:
                # Use expert archetype assigner if available
                if self.use_expert_spires and hasattr(self, 'expert_archetype_assigner') and self.expert_archetype_assigner:
                    # Expert spire method (placeholder - implement based on actual interface)
                    current_impact = self._calculate_expert_archetype_impact(archetype)
                elif self.prophecy_forge and hasattr(self.prophecy_forge, 'calculate_archetype_impact'):
                    # Legacy prophecy forge method
                    current_impact = self.prophecy_forge.calculate_archetype_impact(archetype)
                else:
                    # Fallback calculation
                    current_impact = self._fallback_archetype_impact(archetype)

                # Placeholder for OMEN_THRESHOLDS, assuming it's a class attribute
                injury_threshold = 0.7 # Default threshold
                if hasattr(self, 'OMEN_THRESHOLDS') and "injury" in self.OMEN_THRESHOLDS:
                    injury_threshold = self.OMEN_THRESHOLDS["injury"]["threshold"]

                if (
                    current_impact > injury_threshold
                ): # Using injury threshold for impact
                    self.logger.info(
                        f"Significant injury detected for player {hero_id} (Archetype: {archetype}, Impact: {current_impact:.2f})."
                    )
                    return True
            except Exception as e:
                self.logger.error(
                    f" TITAN PROCESSING FAILED: calculate archetype impact for player {hero_id}: {e}"
                )
                # Decide whether to consider it significant on error - for now, return base significance
                return base_significance

        return base_significance # Return base significance if no archetype/id or calculation failed

    @oracle_focus
    def _update_emotional_state(self, new_state: str):
        """
        Quantum state transition with validation.
        Updates the emotional state of the watcher if the transition is valid.

        Args:
            new_state: The proposed new emotional state.
        """
        if new_state in self._emotional_state_machine.get(self.emotional_state, []):
            self.emotional_state = new_state
            self.logger.info(f"🌀 Emotional state transition: {self.emotional_state}")
            # Update urgency level based on new state (example logic)
            if new_state in ["alert", "excitement"]:
                self.urgency_level = 1.5 # Increase urgency
            elif new_state == "calm":
                self.urgency_level = 1.0 # Reset urgency
            # Add logic for other states if needed
        else:
            self.logger.warning(
                f"Invalid emotional transition attempted: {self.emotional_state} -> {new_state}"
            )

    @oracle_focus
    async def _predict_cosmic_storm(self, analysis: Dict):
        """
        Predict storm characteristics using quantum forecasting and archive the storm.

        Args:
            analysis: The analysis results from omen detection.
        """
        logger.critical("Initiating cosmic storm prediction sequence...")

        # Determine storm class based on omen count and storm potential
        storm_class = "alpha" # Default class
        for cls, (count_threshold, intensity_threshold) in self.storm_classes.items():
            if (
                analysis["omens"] >= count_threshold
                and analysis["storm_potential"] >= intensity_threshold
            ):
                storm_class = cls
            else:
                # Assuming storm classes are ordered by severity (alpha < beta < gamma)
                # If thresholds are not met for a higher class, break and use the current class
                break # Or continue if classes are not strictly ordered

        # Determine realm origin based on which realm had higher impact
        realm_origin = max(analysis["realm_impact"], key=analysis["realm_impact"].get)

        # Create the CosmicStorm object
        storm = CosmicStorm(
            omen_count=analysis["omens"],
            intensity=analysis["storm_potential"],
            emotional_state=self.emotional_state,
            realm_origin=realm_origin,
            storm_class=storm_class,
        )

        # Archive the storm in a thread-safe manner
        async with self.lock:
            self.storms.append(storm)
            self.storm_counter += 1
            # Reset prophecy depth after a storm (signifies a major event processed)
            self.prophecy_depth = 0

        self.logger.critical(
            f"🌪️ CLASS {storm.storm_class.upper()} STORM DETECTED! "
            f"Origin: {storm.realm_origin} Intensity: {storm.intensity:.2f} "
            f"Omens: {storm.omen_count} State: {storm.emotional_state}"
        )

        # Trigger realm balancing and dispatch alert
        await self._trigger_realm_balancing(storm)
        await self._trigger_olympian_alert(
            {"storm": storm}
        ) # Pass storm details in the identity dict

    @oracle_focus
    async def _trigger_realm_balancing(self, storm: CosmicStorm):
        """
        Balance realm influence after major storms.
        Adjusts realm balance based on storm class and origin.

        Args:
            storm: The detected CosmicStorm object.
        """
        logger.info(
            f"Triggering realm balancing after {storm.storm_class.upper()} storm from {storm.realm_origin}."
        )
        adjustment = 0.0 # Default adjustment

        # Determine adjustment amount based on storm class
        if storm.storm_class == "gamma":
            adjustment = 0.1
        elif storm.storm_class == "beta":
            adjustment = 0.05
        # Alpha storms might not require significant balancing, or a smaller adjustment

        if adjustment > 0:
            # Increase influence for the origin realm, decrease for the other
            other_realm = "wnba" if storm.realm_origin == "nba" else "nba"

            # Ensure balance stays within reasonable bounds (e.g., 0.2 to 0.8)
            self.realm_balance[storm.realm_origin] = min(
                self.realm_balance.get(storm.realm_origin, 0.5) + adjustment, 0.8
            )
            self.realm_balance[other_realm] = max(
                self.realm_balance.get(other_realm, 0.5) - adjustment, 0.2
            )
            self.logger.info(f"Realm balance adjusted: {self.realm_balance}")
        else:
            self.logger.info(
                "No significant realm balance adjustment needed for this storm class."
            )

    @oracle_focus
    def _detect_cross_realm_archetype_shifts(
        self, events: Dict
    ) -> Tuple[bool, List[Dict]]:
        """
        Detect interconnected archetype changes across realms.
        Looks for significant archetype shifts in multiple players across different realms.

        Args:
            events: A dictionary containing event data from different realms.

        Returns:
            A tuple: (shift_detected, shift_details)
            shift_detected: True if a cross-realm shift is detected, False otherwise.
            shift_details: A list of dictionaries detailing the detected shifts.
        """
        shifts_details: List[Dict] = []
        min_shifts_for_cross_realm = (
            2 # Threshold for considering it a cross-realm event
        )

        for realm in ["nba", "wnba"]:
            for player in events.get(realm, {}).get("players", []):
                hero_id = player.get("id")
                current_archetype = player.get("archetype")

                if hero_id and current_archetype:
                    # Retrieve historical archetypes for this player
                    historical_archetypes = self.archetype_history.get(hero_id, [])

                    # Check if the current archetype represents a significant shift
                    if self._is_significant_shift(
                        current_archetype, historical_archetypes
                    ):
                        shifts_details.append(
                            {
                                "realm": realm,
                                "hero_id": hero_id,
                                "current_archetype": current_archetype,
                                "historical_count": len(historical_archetypes),
                            }
                        )
                        self.logger.info(
                            f"Detected significant archetype shift for player {hero_id} in {realm}."
                        )

                    # Update archetype history for this player
                    # This should happen regardless of whether it's a "significant" shift now
                    self._update_archetype_history_for_player(
                        hero_id, current_archetype
                    )

        shift_detected = len(shifts_details) >= min_shifts_for_cross_realm
        if shift_detected:
            self.logger.info(
                f"🌉 Cross-realm archetype resonance detected: {len(shifts_details)} shifts."
            )
        else:
            self.logger.debug("No archetype shifts detected.")

        return shift_detected, shifts_details

    @oracle_focus
    def _is_significant_shift(self, current: str, historical: List[str]) -> bool:
        """
        Determines if a player's current archetype represents a significant shift
        compared to their historical archetype data using a Z-score approach.

        Args:
            current: The player's current archetype string.
            historical: A list of historical archetype strings for the player.

        Returns:
            True if the shift is significant, False otherwise.
        """
        # Need at least a few historical data points to calculate a meaningful z-score
        MIN_HISTORY_FOR_ZSCORE = 5
        if len(historical) < MIN_HISTORY_FOR_ZSCORE:
            self.logger.debug(
                f"Insufficient history ({len(historical)}) for z-score shift detection."
            )
            return False

        # Create a numerical representation of the historical data for the current archetype
        # 1 if the historical archetype matches the current one, 0 otherwise.
        frequency_representation = np.array(
            [1 if h == current else 0 for h in historical]
        )

        try:
            # Calculate the z-score for the *last* data point (the current archetype)
            # relative to the mean and std dev of the *previous* data points.
            # This checks how unusual the *current* archetype is based on past frequency.
            # Need to handle cases where the history has no variance (all same archetype).
            if len(frequency_representation) > 1:
                # Calculate z-score of the last element based on the preceding elements
                mean_past = np.mean(frequency_representation[:-1])
                std_past = np.std(frequency_representation[:-1])

                if std_past == 0:
                    # If no variance in the past, a shift occurs only if the current is different
                    is_shift = frequency_representation[-1] != mean_past
                    if is_shift:
                        self.logger.info(
                            f"Significant shift detected for current archetype '{current}' (no historical variance)."
                        )
                    return is_shift
                else:
                    z_score = (frequency_representation[-1] - mean_past) / std_past
                    # Placeholder for OMEN_THRESHOLDS, assuming it's a class attribute
                    archetype_shift_threshold = 2.0 # Default threshold
                    if hasattr(self, 'OMEN_THRESHOLDS') and "archetype_shift" in self.OMEN_THRESHOLDS:
                        archetype_shift_threshold = self.OMEN_THRESHOLDS["archetype_shift"]["threshold"]

                    self.logger.debug(
                        f"Calculated z-score for archetype shift: {z_score:.2f} (Threshold: {archetype_shift_threshold:.2f})"
                    )
                    return (
                        abs(z_score)
                        > archetype_shift_threshold
                    )
            else:
                # Only one historical data point, cannot calculate z-score relative to past
                return False # Or define a rule for single data point shifts if needed

        except Exception as e:
            self.logger.error(
                f" TITAN PROCESSING FAILED: calculate z-score for archetype shift: {e}", exc_info=True
            )
            return False # Return False on error

    @oracle_focus
    def _update_archetype_history_for_player(self, hero_id: str, archetype: str):
        """
        Updates the historical archetype list for a specific player.
        Adds the current archetype to the history.

        Args:
            hero_id: The ID of the player.
            archetype: The current archetype string.
        """
        if hero_id:
            if hero_id not in self.archetype_history:
                self.archetype_history[hero_id] = []
            self.archetype_history[hero_id].append(archetype)
            # Optional: Limit the history length to prevent excessive memory usage
            MAX_HISTORY_LENGTH = 100
            if len(self.archetype_history[hero_id]) > MAX_HISTORY_LENGTH:
                self.archetype_history[hero_id] = self.archetype_history[hero_id][
                    -MAX_HISTORY_LENGTH:
                ]
            self.logger.debug(
                f"Updated archetype history for player {hero_id}. History length: {len(self.archetype_history[hero_id])}"
            )
        else:
            logger.warning(" TITAN WARNING: Cannot update archetype history for player with no ID.")

    @oracle_focus
    async def update_cosmic_balance(self, live_data: Dict):
        """
        Quantum Archetype Analysis Engine.
        Extracts features from live data, predicts identity, and triggers alerts on shift.

        Args:
            live_data: Dictionary containing live data for analysis.
        """
        logger.info(" MEDUSA VAULT: Updating cosmic balance...")
        try:
            # Extract features from live data, including quantum/temporal aspects
            features = self._extract_quantum_features(live_data)
            if self.use_expert_spires and hasattr(self, 'prophecy_orchestrator') and self.prophecy_orchestrator:
                # Use expert spire for prediction
                identity = await self._predict_identity_expert(features)
            elif self.prophecy_forge and hasattr(self.prophecy_forge, 'quantum_predict'):
                # Legacy prophecy forge method
                identity = self.prophecy_forge.quantum_predict(features)
            else:
                # Fallback prediction
                identity = self._fallback_identity_prediction(features)


            # Detect if this represents a significant archetype shift
            hero_id = identity.get("hero_id")
            archetype = identity.get("archetype")
            if hero_id and archetype:
                historical_archetypes = self.archetype_history.get(hero_id, [])
                if self._is_significant_shift(archetype, historical_archetypes):
                    self.logger.warning(" TITAN WARNING: Cosmic archetype shift detected!")
                    await self._trigger_olympian_alert(identity)
                    self._update_archetype_history_for_player(hero_id, archetype)
            else:
                logger.warning(
                    "Could not update archetype history: missing hero_id or archetype in identity."
                )

        except Exception as e:
            self.capture_error(e, {"type": "cosmic_balance_update_failed"})
            self.logger.error(f" TITAN PROCESSING FAILED: update cosmic balance: {e}", exc_info=True)

    @oracle_focus
    def _extract_quantum_features(self, live_data: Dict) -> Dict:
        """
        Entangled feature extraction across realms.
        Combines standard features with quantum/temporal metrics.

        Args:
            live_data: Dictionary containing live data.

        Returns:
            A dictionary of extracted features.
        """
        # TODO: Replace with real quantum/temporal metrics if available
        quantum_entanglement = 0.5 # Placeholder for real metric
        temporal_consistency = 1.0 # Placeholder for real metric
        # TODO: Replace with real feature extraction logic
        standard_features = live_data.copy() if isinstance(live_data, dict) else {}
        features = {
            "quantum_entanglement": quantum_entanglement,
            "temporal_consistency": temporal_consistency,
            "standard_features": standard_features,
        }
        return features

    @oracle_focus
    async def _trigger_olympian_alert(self, identity: Dict):
        """
        Multi-spectrum divine alert system.
        Enhanced with Expert Messaging Orchestrator integration.
        """
        logger.critical("Triggering Olympian Alert!")

        # Try to get the expert messaging orchestrator
        try:
            from vault_oracle.interfaces.expert_messaging_orchestrator import (
                ExpertMessagingOrchestrator,
                UnifiedMessage,
                MessageType,
                MessagePriority,
                DeliveryChannel
            )

            # Initialize orchestrator if not already available
            if not hasattr(self, 'messaging_orchestrator'):
                self.messaging_orchestrator = ExpertMessagingOrchestrator()
                await self.messaging_orchestrator.initialize_services()

            # Construct enhanced alert message
            alert_message = (
                f" COSMIC SHIFT DETECTED!\n"
                f"Archetype: {identity.get('archetype', 'Unknown')}\n"
                f"Divine Alignment: {identity.get('deity', 'Unknown')}\n"
                f"Confidence: {identity.get('confidence', 0.0):.2f}\n"
                f"Realm Balance: {self.realm_balance}"
            )

            # Include storm status if available
            storm_data = identity.get("storm")
            storm_status_payload = None

            if storm_data and isinstance(storm_data, CosmicStorm):
                storm_status = {
                    "class": storm_data.storm_class,
                    "origin": storm_data.realm_origin,
                    "intensity": storm_data.intensity,
                    "omens": storm_data.omen_count,
                }
                alert_message += f"\nAssociated Storm: Class {storm_status['class'].upper()} from {storm_status['origin']} (Intensity: {storm_status['intensity']:.2f})"
                storm_status_payload = storm_status
            else:
                storm_status_payload = self._current_storm_status()

            # Determine message priority based on storm intensity and archetype confidence
            confidence = identity.get('confidence', 0.0)
            storm_intensity = storm_status_payload.get('intensity', 0.0) if isinstance(storm_status_payload, dict) else 0.0

            if confidence > 0.9 or storm_intensity > 0.8:
                priority = MessagePriority.EMERGENCY
            elif confidence > 0.7 or storm_intensity > 0.6:
                priority = MessagePriority.CRITICAL
            else:
                priority = MessagePriority.HIGH

            # Create unified message for expert messaging system
            unified_message = UnifiedMessage(
                message_type=MessageType.EMERGENCY_ALERT if priority == MessagePriority.EMERGENCY else MessageType.BASKETBALL_ANALYTICS,
                priority=priority,
                title=" Medusa Eye: Cosmic Shift Detected",
                body=alert_message, content={
                    "source": "medusa_eye_watcher",
                    "archetype": identity.get('archetype'),
                    "deity": identity.get('deity'),
                    "confidence": confidence,
                    "realm_balance": self.realm_balance,
                    "storm_data": storm_status_payload,
                    "emotional_state": self.emotional_state,
                    "urgency_level": self.urgency_level,
                    "prophecy_depth": self.prophecy_depth
                },
                channels=[
                    DeliveryChannel.QUANTUM_MESSENGER,
                    DeliveryChannel.FIREBASE_SERVICE,
                    DeliveryChannel.FCM_PUSH,
                    DeliveryChannel.MNEMOSYNE_LOG
                ],
                tags=["cosmic_shift", "medusa_eye", "basketball_analytics", "quantum_detection"]
            )

            # Send through expert messaging orchestrator
            delivery_results = await self.messaging_orchestrator.send_unified_message(unified_message)

            # Log delivery results
            successful_deliveries = sum(1 for result in delivery_results if result.success)
            self.logger.info(f"📡 Alert sent through {successful_deliveries}/{len(delivery_results)} channels")

        except ImportError:
            # Fallback to original oracle dispatch if expert messaging not available
            self.logger.warning(" TITAN WARNING: Expert Messaging Orchestrator not available, falling back to Oracle dispatch")
            await self._fallback_oracle_dispatch(identity, alert_message, storm_status_payload)

        except Exception as e:
            self.logger.error(f" TITAN PROCESSING FAILED: send alert through Expert Messaging: {e}")
            # Fallback to original oracle dispatch
            await self._fallback_oracle_dispatch(identity, alert_message, storm_status_payload)

    @oracle_focus
    async def _fallback_oracle_dispatch(self, identity: Dict, alert_message: str, storm_status_payload: Dict):
        """Fallback method using original oracle dispatch"""
        try:
            # Assuming self.oracle has a dispatch_alert method
            await self.oracle.dispatch_alert(
                priority="cosmic_emergency",
                message=alert_message,
                storm_data=storm_status_payload,
            )
            logger.info(" MEDUSA VAULT: Olympian Alert dispatched successfully via Oracle fallback.")
        except Exception as e:
            # Placeholder for self.capture_error if MedusaEyeWatcher instance cannot be accessed
            # in this error context, or if it's called from a different part of the system.
            # If self.capture_error needs to be called, ensure 'self' is the MedusaEyeWatcher instance.
            logger.error(f" TITAN PROCESSING FAILED: dispatch Olympian Alert via fallback: {e}", exc_info=True)
            # You might want to re-raise the exception or handle it more gracefully
            # raise self.oracle.capture_error(e, {"type": "alert_dispatch_failed"}) # Example if oracle has capture_error
            # If self.capture_error is available, use it like this:
            # self.capture_error(e, {"type": "alert_dispatch_failed"}) # This assumes 'self' is MedusaEyeWatcher

    @oracle_focus
    async def send_realm_analysis_update(self, realm_name: str, analysis_results: Dict):
        """Send realm analysis updates through the expert messaging system"""
        try:
            if hasattr(self, 'messaging_orchestrator'):
                # Create analytics message
                unified_message = UnifiedMessage(
                    message_type=MessageType.BASKETBALL_ANALYTICS,
                    priority=MessagePriority.NORMAL,
                    title=f" {realm_name.upper()} Realm Analysis Update",
                    body=f"Latest analysis results for {realm_name} realm",
                    content={
                        "source": "medusa_eye_watcher",
                        "realm": realm_name,
                        "analysis": analysis_results,
                        "timestamp": datetime.now().isoformat(),
                        "prophecy_depth": self.prophecy_depth
                    },
                    channels=[DeliveryChannel.QUANTUM_MESSENGER, DeliveryChannel.MNEMOSYNE_LOG],
                    tags=["realm_analysis", "medusa_eye", realm_name]
                )

                await self.messaging_orchestrator.send_unified_message(unified_message)

        except Exception as e:
            self.logger.error(f" TITAN PROCESSING FAILED: send realm analysis update: {e}")

    @oracle_focus
    async def send_omen_detection_alert(self, omen_details: List[Dict]):
        """Send omen detection alerts through the expert messaging system"""
        try:
            if hasattr(self, 'messaging_orchestrator') and omen_details:
                # Determine priority based on omen count and types
                omen_count = len(omen_details)
                has_critical_omens = any(
                    omen.get('type') in ['injury', 'cross_realm_archetype_shift']
                    for omen in omen_details
                )

                priority = MessagePriority.CRITICAL if has_critical_omens else MessagePriority.HIGH

                # Create omen alert message
                unified_message = UnifiedMessage(
                    message_type=MessageType.PREDICTION_ALERT,
                    priority=priority,
                    title=f" Cosmic Omens Detected ({omen_count})",
                    body=f"MedusaEye has detected {omen_count} cosmic omens requiring attention",
                    content={
                        "source": "medusa_eye_watcher",
                        "omen_count": omen_count,
                        "omen_details": omen_details,
                        "emotional_state": self.emotional_state,
                        "urgency_level": self.urgency_level,
                        "realm_balance": self.realm_balance
                    },
                    channels=[
                        DeliveryChannel.QUANTUM_MESSENGER,
                        DeliveryChannel.FIREBASE_SERVICE,
                        DeliveryChannel.MNEMOSYNE_LOG
                    ],
                    tags=["omens", "medusa_eye", "cosmic_detection"]
                )

                await self.messaging_orchestrator.send_unified_message(unified_message)

        except Exception as e:
            self.logger.error(f" TITAN PROCESSING FAILED: send omen detection alert: {e}")

    @oracle_focus
    def _calculate_expert_archetype_impact(self, archetype: str) -> float:
        """Calculate archetype impact using expert spires"""
        try:
            # Expert implementation using archetype intelligence
            archetype_weights = {
                'Zeus': 0.95, 'Poseidon': 0.90, 'Hades': 0.85,
                'Hera': 0.80, 'Athena': 0.88, 'Apollo': 0.82,
                'Artemis': 0.75, 'Ares': 0.78, 'Aphrodite': 0.70,
                'Hephaestus': 0.72, 'Demeter': 0.68, 'Dionysus': 0.65
            }
            return archetype_weights.get(archetype, 0.5)
        except Exception as e:
            self.logger.warning(f"Expert archetype impact calculation failed: {e}")
            return self._fallback_archetype_impact(archetype)

    @oracle_focus
    def _fallback_archetype_impact(self, archetype: str) -> float:
        """Fallback archetype impact calculation"""
        # Simple fallback based on archetype name characteristics
        impact_map = {
            'zeus': 0.9, 'poseidon': 0.85, 'hades': 0.8,
            'hera': 0.75, 'athena': 0.85, 'apollo': 0.8,
            'artemis': 0.7, 'ares': 0.75, 'aphrodite': 0.65,
            'hephaestus': 0.7, 'demeter': 0.6, 'dionysus': 0.6
        }
        archetype_lower = archetype.lower()
        return impact_map.get(archetype_lower, 0.5)

    @oracle_focus
    async def _predict_identity_expert(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """Predict identity using expert spires"""
        try:
            if hasattr(self, 'prophecy_orchestrator') and self.prophecy_orchestrator:
                # Use prophecy orchestrator for comprehensive prediction
                prediction_result = await self.prophecy_orchestrator.predict_async(features)

                # Convert to expected identity format
                return {
                    'hero_id': features.get('hero_id', 'unknown'),
                    'archetype': prediction_result.get('primary_archetype', 'Unknown'),
                    'confidence': prediction_result.get('confidence', 0.5),
                    'expert_prediction': True
                }
        except Exception as e:
            self.logger.warning(f"Expert identity prediction failed: {e}")

        # Fallback to simpler spire
        if hasattr(self, 'heroic_archetype_model') and self.heroic_archetype_model:
            try:
                # Assuming heroic_archetype_model has a 'fit' method that returns an object with primary_archetype and primary_confidence
                archetype_result = self.heroic_archetype_model.fit(features)
                return {
                    'hero_id': features.get('hero_id', 'unknown'),
                    'archetype': archetype_result.primary_archetype,
                    'confidence': archetype_result.primary_confidence,
                    'expert_prediction': True
                }
            except Exception as e:
                self.logger.warning(f"Heroic archetype model prediction failed: {e}")

        # Final fallback
        return self._fallback_identity_prediction(features)

    @oracle_focus
    def _fallback_identity_prediction(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback identity prediction when all else fails"""
        # Simple fallback based on available features
        hero_id = features.get('hero_id', 'unknown')

        # Basic archetype assignment based on simple rules
        archetype = 'Apollo' # Default to Apollo (balanced archetype)
        confidence = 0.3 # Low confidence for fallback

        if 'points' in features:
            points = features.get('points', 0)
            if points > 25:
                archetype = 'Zeus' # High scorer
                confidence = 0.4
            elif points > 15:
                archetype = 'Apollo' # Balanced scorer
                confidence = 0.35

        return {
            'hero_id': hero_id,
            'archetype': archetype,
            'confidence': confidence,
            'fallback_prediction': True
        }

    # Add missing _update_realm_balance method
    def _update_realm_balance(self, realm_impact: Dict[str, float]):
        """Update realm balance based on impact."""
        # Simple update: shift balance towards the realm with higher impact
        total_impact = sum(realm_impact.values())
        if total_impact > 0:
            for realm, impact in realm_impact.items():
                self.realm_balance[realm] = (self.realm_balance[realm] * 0.7) + ((impact / total_impact) * 0.3)
                # Normalize to ensure sum is 1.0 (or close to it)
                total_current_balance = sum(self.realm_balance.values())
                if total_current_balance != 0:
                    for r in self.realm_balance:
                        self.realm_balance[r] /= total_current_balance

    # Add missing _current_storm_status method
    def _current_storm_status(self) -> Dict[str, Any]:
        """Returns the status of the most recent cosmic storm, or default if none."""
        if self.storms:
            latest_storm = self.storms[-1]
            return {
                "class": latest_storm.storm_class,
                "origin": latest_storm.realm_origin,
                "intensity": latest_storm.intensity,
                "omens": latest_storm.omen_count,
                "timestamp": latest_storm.timestamp.isoformat()
            }
        return {
            "class": "none",
            "origin": "none",
            "intensity": 0.0,
            "omens": 0,
            "timestamp": datetime.now().isoformat()
        }

    # Define OMEN_THRESHOLDS as a class attribute
    OMEN_THRESHOLDS: Dict[str, Dict[str, Any]] = {
        "injury": {"threshold": 0.7, "weight": 0.2}, # Example: 0.7 impact
        "line_move": {"threshold": 0.1, "weight": 0.3}, # Example: 0.1 abs movement
        "volume_surge": {"threshold": 2.5, "weight": 0.4}, # Example: 2.5 z-score
        "archetype_shift": {"threshold": 1.5, "weight": 0.5}, # Example: 1.5 z-score
    }
