#!/usr/bin/env python3
# DIGITAL FINGERPRINT: UUID=6f7a8b9c-0d1e-2f3a-4b5c-6d7e8f9a0b1c | DATE=2025-06-26
"""
(C) 2025 Hyper Medusa Neural Vault, All Rights Reserved.
PROPRIETARY & CONFIDENTIAL — UNAUTHORIZED USE, COPYING, OR DISTRIBUTION IS STRICTLY PROHIBITED.

This file contains proprietary algorithms, business logic, and intellectual property of Hyper Medusa Neural Vault.
Use, reproduction, or disclosure by any means without explicit written permission is a violation of applicable law.
"""

"""
HYPER MEDUSA NEURAL VAULT - Celestial Dashboard Visualization Business Value Documentation
========================================================================================

celestial_dashboard.py
----------------------
Provides interactive visualizations of heroic archetypes and performance metrics for the Medusa Vault platform.

Business Value:
- Enables intuitive exploration and communication of complex analytics and player/team archetypes.
- Supports data-driven storytelling, decision-making, and stakeholder engagement.
- Accelerates insight generation and enhances the value of analytics outputs.

Extension Points for Plugins & Custom Visualizations:
-----------------------------------------------------
- Subclass `CelestialDashboard` to add new visualization or analytics logic.
- Register visualization plugins via a plugin registry or callback system.
- Add new endpoints or data sources by extending the visualization class.
- Use the config system to enable/disable features per environment.

For further details, see module-level docstrings and architecture documentation.
"""

import plotly.express as px
import pandas as pd
import plotly.graph_objects as go
from typing import List, Dict, Any, Union
import logging

# src/oracle_wells/visualization/celestial_dashboard.py

# Configure logger
logger = logging.getLogger(__name__)

def create_celestial_dashboard(archetype_data: Union[List[Dict[str, Any]], pd.DataFrame], hover_data: list = None):
    """
    Creates an interactive Plotly sunburst visualization of heroic realms (archetypes).

    Args:
    archetype_data: Data representing players/teams, including 'deity', 'archetype',
    and performance metrics like 'minutes', 'net_rating', 'pts', etc.
    Can be a list of dictionaries or a pandas DataFrame.
    hover_data: Optional list of additional columns to show on hover.
    """
    try:
        # Convert list of dicts to DataFrame if it's not already a DataFrame
        if isinstance(archetype_data, list):
            # Defensive: handle empty list edge case
            if not archetype_data:
                logger.warning("Input archetype_data is an empty list. Cannot create dashboard.")
                return
            data_df = pd.DataFrame(archetype_data)
        elif isinstance(archetype_data, pd.DataFrame):
            data_df = archetype_data
        else:
            # Edge case: invalid input type
            logger.error(f"Invalid input type for archetype_data: {type(archetype_data)}. Must be a list or DataFrame.")
            return # Exit if data format is incorrect

        if hover_data is None:
            hover_data = ["pts", "reb", "ast"]

        # Defensive: Ensure columns specified in path, values, color, hover_data exist in data_df
        required_cols = ["deity", "archetype", "minutes", "net_rating"] + hover_data
        if not all(col in data_df.columns for col in required_cols):
            missing = [col for col in required_cols if col not in data_df.columns]
            logger.error(f"Missing required columns in data for sunburst plot: {missing}")
            return # Cannot plot if required columns are missing

        # Create the sunburst plot using Plotly Express
        # path: Defines the hierarchy (Deity -> Archetype)
        # values: Column to determine the size of the segments
        # color: Column to determine the color of the segments
        # hover_data: Additional data to show when hovering over segments
        # branchvalues: How to calculate segment sizes ('total' sums up values from children)
        fig = px.sunburst(
            data_df, # Use the DataFrame
            path=["deity", "archetype"], # Hierarchy from Deity to Archetype
            values="minutes", # Use 'minutes' played for segment size
            color="net_rating", # Color segments by 'net_rating'
            hover_data=hover_data, # Show these stats on hover
            branchvalues="total", # Segment size is the sum of values in children
        )

        # Update layout for the plot
        fig.update_layout(
            title_text="Pantheon of Basketball Divinity"
        ) # Set plot title

        # Show the plot
        fig.show()
    
    except Exception as e:
        # Catch any errors during plot creation or display
        logger.error(f"Error creating celestial dashboard: {e}", exc_info=True)

# Example function to gather and visualize data
def visualize_archetypes():
    """
    Example function demonstrating how to prepare data and call the visualization function.
    In a real application, this data would come from the HeroicArchetypeModel and other sources.
    """
    # Example data representing players/teams after archetype assignment and adding metrics
    # This data structure should match what create_celestial_dashboard expects.
    archetype_data_example = [
        # Data for players assigned to Zeus / Olympian Vanguard
        {
            "deity": "Zeus",
            "archetype": "Olympian Vanguard",
            "minutes": 34,
            "net_rating": 10.5,
            "pts": 25,
            "reb": 7,
            "ast": 5,
        },
        {
            "deity": "Zeus",
            "archetype": "Olympian Vanguard",
            "minutes": 30,
            "net_rating": 9.8,
            "pts": 22,
            "reb": 8,
            "ast": 4,
        },
        # Data for players assigned to Hermes / Aether Catalyst
        {
            "deity": "Hermes",
            "archetype": "Aether Catalyst",
            "minutes": 29,
            "net_rating": 8.1,
            "pts": 20,
            "reb": 4,
            "ast": 10,
        },
        {
            "deity": "Hermes",
            "archetype": "Aether Catalyst",
            "minutes": 27,
            "net_rating": 8.5,
            "pts": 18,
            "reb": 3,
            "ast": 12,
        },
        # Data for players assigned to Atlas / Titan Enforcer
        {
            "deity": "Atlas",
            "archetype": "Titan Enforcer",
            "minutes": 32,
            "net_rating": 9.3,
            "pts": 15,
            "reb": 12,
            "ast": 2,
        },
        {
            "deity": "Atlas",
            "archetype": "Titan Enforcer",
            "minutes": 30,
            "net_rating": 9.0,
            "pts": 14,
            "reb": 11,
            "ast": 3,
        },
        # Add more example data as needed for other archetypes/deities
        {
            "deity": "Apollo",
            "archetype": "Prophetic Shooter",
            "minutes": 28,
            "net_rating": 7.5,
            "pts": 28,
            "reb": 3,
            "ast": 4,
        },
        {
            "deity": "Athena",
            "archetype": "Strategic Playmaker",
            "minutes": 33,
            "net_rating": 11.0,
            "pts": 16,
            "reb": 6,
            "ast": 11,
        },
    ]

    # Call the main visualization function with the example data
    create_celestial_dashboard(archetype_data_example)


# === Main Execution Block ===
# This block runs the example visualization when the script is executed directly.
if __name__ == "__main__":
    visualize_archetypes() # Call the example function to gather and visualize data
