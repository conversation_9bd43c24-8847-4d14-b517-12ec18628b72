import sys
import os
from datetime import datetime, timedelta
from typing import Dict, Optional, Any
import logging
from vault_oracle.core.oracle_focus import oracle_focus


# Add project root to path for imports
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
if PROJECT_ROOT not in sys.path:
    sys.path.insert(0, PROJECT_ROOT)

# Import oracle_focus decorator for integration
try:
    ORACLE_FOCUS_AVAILABLE = True
except ImportError:
    # Fallback if oracle_focus is not available
    def oracle_focus(func):
        return func
    ORACLE_FOCUS_AVAILABLE = False

logger = logging.getLogger(__name__)

# Log integration status
if ORACLE_FOCUS_AVAILABLE:
    logger.info("🔥 MEDUSA VAULT: Elemental utilities integrated with Oracle Focus")
else:
    logger.warning("🔥 TITAN WARNING: Oracle Focus not available, using fallback decorator")

# 🔥 Elemental Constants for Basketball Analysis
ELEMENTAL_HALF_LIFE = {
    "fire": timedelta(hours=6),     # Offensive intensity fades quickly
    "earth": timedelta(days=2),     # Defensive stability lasts longer
    "water": timedelta(days=1),     # Flow and rhythm moderate decay
    "air": timedelta(hours=12),     # Transition speed fades moderately
}

ELEMENTAL_MAP = {
    "fire": "offensive_pace",
    "water": "defensive_fluidity", 
    "earth": "rebound_strength",
    "air": "transition_speed",
}

# 🏀 Basketball-specific elemental mappings
BASKETBALL_ELEMENTAL_STATS = {
    "fire": ["points_per_game", "field_goal_percentage", "three_point_percentage", "offensive_rating"],
    "water": ["assists_per_game", "defensive_rating", "steal_percentage", "turnover_ratio"],
    "earth": ["rebounds_per_game", "blocks_per_game", "defensive_rebounds", "paint_points"],
    "air": ["fast_break_points", "pace", "transition_percentage", "speed_rating"]
}


@oracle_focus
def calculate_all_elemental_decays(mythic_roster_id: str, last_game_date: Optional[datetime] = None) -> Dict[str, float]:
    """
    🔥 Calculate decay factors for all elements based on time since last game.
    
    Args:
        mythic_roster_id: Team identifier
        last_game_date: When the team last played (if None, no decay applied)
    
    Returns:
        Dict mapping element names to decay factors (0.0 to 1.0)
    """
    if not last_game_date:
        logger.warning(f"🔥 TITAN WARNING: No last game date for team {mythic_roster_id}. All elemental decays are 1.0 (no decay).")
        return {element: 1.0 for element in ELEMENTAL_HALF_LIFE}

    time_since = datetime.now() - last_game_date
    decay_factors = {
        element: 0.5 ** (time_since.total_seconds() / hl.total_seconds())
        for element, hl in ELEMENTAL_HALF_LIFE.items()
    }
    
    return decay_factors


@oracle_focus
def calculate_elemental_shift(t1_elements: Dict, t2_elements: Dict) -> float:
    """
    🔥 Calculate the magnitude of elemental balance shift between two teams.
    
    Args:
        t1_elements: Team 1 elemental analysis dict
        t2_elements: Team 2 elemental analysis dict
    
    Returns:
        Float representing shift magnitude (0.0 to 2.0)
    """
    try:
        t1_balance = t1_elements.get("elemental_balance", {})
        t2_balance = t2_elements.get("elemental_balance", {})

        # Calculate L1 norm (sum of absolute differences) between elemental balances
        elements = {"Fire", "Water", "Earth", "Air"}
        balance_diff = sum(
            abs(t1_balance.get(e, 0) - t2_balance.get(e, 0)) for e in elements
        )

        # Also consider the difference in primary element values
        t1_primary = t1_elements.get("primary_element", "Balanced")
        t2_primary = t2_elements.get("primary_element", "Balanced")
        
        primary_diff = 0.0 if t1_primary == t2_primary else 0.1
        
        total_shift = balance_diff + primary_diff
        result = min(total_shift, 2.0) # Cap at 2.0
        
        return result
        
    except Exception as e:
        logger.error(f"🔥 MEDUSA ERROR: calculating elemental shift: {e}")
        return 0.0


@oracle_focus
def determine_dominant_element(t1_elements: Dict, t2_elements: Dict) -> str:
    """
    🔥 Determine dominant elemental force based on primary element values and weaknesses.
    
    Args:
        t1_elements: Team 1 elemental analysis dict
        t2_elements: Team 2 elemental analysis dict
    
    Returns:
        String representing the dominant element
    """
    try:
        t1_balance = t1_elements.get("elemental_balance", {})
        t2_balance = t2_elements.get("elemental_balance", {})
        
        # Find the element with highest combined strength
        combined_strength = {}
        for element in ["Fire", "Water", "Earth", "Air"]:
            combined_strength[element] = t1_balance.get(element, 0) + t2_balance.get(element, 0)
        
        if combined_strength:
            dominant = max(combined_strength, key=combined_strength.get)
            return dominant
        else:
            return "Balanced"
        
    except Exception as e:
        logger.error(f"🔥 MEDUSA ERROR: determining dominant element: {e}")
        return "Balanced"


@oracle_focus
def get_default_elemental_profile() -> Dict:
    """
    🔥 Get a balanced default elemental profile.
    
    Returns:
        Dict with balanced elemental values
    """
    profile = {
        "primary_element": "Balanced",
        "elemental_balance": {
            "Fire": 0.25,
            "Water": 0.25,
            "Earth": 0.25,
            "Air": 0.25,
        },
        "elemental_decay": {element: 1.0 for element in ELEMENTAL_HALF_LIFE},
        "elemental_weakness": "None",
    }
    
    return profile


@oracle_focus
def calculate_elemental_compatibility(team1_element: str, team2_element: str) -> float:
    """
    🔥 Calculate elemental compatibility/counter relationships between teams.
    
    Args:
        team1_element: Primary element of team 1
        team2_element: Primary element of team 2
    
    Returns:
        Float representing compatibility factor (-1.0 to 1.0)
        Positive = synergy, Negative = counter, 0 = neutral
    """
    # Elemental interaction matrix (Rock-Paper-Scissors style)
    interactions = {
        ("Fire", "Air"): 0.3,    # Fire feeds on air
        ("Air", "Fire"): -0.3,   # Air gets consumed by fire
        ("Water", "Fire"): 0.4,  # Water extinguishes fire
        ("Fire", "Water"): -0.4, # Fire gets extinguished by water
        ("Earth", "Water"): 0.2, # Earth absorbs water
        ("Water", "Earth"): -0.2, # Water gets absorbed by earth
        ("Air", "Earth"): 0.1,   # Air erodes earth slowly
        ("Earth", "Air"): -0.1,  # Earth resists air
    }
    
    compatibility = interactions.get((team1_element, team2_element), 0.0)
    return compatibility


@oracle_focus
def enhance_team_stats_with_elements(team_stats: Dict[str, Any], elemental_profile: Dict[str, Any]) -> Dict[str, Any]:
    """
    🔥 Enhance team statistics with elemental modifiers.
    
    Args:
        team_stats: Raw team statistics
        elemental_profile: Team's elemental analysis
    
    Returns:
        Enhanced team stats with elemental adjustments
    """
    try:
        enhanced_stats = team_stats.copy()
        elemental_balance = elemental_profile.get("elemental_balance", {})
        elemental_decay = elemental_profile.get("elemental_decay", {})
        
        # Apply elemental modifiers to specific stats
        for element, stat_categories in BASKETBALL_ELEMENTAL_STATS.items():
            element_strength = elemental_balance.get(element.title(), 0.0)
            decay_factor = elemental_decay.get(element.lower(), 1.0)
            modifier = element_strength * decay_factor
            
            for stat in stat_categories:
                if stat in enhanced_stats:
                    # Apply small modifier (±5% max)
                    adjustment = 1.0 + (modifier - 0.25) * 0.2
                    enhanced_stats[f"{stat}_elemental"] = enhanced_stats[stat] * adjustment
        
        return enhanced_stats
        
    except Exception as e:
        logger.error(f"🔥 MEDUSA ERROR: enhancing team stats with elements: {e}")
        return team_stats
