#!/usr/bin/env python3
"""
HYPER MEDUSA NEURAL VAULT - REAL NBA/WNBA DATA INTEGRATION FOR MEDUSA
==============================================

Production-ready integration between NBA API and MEDUSA's Supreme Decision Engine.
This module handles real NBA/WNBA data fetching, processing, and feeding to MEDUSA's
neural consciousness through the medusa_raw_data_intake system.

REAL DATA FLOW:
1. Fetch live NBA/WNBA data from official APIs
2. Process and structure data for MEDUSA
3. Feed raw data directly to medusa_raw_data_intake
4. MEDUSA processes data through neural consciousness
5. Advisory systems receive MEDUSA-processed insights
6. MEDUSA makes final supreme decisions

PRODUCTION FEATURES:
- Real NBA API integration with proper rate limiting
- Live game data streaming
- Player performance tracking
- Team analytics processing
- Market data incorporation
- Error handling and retry logic
- Comprehensive logging and monitoring
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import pandas as pd
import numpy as np
from pathlib import Path
import sys
from vault_oracle.wells.nba_api_connector import BasketballDataConnector






# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.data_integration.medusa_data_ingestion import (
    MedusaDataIngestionEngine,
    DataIngestionConfig,
    create_medusa_data_ingestion_engine
)

from src.neural_cortex.medusa_supreme_decision_engine import (
    RawDataPackage,
    create_medusa_supreme_engine
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("HOOPS_PANTHEON_MEDUSA_INTEGRATION")

class RealNBADataProcessor:
    """
    Real NBA/WNBA Data Processor for MEDUSA
    
    Handles fetching real NBA/WNBA data and preparing it for MEDUSA's
    supreme neural processing.
    """
    
    def __init__(self):
        """Initialize real NBA data processor"""
        self.nba_connector = BasketballDataConnector()
        self.medusa_ingestion = create_medusa_data_ingestion_engine()
        self.processing_stats = {
            'games_processed': 0,
            'players_processed': 0,
            'api_calls_made': 0,
            'errors': 0,
            'start_time': datetime.now(),
            'last_update': None
        }
        
        logger.info(" MEDUSA VAULT: Real NBA Data Processor initialized for MEDUSA")
    
    async def fetch_todays_games(self) -> List[Dict[str, Any]]:
        """
        Fetch today's NBA/WNBA games with comprehensive data
        
        Returns:
        List of processed game data ready for MEDUSA
        """
        logger.info(" MEDUSA VAULT: Fetching today's NBA/WNBA games for MEDUSA...")
        
        today = datetime.now().strftime("%Y-%m-%d")
        processed_games = []
        
        try:
            # Fetch NBA games
            nba_games = await self._fetch_nba_games_for_date(today)
            processed_games.extend(nba_games)
            
            # Fetch WNBA games (if in season)
            wnba_games = await self._fetch_wnba_games_for_date(today)
            processed_games.extend(wnba_games)
            
            logger.info(f" Fetched {len(processed_games)} games for MEDUSA processing")
            
        except Exception as e:
            logger.error(f" TITAN PROCESSING FAILED: fetch today's games: {e}")
            self.processing_stats['errors'] += 1
        
        return processed_games
    
    async def _fetch_nba_games_for_date(self, date: str) -> List[Dict[str, Any]]:
        """Fetch NBA games for specific date with full data"""
        games = []
        
        try:
            # Use NBA API connector to get games
            games_df = await self.nba_connector.get_games_by_date(date)
            self.processing_stats['api_calls_made'] += 1
            
            if games_df is not None and not games_df.empty:
                for _, game_row in games_df.iterrows():
                    # Process each game into MEDUSA format
                    game_data = await self._process_game_for_medusa(game_row, "NBA")
                    if game_data:
                        games.append(game_data)
                
                logger.info(f" Processed {len(games)} NBA games")
            
        except Exception as e:
            logger.error(f" NBA games fetch failed: {e}")
        
        return games
    
    async def _fetch_wnba_games_for_date(self, date: str) -> List[Dict[str, Any]]:
        """Fetch WNBA games for specific date with full data"""
        games = []
        
        try:
            # Use NBA API connector for WNBA (same API, different league)
            games_df = await self.nba_connector.get_wnba_games_by_date(date)
            self.processing_stats['api_calls_made'] += 1
            
            if games_df is not None and not games_df.empty:
                for _, game_row in games_df.iterrows():
                    # Process each game into MEDUSA format
                    game_data = await self._process_game_for_medusa(game_row, "WNBA")
                    if game_data:
                        games.append(game_data)
                
                logger.info(f" Processed {len(games)} WNBA games")
            
        except Exception as e:
            logger.error(f" WNBA games fetch failed: {e}")
        
        return games
    
    async def _process_game_for_medusa(self, game_row: pd.Series, league: str) -> Optional[Dict[str, Any]]:
        """
        Process individual game data into MEDUSA-ready format
        
        Args:
        game_row: Game data from NBA API
        league: NBA or WNBA
        
        Returns:
        Comprehensive game data for MEDUSA's analysis
        """
        try:
            titan_clash_id = str(game_row.get('titan_clash_id', ''))
            home_team_id = str(game_row.get('home_team_id', ''))
            away_team_id = str(game_row.get('visitor_team_id', ''))
            
            # Fetch comprehensive team data
            home_team_data = await self._fetch_team_comprehensive_data(home_team_id, league)
            away_team_data = await self._fetch_team_comprehensive_data(away_team_id, league)
            
            # Fetch recent head-to-head data
            h2h_data = await self._fetch_head_to_head_data(home_team_id, away_team_id, league)
            
            # Fetch player data for both teams
            home_players = await self._fetch_team_players_data(home_team_id, league)
            away_players = await self._fetch_team_players_data(away_team_id, league)
            
            # Construct comprehensive game package for MEDUSA
            medusa_game_data = {
                'titan_clash_id': titan_clash_id,
                'league': league,
                'date': game_row.get('game_date_est', ''),
                'status': game_row.get('game_status_text', 'Scheduled'),
                'arena': game_row.get('arena_name', 'Unknown Arena'),
                
                # Team data
                'home_team': {
                    'id': home_team_id,
                    'name': game_row.get('home_team_name', ''),
                    'abbreviation': game_row.get('home_team_abbreviation', ''),
                    'city': game_row.get('home_team_city', ''),
                    'comprehensive_data': home_team_data,
                    'players': home_players,
                    'is_home': True
                },
                'away_team': {
                    'id': away_team_id,
                    'name': game_row.get('visitor_team_name', ''),
                    'abbreviation': game_row.get('visitor_team_abbreviation', ''),
                    'city': game_row.get('visitor_team_city', ''),
                    'comprehensive_data': away_team_data,
                    'players': away_players,
                    'is_home': False
                },
                
                # Historical context
                'head_to_head': h2h_data,
                'season': game_row.get('season', ''),
                'season_type': game_row.get('season_type', ''),
                
                # Raw API data for MEDUSA's deep analysis
                'raw_api_response': game_row.to_dict(),
                
                # Processing metadata
                'processed_timestamp': datetime.now().isoformat(),
                'data_quality_score': self._assess_game_data_quality(game_row, home_team_data, away_team_data),
                'medusa_processing_priority': self._determine_medusa_priority(game_row, league)
            }
            
            self.processing_stats['games_processed'] += 1
            return medusa_game_data
        
        except Exception as e:
            logger.error(f" TITAN PROCESSING FAILED: process game {game_row.get('titan_clash_id', 'unknown')}: {e}")
            return None
    
    async def _fetch_team_comprehensive_data(self, mythic_roster_id: str, league: str) -> Dict[str, Any]:
        """Fetch comprehensive team data for MEDUSA analysis"""
        team_data = {}
        
        try:
            # Basic team info
            team_info = await self.nba_connector.get_team_info(mythic_roster_id)
            if team_info is not None:
                team_data['info'] = team_info.to_dict('records')[0] if not team_info.empty else {}
            
            # Team stats for current season
            team_stats = await self.nba_connector.get_team_stats(mythic_roster_id)
            if team_stats is not None:
                team_data['season_stats'] = team_stats.to_dict('records')[0] if not team_stats.empty else {}
            
            # Recent game performance (last 10 games)
            recent_games = await self.nba_connector.get_team_game_log(mythic_roster_id, limit=10)
            if recent_games is not None:
                team_data['recent_games'] = recent_games.to_dict('records')
                team_data['recent_form'] = self._analyze_recent_form(recent_games)
            
            self.processing_stats['api_calls_made'] += 3
            
        except Exception as e:
            logger.error(f" TITAN PROCESSING FAILED: fetch team data for {mythic_roster_id}: {e}")
            team_data['error'] = str(e)
        
        return team_data
    
    async def _fetch_team_players_data(self, mythic_roster_id: str, league: str) -> List[Dict[str, Any]]:
        """Fetch team roster and player data"""
        players_data = []
        
        try:
            # Get team roster
            roster = await self.nba_connector.get_team_roster(mythic_roster_id)
            if roster is not None and not roster.empty:
                for _, player_row in roster.iterrows():
                    hero_id = str(player_row.get('hero_id', ''))
                    
                    # Get player stats
                    player_stats = await self._fetch_player_stats(hero_id)
                    
                    player_data = {
                        'id': hero_id,
                        'name': player_row.get('player_name', ''),
                        'position': player_row.get('position', ''),
                        'jersey_number': player_row.get('jersey_number', ''),
                        'roster_info': player_row.to_dict(),
                        'stats': player_stats,
                        'injury_status': await self._check_player_injury_status(hero_id)
                    }
                    
                    players_data.append(player_data)
                
                self.processing_stats['api_calls_made'] += 1
                self.processing_stats['players_processed'] += len(players_data)
            
        except Exception as e:
            logger.error(f" TITAN PROCESSING FAILED: fetch players for team {mythic_roster_id}: {e}")
        
        return players_data
    
    async def _fetch_player_stats(self, hero_id: str) -> Dict[str, Any]:
        """Fetch comprehensive player statistics"""
        try:
            player_stats = await self.nba_connector.get_player_stats(hero_id)
            self.processing_stats['api_calls_made'] += 1
            
            if player_stats is not None and not player_stats.empty:
                return player_stats.to_dict('records')[0]
            else:
                return {}
            
        except Exception as e:
            logger.error(f" TITAN PROCESSING FAILED: fetch stats for player {hero_id}: {e}")
            return {'error': str(e)}
    
    async def _check_player_injury_status(self, hero_id: str) -> Dict[str, Any]:
        """Check player injury status"""
        # This would integrate with injury report APIs
        # For now, return placeholder structure
        return {
            'status': 'active',
            'injury_type': None,
            'estimated_return': None,
            'last_updated': datetime.now().isoformat()
        }
    
    async def _fetch_head_to_head_data(self, home_team_id: str, away_team_id: str, league: str) -> Dict[str, Any]:
        """Fetch head-to-head historical data"""
        try:
            # This would fetch historical matchups between teams
            # For now, return structured placeholder
            h2h_data = {
                'total_games': 0,
                'home_wins': 0,
                'away_wins': 0,
                'last_meeting': None,
                'last_5_meetings': [],
                'average_score_differential': 0.0,
                'trends': []
            }
            
            return h2h_data
        
        except Exception as e:
            logger.error(f" TITAN PROCESSING FAILED: fetch H2H data: {e}")
            return {'error': str(e)}
    
    def _analyze_recent_form(self, recent_games: pd.DataFrame) -> Dict[str, Any]:
        """Analyze team's recent form from game log"""
        if recent_games.empty:
            return {}
        
        # Calculate recent performance metrics
        wins = (recent_games['wl'] == 'W').sum() if 'wl' in recent_games.columns else 0
        total_games = len(recent_games)
        
        avg_points = recent_games['pts'].mean() if 'pts' in recent_games.columns else 0
        avg_opp_points = recent_games['opp_pts'].mean() if 'opp_pts' in recent_games.columns else 0
        
        return {
            'wins': int(wins),
            'losses': int(total_games - wins),
            'win_percentage': float(wins / total_games) if total_games > 0 else 0.0,
            'avg_points_scored': float(avg_points),
            'avg_points_allowed': float(avg_opp_points),
            'point_differential': float(avg_points - avg_opp_points),
            'games_analyzed': int(total_games)
        }
    
    def _assess_game_data_quality(self, game_row: pd.Series, 
                                  home_data: Dict[str, Any], 
                                  away_data: Dict[str, Any]) -> float:
        """Assess the quality of game data for MEDUSA processing"""
        quality_score = 1.0
        
        # Check for missing key data
        if not game_row.get('titan_clash_id'):
            quality_score -= 0.3
        if not home_data or 'error' in home_data:
            quality_score -= 0.2
        if not away_data or 'error' in away_data:
            quality_score -= 0.2
        if not game_row.get('game_date_est'):
            quality_score -= 0.1
        
        return max(0.0, quality_score)
    
    def _determine_medusa_priority(self, game_row: pd.Series, league: str) -> str:
        """Determine MEDUSA processing priority for this game"""
        # High priority for playoff games, primetime games, etc.
        season_type = game_row.get('season_type', '')
        
        if 'playoff' in season_type.lower():
            return 'HIGH'
        elif league == 'NBA' and datetime.now().hour >= 19: # Primetime
            return 'MEDIUM'
        else:
            return 'NORMAL'
    
    async def process_and_feed_to_medusa(self, games_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Process games data and feed to MEDUSA's ingestion system
        
        Args:
        games_data: List of processed game data
        
        Returns:
        Processing results and statistics
        """
        logger.info(f" Feeding {len(games_data)} games to MEDUSA's neural consciousness")
        
        results = {
            'games_fed_to_medusa': 0,
            'successful_ingestions': 0,
            'failed_ingestions': 0,
            'medusa_intake_ids': [],
            'processing_time_ms': 0,
            'errors': []
        }
        
        start_time = time.time()
        
        for game_data in games_data:
            try:
                # Feed to MEDUSA's ingestion engine
                intake_id = await self.medusa_ingestion._feed_to_medusa_intake(
                    raw_data=game_data,
                    data_source=f"{game_data['league']}_API",
                    data_type="LIVE_GAME"
                )
                
                results['medusa_intake_ids'].append(intake_id)
                results['successful_ingestions'] += 1
                
                # Process through MEDUSA's neural consciousness
                await self.medusa_ingestion._process_through_medusa(game_data, intake_id)
                
                logger.info(f" Fed game {game_data['titan_clash_id']} to MEDUSA: {intake_id}")
                
            except Exception as e:
                logger.error(f" TITAN PROCESSING FAILED: feed game {game_data.get('titan_clash_id', 'unknown')} to MEDUSA: {e}")
                results['failed_ingestions'] += 1
                results['errors'].append(str(e))
        
        results['games_fed_to_medusa'] = len(games_data)
        results['processing_time_ms'] = (time.time() - start_time) * 1000
        
        logger.info(f" MEDUSA ingestion complete: {results['successful_ingestions']}/{len(games_data)} successful")
        
        return results
    
    async def run_real_time_integration(self):
        """Run real-time NBA/WNBA data integration for MEDUSA"""
        logger.info(" MEDUSA VAULT: Starting real-time NBA/WNBA integration for MEDUSA")
        
        while True:
            try:
                # Fetch today's games
                games_data = await self.fetch_todays_games()
                
                if games_data:
                    # Feed to MEDUSA
                    results = await self.process_and_feed_to_medusa(games_data)
                    
                    # Update stats
                    self.processing_stats['last_update'] = datetime.now().isoformat()
                    
                    logger.info(f" Cycle complete: {results['successful_ingestions']} games processed")
                else:
                    logger.info(" MEDUSA VAULT: 📅 No games today, MEDUSA standing by...")
                
                # Wait before next cycle (15 minutes)
                await asyncio.sleep(900)
            
            except Exception as e:
                logger.error(f" Real-time integration error: {e}")
                self.processing_stats['errors'] += 1
                await asyncio.sleep(300) # Wait 5 minutes on error
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get current processing statistics"""
        stats = self.processing_stats.copy()
        stats['uptime_hours'] = (datetime.now() - stats['start_time']).total_seconds() / 3600
        return stats

# Factory function
def create_real_nba_processor() -> RealNBADataProcessor:
    """Create Real NBA Data Processor for MEDUSA"""
    return RealNBADataProcessor()

# Demo and testing functions
async def demo_real_nba_integration():
    """Demonstrate real NBA/WNBA data integration with MEDUSA"""
    
    # Initialize processor
    processor = create_real_nba_processor()
    
    try:
        games_data = await processor.fetch_todays_games()
        
        if games_data:
            results = await processor.process_and_feed_to_medusa(games_data)
            
            
            stats = processor.get_processing_stats()
            for key, value in stats.items():
                if key != 'start_time':
                    print(f"{key}: {value}")
            
        
    except Exception as e:
        logger.error(f"Demo integration failed: {e}")
    
    finally:
        processor.medusa_ingestion.close()

if __name__ == "__main__":
    asyncio.run(demo_real_nba_integration())
