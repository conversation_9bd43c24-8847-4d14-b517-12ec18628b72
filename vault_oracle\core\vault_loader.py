# DIGITAL FINGERPRINT: UUID=cc3a4b5c-6d7e-8f9a-0b1c-2d3e4f5a6b7c | DATE=2025-06-26
#!/usr/bin/env python3
"""
(C) 2025 Hyper Medusa Neural Vault, All Rights Reserved.
PROPRIETARY & CONFIDENTIAL — UNAUTHORIZED USE, COPYING, OR DISTRIBUTION IS STRICTLY PROHIBITED.

This file contains proprietary algorithms, business logic, and intellectual property of Hyper Medusa Neural Vault.
Use, reproduction, or disclosure by any means without explicit written permission is a violation of applicable law.
"""

"""
HYPER MEDUSA NEURAL VAULT - Module Business Value Documentation
================================================================

vault_loader.py
---------------
Loads and validates the Hyper Medusa Neural Vault's configuration from TOML files and environment variables using Pydantic models defined in vault_config.py. Provides a central configuration object for the entire Vault.

Business Value:
- Centralizes and secures configuration management, enabling consistent, reliable, and environment-agnostic system operation.
- Reduces misconfiguration risk and supports rapid environment changes.

For further details, see module-level docstrings and architecture documentation.
"""

"""
VAULT_LOADER.py
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
Loads and validates the Hyper Medusa Neural Vault's configuration from TOML files
and environment variables using Pydantic models defined in vault_config.py.
Provides a central configuration object for the entire Vault.
"""



import sys
import os
import toml # For parsing TOML configuration files
from pathlib import Path # For path manipulation
from typing import Dict, Any, Optional, Union, Literal, List # For type hints
import logging # For logging events and errors
import re # REQUIRED for environment variable substitution

try:
    from pydantic import ValidationError
except ImportError:
    ValidationError = None

try:
    from pydantic_settings import BaseSettings
except ImportError:
    BaseSettings = None

try:
    from vault_oracle.core.vault_config import VaultConfig
except ImportError:
    VaultConfig = None

try:
    from vault_oracle.core.vault_manifest import VaultPaths
except ImportError:
    VaultPaths = None

try:
    from vault_oracle.core.vault_config import ExpertEnvironmentValidator
except ImportError:
    ExpertEnvironmentValidator = None

# Oracle Focus decorator import
try:
    from vault_oracle.core.oracle_focus import oracle_focus
except ImportError:
    # Fallback decorator if oracle_focus is not available
    def oracle_focus(func):
        """Fallback oracle_focus decorator."""
        return func

# Configure logger for this module
logger = logging.getLogger(__name__)
# Basic logging configuration (will set up handlers if none exist)
if not logger.handlers:
    handler = logging.StreamHandler(sys.stdout)
    formatter = logging.Formatter(
        "𓀀 %(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO) # Default to INFO level for production


# --- Global Configuration Storage ---
# Use a global variable (or a singleton pattern) to store the loaded configuration
# once it's successfully parsed and validated. This prevents re-loading it multiple times.
_VAULT_CONFIGURATION: Optional["VaultConfig"] = None

# --- Constants for Configuration Paths and Names ---
# Define the project root path based on the loader's location.
# Assumes vault_loader.py is in vault_oracle/core/ and project root is two levels up (HOOPS_PANTHEON_BOOK_OFFICIAL).
PROJECT_ROOT = Path(__file__).resolve().parent.parents[2]

# Define the root directory for configuration files relative to the PROJECT_ROOT
CONFIG_ROOT = PROJECT_ROOT / "config"
DEFAULT_TOML_CONFIG_FILE = "sacred_config.toml" # Default TOML config file name

# Define allowed environments for configuration loading
ALLOWED_ENVIRONMENTS: List[str] = [
    "development",
    "staging",
    "production",
    "mortal",
    "testing",
] # Added 'testing'

# Ensure necessary Pydantic models are imported
try:
    from pydantic_settings import (
        BaseSettings,
        SettingsConfigDict,
    ) # For loading environment variables

    # Import all necessary config models from vault_config.py
    from vault_oracle.core.vault_config import (
        VaultConfig, # The main top-level config model
        ConfigValidator, # For environment variables
    )

    # Define a custom type alias for the settings instance, for clarity.
    VaultSettings = ConfigValidator
    # Import VaultPaths if it's used for directory creation outside of the main block
    # This import needs to be here because VaultPaths is used within the VaultLoader class
except ImportError as e:
    logger.critical(
        f" Critical: TITAN PROCESSING FAILED: import Pydantic or vault_config modules: {e}"
    )
    sys.exit(1) # Exit if core dependencies are missing

# Update the global variable type annotation now that VaultSettings is defined
_ENVIRONMENT_SETTINGS: Optional["VaultSettings"] = None


class VaultLoader:
    """
    Manages the loading and validation of the Hyper Medusa Neural Vault's configuration.
    Provides methods to load from environment variables and TOML files, and ensures
    the configuration conforms to the defined Pydantic schemas.
    """

    # Class-level variables to hold the loaded configuration, ensuring it's a singleton-like access
    _vault_config_instance: Optional["VaultConfig"] = None
    _env_settings_instance: Optional["VaultSettings"] = None
    _is_loaded: bool = False

    @classmethod
    def load_config(
        cls,
        env: Literal[
            "development", "staging", "production", "testing", "mortal"
        ] = "development",
        config_file: Optional[Union[str, Path]] = None,
    ) -> "VaultConfig":
        """
        Loads and validates the Hyper Medusa Neural Vault's configuration.

        It first loads environment variables, then merges configuration from a TOML file.
        The TOML file chosen depends on the 'env' parameter and 'config_file' override.

        Args:
            env (Literal): The deployment environment (e.g., "development", "production").
                This determines which TOML config subset to load if no specific
                config_file is provided.
            config_file (Optional[Union[str, Path]]): Optional. The explicit path to a TOML
                configuration file to load. If provided,
                it overrides the environment-based selection.

        Returns:
            VaultConfig: A fully validated Pydantic model containing the entire Vault configuration.

        Raises:
            FileNotFoundError: If the specified TOML configuration file does not exist.
            ValueError: If an invalid environment is provided or if configuration parsing/validation fails.
            ValidationError: If the loaded data does not conform to the VaultConfig schema.
        """
        if cls._is_loaded and cls._vault_config_instance is not None:
            logger.info(
                "Vault configuration already loaded. Returning existing instance."
            )
            return cls._vault_config_instance

        logger.info(f"Attempting to load Vault configuration for environment: '{env}'")

        if env not in ALLOWED_ENVIRONMENTS:
            raise ValueError(
                f"Invalid environment specified: '{env}'. Must be one of {ALLOWED_ENVIRONMENTS}"
            )

        # 1. Load Environment Variables via ConfigValidator
        # This will automatically load from OS env vars and .env file as defined in ConfigValidator's model_config
        try:
            cls._env_settings_instance = ConfigValidator()
            logger.info(" MEDUSA VAULT: Environment variables loaded via ConfigValidator.")
        except Exception as e:
            # Handle both ValidationError and other exceptions
            if ValidationError is not None and isinstance(e, ValidationError):
                logger.error(
                    f"Validation error loading environment variables: {e.errors()}"
                )
                raise ValueError(
                    f"Environment variable configuration invalid: {e.errors()}"
                ) from e
            else:
                logger.error(
                    f"Unexpected error loading environment variables: {e}", exc_info=True
                )
                raise ValueError(f" TITAN PROCESSING FAILED: load environment variables: {e}") from e

        # 2. Determine TOML Configuration File Path
        toml_path: Path
        if config_file:
            toml_path = Path(config_file).resolve()
            logger.info(f"Using explicitly provided config file: {toml_path}")
        else:
            # Construct path for environment-specific TOML file
            toml_file_name = f"sacred_config.{env}.toml"
            toml_path = CONFIG_ROOT / toml_file_name
            logger.info(f"Using environment-specific config file: {toml_path}")

        # Add an explicit check and log before raising FileNotFoundError
        logger.info(
            f"Checking for existence of TOML file: {toml_path}. Exists: {toml_path.exists()}"
        )
        if not toml_path.exists():
            logger.critical(f"Configuration file not found: {toml_path}")
            raise FileNotFoundError(f"Configuration file not found at: {toml_path}")

        # 3. Load TOML Data
        loaded_data: Dict[str, Any]
        try:
            with open(toml_path, "r", encoding="utf-8") as f:
                loaded_data = toml.load(f)
            logger.info(f" MEDUSA VAULT: Successfully loaded TOML data from: {toml_path}")
        except toml.TomlDecodeError as e:
            logger.error(f"Error parsing TOML file '{toml_path}': {e}")
            raise ValueError(f"Invalid TOML format in {toml_path}: {e}") from e
        except Exception as e:
            logger.error(
                f"Unexpected error loading TOML file '{toml_path}': {e}", exc_info=True
            )
            raise ValueError(f" TITAN PROCESSING FAILED: load TOML file {toml_path}: {e}") from e

        # 4. Substitute environment variables into loaded TOML data
        processed_data = cls._substitute_env_variables(loaded_data)
        logger.info(" MEDUSA VAULT: Environment variable substitution completed on TOML data.")

        # 5. Validate Processed Data against VaultConfig Schema
        try:
            cls._vault_config_instance = VaultConfig(**processed_data)
            logger.info(" MEDUSA VAULT: Vault configuration successfully validated against schema.")

            # After successful validation, ensure directories exist
            if VaultPaths is not None:
                VaultPaths.ensure_directories_exist()
                logger.info(" MEDUSA VAULT: Ensured required Vault directories exist.")
            else:
                logger.warning(" MEDUSA VAULT: VaultPaths not available, skipping directory creation.")

        except Exception as e:
            # Handle both ValidationError and other exceptions
            if ValidationError is not None and isinstance(e, ValidationError):
                logger.error(f"Validation error for Vault configuration: {e.errors()}")
                raise ValueError(f"Vault configuration invalid: {e.errors()}") from e
            else:
                logger.error(
                    f"Unexpected error during Vault configuration validation: {e}",
                    exc_info=True,
                )
                raise ValueError(f" TITAN PROCESSING FAILED: validate Vault configuration: {e}") from e

        cls._is_loaded = True
        return cls._vault_config_instance

    @classmethod
    def _substitute_env_variables(cls, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Recursively substitutes string placeholders like "$ENV_VAR_NAME" or "${ENV_VAR_NAME}"
        with their corresponding environment variable values.
        """

        # Regex to match either $VAR_NAME or ${VAR_NAME}
        # Group 1 captures VAR_NAME for $VAR_NAME, Group 2 captures VAR_NAME for ${VAR_NAME}
        ENV_VAR_PATTERN = re.compile(
            r"\$([A-Za-z_][A-Za-z0-9_]*)|(?P<curly>\$\{([A-Za-z_][A-Za-z0-9_]*)\})"
        )

        def _recurse(item):
            if isinstance(item, dict):
                return {k: _recurse(v) for k, v in item.items()}
            elif isinstance(item, list):
                return [_recurse(elem) for elem in item]
            elif isinstance(item, str):
                match = ENV_VAR_PATTERN.fullmatch(item)
                if match:
                    # Determine which group captured the variable name
                    env_var_name = match.group(1) or match.group(3)
                    env_value = os.getenv(env_var_name)
                    if env_value is not None:
                        logger.debug(
                            f"Substituted '{item}' with environment variable '{env_var_name}'."
                        )
                        return env_value
                    else:
                        logger.warning(
                            f"Environment variable '{env_var_name}' not found for substitution of '{item}'. Keeping original."
                        )
                        # If the environment variable is not found, keep the original placeholder string.
                        # This allows Pydantic to potentially handle it (e.g., if it's a SecretStr that
                        # will later be validated as empty, or if it's an optional field).
                        return item
                return item # Not a placeholder, return as is
            else:
                return item # Not a string, dict, or list, return as is

        return _recurse(data)

    @classmethod
    def get_config(cls) -> "VaultConfig":
        """
        Retrieves the globally loaded and validated Vault configuration.

        Raises:
            RuntimeError: If the configuration has not been loaded yet.
        """
        if cls._vault_config_instance is None or not cls._is_loaded:
            logger.critical(
                "Vault configuration has not been loaded. Call VaultLoader.load_config() first."
            )
            raise RuntimeError(
                "Vault configuration has not been loaded. Call VaultLoader.load_config() first."
            )
        return cls._vault_config_instance

    @classmethod
    def get_env_settings(cls) -> "VaultSettings":
        """
        Retrieves the globally loaded environment settings.

        Raises:
            RuntimeError: If environment settings have not been loaded yet.
        """
        if cls._env_settings_instance is None or not cls._is_loaded:
            logger.critical(
                "Environment settings have not been loaded. Call VaultLoader.load_config() first."
            )
            raise RuntimeError(
                "Environment settings have not been loaded. Call VaultLoader.load_config() first."
            )
        return cls._env_settings_instance

    @classmethod
    @oracle_focus
    def get_basketball_intelligence_config(cls) -> Dict[str, Any]:
        """
        Get basketball intelligence specific configuration with expert validation.

        Returns:
            Dict containing basketball-specific API configurations and features
        """
        if not cls._is_loaded:
            raise RuntimeError("Configuration must be loaded first.")

        env_settings = cls.get_env_settings()
        config = cls.get_config()

        basketball_config = {
            "apis": {
                "odds_api_enabled": env_settings.MEDUSA_ODDS_KEY is not None,
                "ball_dont_lie_enabled": env_settings.BALLDONTLIE_KEY is not None,
            },
            "features": {
                "real_time_analysis": getattr(config.features, "enable_realtime_alerts", False),
                "advanced_metrics": getattr(config.features, "enable_shap_analysis", False),
                "quantum_predictions": getattr(config.features, "enable_quantum_recalibration", False),
            },
            "security": {
                "encrypted_communications": all([
                    env_settings.AEGIS_KEY is not None,
                    env_settings.AMBROSIA_KEY is not None,
                    env_settings.VAULT_ENCRYPTION_KEY is not None
                ])
            }
        }

        return basketball_config

    @classmethod
    @oracle_focus
    def validate_expert_production_readiness(cls) -> Dict[str, Any]:
        """
        Comprehensive expert-level validation for production deployment.

        Returns:
            Dict containing validation results for all expert systems
        """
        if not cls._is_loaded:
            raise RuntimeError("Configuration must be loaded first.")

        config = cls.get_config()
        env_settings = cls.get_env_settings()

        # Run expert environment validator
        if ExpertEnvironmentValidator is not None:
            production_checks = ExpertEnvironmentValidator.validate_production_readiness(env_settings)
            basketball_checks = ExpertEnvironmentValidator.validate_basketball_intelligence_readiness(env_settings)
        else:
            logger.warning(" MEDUSA VAULT: ExpertEnvironmentValidator not available, using fallback validation.")
            production_checks = {"fallback_validation": True}
            basketball_checks = True

        # Validate system configuration
        system_checks = {
            "log_level_appropriate": config.system_config.log_level in ["INFO", "WARNING", "ERROR"],
            "environment_set": config.system_config.environment != "development",
            "max_workers_optimized": config.system_config.max_workers >= 2,
        } # Validate security configuration
        security_checks = {
            "retry_rituals_configured": config.aegis_defense.retry_rituals >= 3,
            "minotaur_gate_enabled": config.aegis_defense.minotaur_gate,
            "harpy_shield_strong": config.aegis_defense.harpy_shield >= 1024,
        }

        # Validate neural configuration if available
        neural_checks = {}
        if hasattr(config, "neural_settings"):
            neural_checks = {
                "batch_size_optimized": getattr(config.neural_settings, "batch_size", 32) >= 16,
                "learning_rate_set": getattr(config.neural_settings, "learning_rate", 0.001) > 0,
                "epochs_reasonable": getattr(config.neural_settings, "epochs", 100) >= 10,
            }

        validation_results = {
            "production_environment": production_checks,
            "basketball_intelligence": {"ready": basketball_checks},
            "system_configuration": system_checks,
            "security_configuration": security_checks,
            "neural_configuration": neural_checks,
            "overall_ready": (
                all(production_checks.values())
                and basketball_checks
                and all(system_checks.values())
                and all(security_checks.values())
                and (not neural_checks or all(neural_checks.values()))
            ),
        }

        if validation_results["overall_ready"]:
            logger.info(" MEDUSA VAULT: Expert system validation passed - Production ready!")
        else:
            logger.warning(" Expert system validation failed - Check configuration before production deployment")

        return validation_results

    @classmethod
    @oracle_focus
    def reload_config(cls, force: bool = False) -> "VaultConfig":
        """
        Reload configuration with expert-level change detection.

        Args:
            force: Force reload even if already loaded

        Returns:
            Newly loaded VaultConfig instance
        """
        if force or not cls._is_loaded:
            cls._vault_config_instance = None
            cls._env_settings_instance = None
            cls._is_loaded = False
            logger.info(" MEDUSA VAULT: Expert configuration reload initiated")

            # Reload with current environment
            return cls.load_config()
        else:
            logger.info(" MEDUSA VAULT: Configuration already loaded. Use force=True to reload.")
            return cls._vault_config_instance


# --- Legacy compatibility function ---
def load_vault_config(*args, **kwargs):
    """
    Legacy compatibility: load_vault_config delegates to VaultLoader.load_config.
    """
    return VaultLoader.load_config(*args, **kwargs)


# Canonical getter for the loaded VaultConfig instance.
def get_vault_config() -> "VaultConfig":
    """Canonical getter for the loaded VaultConfig instance."""
    return VaultLoader.get_config()

# Canonical getter for the loaded VaultSettings instance.
def get_env_settings() -> "VaultSettings":
    """Canonical getter for the loaded VaultSettings instance."""
    return VaultLoader.get_env_settings()


# --- Main execution block for testing/direct run ---
if __name__ == "__main__":
    logger.info(" MEDUSA VAULT: --- Starting VaultLoader direct run test ---")

    # Define the path to the actual production TOML file
    production_config_path = Path(
        "C:/Users/<USER>/OneDrive/Desktop/HOOPS_PANTHEON_BOOK_OFFICIAL/vault_oracle/core/config/sacred_config.production.toml"
    )

    # These environment variables are set for demonstrating the substitution logic
    # and for the ConfigValidator's internal testing.
    # IMPORTANT: For your actual production environment, you would need to set these
    # environment variables (and any others referenced in your sacred_config.production.toml)
    # in your system's environment (e.g., shell, deployment pipeline) for proper operation.
    os.environ["FIREBASE_TEST_CREDENTIALS"] = (
        '{"type": "service_account", "project_id": "env_test", "private_key_id": "***", "private_key": "-----BEGIN PRIVATE KEY-----\n***\n-----END PRIVATE KEY-----\n", "client_email": "***", "client_id": "***", "auth_uri": "***", "token_uri": "***", "auth_provider_x509_cert_url": "***", "client_x509_cert_url": "***", "universe_domain": "***"}'
    )
    os.environ["TEST_ENCRYPTION_KEY_ALPHA"] = (
        "encrypted_key_for_alpha_from_env_1234567890"
    )
    os.environ["TEST_ENCRYPTION_KEY_BETA"] = (
        "encrypted_key_for_beta_from_env_0987654321"
    )
    os.environ["TEST_CHAOS_KEY"] = (
        "Abcdefghijklmnopqrstuvwxyz1234567890ABCDEFGH=" # 44 characters
    )
    os.environ["TEST_HERMES_TOKEN"] = (
        "ComplexTokenWithUpper123!@#$%-Longer" # 37 characters
    )
    os.environ["TEST_AMBROSIA_SECRET"] = (
        "AmbrosiaSecretThatIsMoreThanSixtyFourCharactersLongAndSecureForTesting" # Valid length
    )

    # For ConfigValidator's direct env var loading (prefix HYPER_MEDUSA_)
    os.environ["HYPER_MEDUSA_AEGIS_KEY"] = (
        "AegisKeyFromEnv123456789012345678901234567890" # Long enough
    )
    os.environ["HYPER_MEDUSA_SENTRY_DSN"] = (
        "http://env_test_public@env_test_example.com/1"
    )
    os.environ["HYPER_MEDUSA_BUILD_NUMBER"] = "env_v1.0.0"

    try:
        # Load the actual production configuration
        # This call will attempt to load sacred_config.production.toml
        # and apply environment variable substitutions.
        config = VaultLoader.load_config(
            env="production", config_file=production_config_path
        )

        # Use model_dump_json for Pydantic v2+ to get a pretty JSON representation

        logger.info(
            "\n--- Verifying Substituted and Loaded Values (from production config and test env vars) ---"
        )
        # These prints will reflect values from your actual production TOML,
        # with placeholders replaced by the *test environment variables set above*.
        logger.info(
            f"Firebase Credentials (substituted from Env Var): {config.hermes_conduits.firebase.credentials.get_secret_value()}"
        )
        logger.info(
            f"Test Realm Alpha Encryption Key (substituted from Env Var): {config.moirai_routing.realms['TEST_REALM_ALPHA'].encryption_key.get_secret_value() if 'TEST_REALM_ALPHA' in config.moirai_routing.realms else 'N/A'}"
        )
        logger.info(
            f"Test Realm Beta Encryption Key (substituted from Env Var): {config.moirai_routing.realms['TEST_REALM_BETA'].encryption_key.get_secret_value() if 'TEST_REALM_BETA' in config.moirai_routing.realms else 'N/A'}"
        )
        logger.info(
            f"Hephaestus Chaos Key (substituted from Env Var): {config.hephaestus_security.chaos_key.get_secret_value()}"
        )
        logger.info(
            f"Hephaestus Hermes Token (substituted from Env Var): {config.hephaestus_security.hermes_token.get_secret_value()}"
        )
        logger.info(
            f"Hephaestus Ambrosia Secret (substituted from Env Var): {config.hephaestus_security.ambrosia_secret.get_secret_value()}"
        )
        logger.info(
            f"Moirai Routing Latency Weight (Alpha): {config.moirai_routing.realms['TEST_REALM_ALPHA'].latency_weight if 'TEST_REALM_ALPHA' in config.moirai_routing.realms else 'N/A'}"
        )
        logger.info(
            f"Pantheon Routing Fate Weight (Gamma): {config.pantheon_routing.celestial_paths['test_path_gamma'].fate_weight if 'test_path_gamma' in config.pantheon_routing.celestial_paths else 'N/A'}"
        )

        # Access environment settings (ConfigValidator instance)
        env_settings = VaultLoader.get_env_settings()
        logger.info(
            f"Env AEGIS_KEY: {env_settings.AEGIS_KEY.get_secret_value() if env_settings.AEGIS_KEY else 'N/A'}"
        )
        logger.info(
            f"Env SENTRY_DSN: {env_settings.SENTRY_DSN.unicode_string() if env_settings.SENTRY_DSN else 'N/A'}"
        )

        logger.info(" MEDUSA VAULT: VaultLoader direct run test complete.")

    except FileNotFoundError as e:
        logger.error(
            f"Test failed: The actual production config file was not found at {production_config_path}: {e}"
        )
        print(
            f" Test failed: The actual production config file was not found at {production_config_path}: {e}"
        )
    except ValueError as e:
        logger.error(
            f"Configuration Error during test loading actual production config: {e}",
            exc_info=True,
        )
        print(
            f" Configuration Error during test loading actual production config: {e}"
        )
    except RuntimeError as e:
        logger.error(f"Runtime Error during test: {e}", exc_info=True)
    except Exception as e:
        logger.critical(f"An unexpected error occurred during test: {e}", exc_info=True)

    finally:
        # Clean up dummy environment variables. No dummy TOML file creation or deletion here.
        for var in [
            "FIREBASE_TEST_CREDENTIALS",
            "TEST_ENCRYPTION_KEY_ALPHA",
            "TEST_ENCRYPTION_KEY_BETA",
            "TEST_CHAOS_KEY",
            "TEST_HERMES_TOKEN",
            "TEST_AMBROSIA_SECRET",
            "HYPER_MEDUSA_AEGIS_KEY",
            "HYPER_MEDUSA_SENTRY_DSN",
            "HYPER_MEDUSA_BUILD_NUMBER",
        ]:
            if var in os.environ:
                del os.environ[var]
        logger.info(" MEDUSA VAULT: Test environment cleaned up.")
