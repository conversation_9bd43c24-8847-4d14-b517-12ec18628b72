import os
import sys
import asyncio
import logging
import json
import hashlib
import hmac
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Tuple, Any
from contextlib import asynccontextmanager
from dataclasses import dataclass, asdict
from enum import Enum
import aiohttp
import asyncpg
from cryptography.fernet import <PERSON><PERSON><PERSON>, InvalidToken
from pydantic import BaseModel, Field, validator
from tenacity import retry, wait_exponential, stop_after_attempt
from vault_oracle.core.oracle_focus import oracle_focus
from vault_oracle.interfaces.expert_messaging_orchestrator import ExpertMessagingOrchestrator
from src.betting.market_analysis import MarketAnalysis, BettingOpportunity, ValueBetting
from dotenv import load_dotenv

#!/usr/bin/env python3
# DIGITAL FINGERPRINT: UUID=f1a2b3c4-d5e6-7f8a-9b0c-1d2e3f4a5b6c | DATE=2025-06-26
"""
(C) 2025 Hyper Medusa Neural Vault, All Rights Reserved.
PROPRIETARY & CONFIDENTIAL — UNAUTHORIZED USE, COPYING, OR DISTRIBUTION IS STRICTLY PROHIBITED.

This file contains proprietary algorithms, business logic, and intellectual property of Hyper Medusa Neural Vault.
Use, reproduction, or disclosure by any means without explicit written permission is a violation of applicable law.
"""

"""
HYPER MEDUSA NEURAL VAULT - Expert Odds Integration Business Value Documentation
===============================================================================

expert_odds_integration.py
--------------------------
Full NBA/WNBA odds integration with intelligent API usage optimization and expert analytics.

Business Value:
- Proprietary odds integration: Smart API allocation, dual-league, and market coverage.
- Competitive Edge: Real-time tracking, value detection, and professional-grade analytics.
- Integration: Connects with Medusa Vault prediction, reporting, and alerting systems.
- Explainability: Provides line movement analysis, alerting, and plugin extensibility.
- Extensibility: Designed for plugin analytics, new endpoints, and custom strategies.

For further details, see module-level docstrings and architecture documentation.
"""

# DIGITAL FINGERPRINT: UUID=f1a2b3c4-d5e6-7f8a-9b0c-1d2e3f4a5b6c | DATE=2025-06-26
"""
(C) 2025 Hyper Medusa Neural Vault, All Rights Reserved.
PROPRIETARY & CONFIDENTIAL — UNAUTHORIZED USE, COPYING, OR DISTRIBUTION IS STRICTLY PROHIBITED.

This file contains proprietary algorithms, business logic, and intellectual property of Hyper Medusa Neural Vault.
Use, reproduction, or disclosure by any means without explicit written permission is a violation of applicable law.
"""

"""
HYPER MEDUSA NEURAL VAULT - Expert Odds Integration Business Value Documentation
===============================================================================

expert_odds_integration.py
--------------------------
Centralizes expert odds integration and analytics for the Medusa Vault platform.

Business Value:
- Enables robust, real-time odds integration and analytics.
- Supports extensibility for new odds sources, analytics, and plugins.
- Accelerates the development of new predictive features and business logic.

Extension Points for Plugins & Custom Odds Analytics:
-----------------------------------------------------
- Subclass `ExpertOddsIntegration` to add new odds integration or analytics logic.
- Register odds plugins via a plugin registry or callback system.
- Add new endpoints or data sources by extending the integration class.
- Use the config system to enable/disable features per environment.

For further details, see module-level docstrings and architecture documentation.
"""


# Add project root to path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../../..")))

try:
    EXPERT_MESSAGING_AVAILABLE = True
except ImportError:
    def oracle_focus(func):
        return func
    EXPERT_MESSAGING_AVAILABLE = False
    MarketAnalysis = None
    BettingOpportunity = None
    ValueBetting = None

logger = logging.getLogger("expert_odds_integration")

# Configure expert logging
if not logger.handlers:
    handler = logging.StreamHandler(sys.stdout)
    formatter = logging.Formatter(
        " %(asctime)s %(levelname)s %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)
    logger.propagate = False

# Load environment variables
load_dotenv()

# Configuration
MEDUSA_ODDS_API_KEY = os.getenv("MEDUSA_ODDS_KEY")
QUANTUM_VAULT_KEY = os.getenv("QUANTUM_VAULT_KEY")
QUANTUM_HMAC_SECRET = os.getenv("QUANTUM_HMAC_SECRET")
QUANTUM_DB_DSN = os.getenv("QUANTUM_DB_DSN")

# API Configuration
BASE_URL = "https://api.the-odds-api.com/v4"
DAILY_CALL_LIMIT = 16 # 500 calls / 31 days ≈ 16 calls per day
MONTHLY_CALL_LIMIT = 500

# Sports Configuration
SPORTS_CONFIG = {
    "basketball_nba": {
        "name": "NBA",
        "priority": 1,
        "calls_per_day": 10, # Higher allocation for NBA
        "markets": "h2h,spreads,totals",
        "regions": "us"
    },
    "basketball_wnba": {
        "name": "WNBA",
        "priority": 2,
        "calls_per_day": 6, # Lower allocation for WNBA
        "markets": "h2h,spreads,totals",
        "regions": "us"
    }
}

# Market Types
class MarketType(str, Enum):
    MONEYLINE = "h2h"
    SPREAD = "spreads"
    TOTALS = "totals"

class League(str, Enum):
    NBA = "basketball_nba"
    WNBA = "basketball_wnba"

@dataclass
class OddsData:
    """Standardized odds data structure"""
    titan_clash_id: str
    sport: str
    commence_time: datetime
    home_team: str
    away_team: str
    bookmakers: List[Dict[str, Any]]
    last_updated: datetime
    markets_available: List[str]

    # Extracted odds for quick access
    moneyline_home: Optional[int] = None
    moneyline_away: Optional[int] = None
    spread_line: Optional[float] = None
    spread_home_odds: Optional[int] = None
    spread_away_odds: Optional[int] = None
    total_line: Optional[float] = None
    total_over_odds: Optional[int] = None
    total_under_odds: Optional[int] = None

    # Market intelligence
    line_movement: Optional[Dict[str, Any]] = None
    value_opportunities: Optional[List[Dict[str, Any]]] = None
    market_consensus: Optional[Dict[str, Any]] = None

@dataclass
class APIUsageStats:
    """Track API usage for budget management"""
    calls_made_today: int = 0
    calls_made_month: int = 0
    last_reset_date: datetime = None
    remaining_calls_today: int = DAILY_CALL_LIMIT
    remaining_calls_month: int = MONTHLY_CALL_LIMIT
    nba_calls_today: int = 0
    wnba_calls_today: int = 0

class ExpertOddsIntegrator:
    """
    Expert-level odds API integration with intelligent resource management.

    Features:
    - Smart API call allocation across NBA/WNBA
    - Complete market coverage (moneyline, spreads, totals)
    - Basketball intelligence integration
    - Value betting opportunity detection
    - Real-time alerts and notifications
    - Professional data processing
    """

    def __init__(self):
        self.api_key = MEDUSA_ODDS_API_KEY
        self.cipher = Fernet(QUANTUM_VAULT_KEY.encode()) if QUANTUM_VAULT_KEY else None
        self.usage_stats = APIUsageStats()
        self.expert_messaging = None

        if EXPERT_MESSAGING_AVAILABLE:
            try:
                self.expert_messaging = ExpertMessagingOrchestrator()
                logger.info(" MEDUSA VAULT: Expert messaging system initialized")
            except Exception as e:
                logger.warning(f"Expert messaging not available: {e}")

        self._initialize_usage_tracking()
        logger.info(" MEDUSA VAULT: Expert Odds Integrator initialized successfully")

    def _initialize_usage_tracking(self):
        """Initialize API usage tracking with daily reset"""
        today = datetime.now().date()
        # Edge case: If last_reset_date is None or not today, reset counters
        if not self.usage_stats.last_reset_date or self.usage_stats.last_reset_date.date() != today:
            self.usage_stats.calls_made_today = 0
            self.usage_stats.nba_calls_today = 0
            self.usage_stats.wnba_calls_today = 0
            self.usage_stats.remaining_calls_today = DAILY_CALL_LIMIT
            self.usage_stats.last_reset_date = datetime.now()
            logger.info("API usage counters reset for new day.")
        # Edge case: If new month, reset monthly counter
        if self.usage_stats.last_reset_date and self.usage_stats.last_reset_date.month != today.month:
            self.usage_stats.calls_made_month = 0
            self.usage_stats.remaining_calls_month = MONTHLY_CALL_LIMIT
            logger.info("API usage counters reset for new month.")

    @oracle_focus
    async def check_api_budget(self, sport: str) -> bool:
        """
        Check if we have remaining API calls for the sport.

        Args:
        sport: Sport key (basketball_nba or basketball_wnba)

        Returns:
        True if API call is within budget
        """
        self._initialize_usage_tracking()

        sport_config = SPORTS_CONFIG.get(sport, {})
        sport_daily_limit = sport_config.get("calls_per_day", 5)

        current_sport_calls = (
            self.usage_stats.nba_calls_today if sport == "basketball_nba"
            else self.usage_stats.wnba_calls_today
        )

        within_daily_limit = self.usage_stats.remaining_calls_today > 0
        within_sport_limit = current_sport_calls < sport_daily_limit
        within_monthly_limit = self.usage_stats.remaining_calls_month > 0

        if not (within_daily_limit and within_sport_limit and within_monthly_limit):
            await self._send_budget_alert(sport, current_sport_calls, sport_daily_limit)
            return False

        return True

    async def _send_budget_alert(self, sport: str, current_calls: int, limit: int):
        """Send alert when API budget is exceeded"""
        sport_name = SPORTS_CONFIG.get(sport, {}).get("name", sport)

        if self.expert_messaging:
            await self.expert_messaging.send_alert(
                title=f"🚨 {sport_name} API Budget Alert",
                message=f"{sport_name} API calls: {current_calls}/{limit} daily limit reached",
                topic="api-budget",
                alert_type="budget_limit",
                priority="normal",
                context={
                    "sport": sport_name,
                    "current_calls": current_calls,
                    "daily_limit": limit,
                    "remaining_daily": self.usage_stats.remaining_calls_today,
                    "remaining_monthly": self.usage_stats.remaining_calls_month
                }
            )

    def _update_usage_stats(self, sport: str):
        """Update API usage statistics after successful call"""
        self.usage_stats.calls_made_today += 1
        self.usage_stats.calls_made_month += 1
        self.usage_stats.remaining_calls_today -= 1
        self.usage_stats.remaining_calls_month -= 1

        if sport == "basketball_nba":
            self.usage_stats.nba_calls_today += 1
        elif sport == "basketball_wnba":
            self.usage_stats.wnba_calls_today += 1

        logger.info(
            f" API Usage Updated - Daily: {self.usage_stats.calls_made_today}/{DAILY_CALL_LIMIT}, "
            f"Monthly: {self.usage_stats.calls_made_month}/{MONTHLY_CALL_LIMIT}"
        )

    @retry(
        wait=wait_exponential(multiplier=1, min=2, max=30),
        stop=stop_after_attempt(3)
    )
    @oracle_focus
    async def fetch_odds_data(
        self,
        sport: str,
        markets: str = "h2h,spreads,totals",
        regions: str = "us"
    ) -> List[OddsData]:
        """
        Fetch odds data from The Odds API with expert processing.

        Args:
        sport: Sport key (basketball_nba or basketball_wnba)
        markets: Markets to fetch (h2h,spreads,totals)
        regions: Regions to include (us)

        Returns:
        List of processed odds data
        """
        if not await self.check_api_budget(sport):
            logger.warning(f" API budget exceeded for {sport}")
            return []

        url = f"{BASE_URL}/sports/{sport}/odds"
        params = {
            "apiKey": self.api_key,
            "regions": regions,
            "markets": markets,
            "oddsFormat": "american",
            "dateFormat": "iso"
        }

        sport_name = SPORTS_CONFIG.get(sport, {}).get("name", sport)
        logger.info(f" Fetching {sport_name} odds data...")

        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(url, params=params) as response:
                    response.raise_for_status()
                    data = await response.json()

                # Update usage stats after successful call
                self._update_usage_stats(sport)

                # Process the raw data
                processed_odds = await self._process_odds_data(data, sport)

                # Send success notification
                await self._send_fetch_success_alert(sport_name, len(processed_odds))

                logger.info(f" Successfully fetched {len(processed_odds)} {sport_name} games")
                return processed_odds

            except aiohttp.ClientError as e:
                logger.error(f" API request failed for {sport}: {e}")
                await self._send_fetch_error_alert(sport_name, str(e))
                raise
            except Exception as e:
                logger.error(f" Unexpected error fetching {sport} odds: {e}")
                await self._send_fetch_error_alert(sport_name, str(e))
                raise

    async def _process_odds_data(self, raw_data: List[Dict], sport: str) -> List[OddsData]:
        """Process raw API data into structured OddsData objects"""
        processed_games = []

        for game in raw_data:
            try:
                odds_data = OddsData(
                    titan_clash_id=game.get("id", ""),
                    sport=sport,
                    commence_time=datetime.fromisoformat(game.get("commence_time", "")),
                    home_team=game.get("home_team", ""),
                    away_team=game.get("away_team", ""),
                    bookmakers=game.get("bookmakers", []),
                    last_updated=datetime.now(timezone.utc),
                    markets_available=[]
                )

                # Extract specific odds for quick access
                await self._extract_market_odds(odds_data)

                # Analyze value opportunities
                await self._analyze_value_opportunities(odds_data)

                processed_games.append(odds_data)

            except Exception as e:
                logger.error(f" Error processing game data: {e}")
                continue

        return processed_games

    async def _extract_market_odds(self, odds_data: OddsData):
        """Extract specific market odds for quick access"""
        if not odds_data.bookmakers:
            return

        # Use first bookmaker for primary odds (typically most liquid)
        primary_book = odds_data.bookmakers[0]
        markets = primary_book.get("markets", [])

        for market in markets:
            market_key = market.get("key", "")
            outcomes = market.get("outcomes", [])

            if market_key == "h2h" and len(outcomes) >= 2:
                # Moneyline odds
                for outcome in outcomes:
                    if outcome.get("name") == odds_data.home_team:
                        odds_data.moneyline_home = outcome.get("price")
                    elif outcome.get("name") == odds_data.away_team:
                        odds_data.moneyline_away = outcome.get("price")
                odds_data.markets_available.append("moneyline")

            elif market_key == "spreads" and len(outcomes) >= 2:
                # Spread odds
                for outcome in outcomes:
                    if outcome.get("name") == odds_data.home_team:
                        odds_data.spread_line = outcome.get("point")
                        odds_data.spread_home_odds = outcome.get("price")
                    elif outcome.get("name") == odds_data.away_team:
                        odds_data.spread_away_odds = outcome.get("price")
                odds_data.markets_available.append("spread")

            elif market_key == "totals" and len(outcomes) >= 2:
                # Total odds
                for outcome in outcomes:
                    if outcome.get("name") == "Over":
                        odds_data.total_line = outcome.get("point")
                        odds_data.total_over_odds = outcome.get("price")
                    elif outcome.get("name") == "Under":
                        odds_data.total_under_odds = outcome.get("price")
                odds_data.markets_available.append("totals")

    async def _analyze_value_opportunities(self, odds_data: OddsData):
        """Analyze odds for value betting opportunities"""
        if not MarketAnalysis:
            return

        opportunities = []

        # Analyze moneyline value
        if odds_data.moneyline_home and odds_data.moneyline_away:
            home_implied = MarketAnalysis.implied_probability(odds_data.moneyline_home)
            away_implied = MarketAnalysis.implied_probability(odds_data.moneyline_away)

            # Remove vig to get true probabilities
            home_true, away_true = MarketAnalysis.remove_vig(
                odds_data.moneyline_home, odds_data.moneyline_away
            )

            # Check for significant edges (using example model probabilities)
            # In real implementation, use your prediction model probabilities
            model_home_prob = 0.55 # Example: Replace with actual model prediction
            model_away_prob = 0.45

            home_edge = MarketAnalysis.calculate_edge(model_home_prob, home_implied)
            away_edge = MarketAnalysis.calculate_edge(model_away_prob, away_implied)

            if home_edge > 0.03: # 3% edge threshold
                opportunities.append({
                    "market": "moneyline",
                    "side": "home",
                    "team": odds_data.home_team,
                    "odds": odds_data.moneyline_home,
                    "edge": home_edge,
                    "kelly_fraction": MarketAnalysis.kelly_criterion(
                        home_edge, MarketAnalysis.american_to_decimal(odds_data.moneyline_home)
                    )
                })

            if away_edge > 0.03:
                opportunities.append({
                    "market": "moneyline", "side": "away",
                    "team": odds_data.away_team,
                    "odds": odds_data.moneyline_away,
                    "edge": away_edge,
                    "kelly_fraction": MarketAnalysis.kelly_criterion(
                        away_edge, MarketAnalysis.american_to_decimal(odds_data.moneyline_away)
                    )
                })

        odds_data.value_opportunities = opportunities
        # Send value alert if significant opportunities found
        if opportunities:
            await self._send_value_opportunity_alert(odds_data, opportunities)

    async def _send_fetch_success_alert(self, sport_name: str, game_count: int):
        """Send success notification for odds fetch"""
        if self.expert_messaging:
            await self.expert_messaging.send_basketball_alert(
                alert_type="data_update",
                title=f" {sport_name} Odds Updated",
                body=f"Successfully fetched odds for {game_count} {sport_name} games"
            )

    async def _send_fetch_error_alert(self, sport_name: str, error: str):
        """Send error notification for failed odds fetch"""
        if self.expert_messaging:
            await self.expert_messaging.send_basketball_alert(
                alert_type="error",
                title=f" {sport_name} Odds Fetch Failed",
                body=f" TITAN PROCESSING FAILED: fetch {sport_name} odds: {error}"
            )

    async def _send_value_opportunity_alert(self, odds_data: OddsData, opportunities: List[Dict]):
        """Send alert for value betting opportunities"""
        if not self.expert_messaging:
            return

        game_info = f"{odds_data.away_team} @ {odds_data.home_team}"
        best_opportunity = max(opportunities, key=lambda x: x["edge"])

        await self.expert_messaging.send_basketball_alert(
            alert_type="opportunity",
            title=f"💰 Value Betting Opportunity",
            body=f"{game_info}: {best_opportunity['edge']:.1%} edge on {best_opportunity['team']} {best_opportunity['market']}"
        )

    @oracle_focus
    async def fetch_all_leagues(self) -> Dict[str, List[OddsData]]:
        """
        Fetch odds for all configured leagues with smart allocation.

        Returns:
        Dictionary with league odds data
        """
        results = {}

        # Process leagues by priority
        sorted_sports = sorted(
            SPORTS_CONFIG.items(),
            key=lambda x: x[1].get("priority", 999)
        )

        for sport_key, sport_config in sorted_sports:
            sport_name = sport_config.get("name", sport_key)

            try:
                odds_data = await self.fetch_odds_data(
                    sport=sport_key,
                    markets=sport_config.get("markets", "h2h,spreads,totals"),
                    regions=sport_config.get("regions", "us")
                )

                results[sport_name] = odds_data
                logger.info(f" {sport_name}: {len(odds_data)} games processed")

            except Exception as e:
                logger.error(f" TITAN PROCESSING FAILED: fetch {sport_name} odds: {e}")
                results[sport_name] = []
        # Send comprehensive update alert
        await self._send_comprehensive_update_alert(results)

        return results

    async def _send_comprehensive_update_alert(self, results: Dict[str, List[OddsData]]):
        """Send comprehensive update notification"""
        if not self.expert_messaging:
            return

        total_games = sum(len(games) for games in results.values())
        total_opportunities = sum(
            len(game.value_opportunities or [])
            for games in results.values()
            for game in games
        )

        await self.expert_messaging.send_basketball_alert(
            alert_type="analytics",
            title=f" Complete Odds Update",
            body=f"Updated {total_games} games across all leagues with {total_opportunities} value opportunities"
        )

    @oracle_focus
    async def get_daily_schedule(self) -> Dict[str, Any]:
        """
        Get optimized daily schedule for API calls based on game times.

        Returns:
        Optimized schedule for API calls
        """
        # Fetch current odds to analyze game times
        all_odds = await self.fetch_all_leagues()

        schedule = {
            "total_games": 0,
            "leagues": {},
            "optimal_call_times": [],
            "api_budget_status": {
                "daily_used": self.usage_stats.calls_made_today,
                "daily_limit": DAILY_CALL_LIMIT,
                "monthly_used": self.usage_stats.calls_made_month,
                "monthly_limit": MONTHLY_CALL_LIMIT
            }
        }

        for sport_name, games in all_odds.items():
            game_times = []
            value_games = []

            for game in games:
                game_times.append(game.commence_time)
                if game.value_opportunities:
                    value_games.append({
                        "game": f"{game.away_team} @ {game.home_team}",
                        "commence_time": game.commence_time.isoformat(),
                        "opportunities": len(game.value_opportunities)
                    })

            schedule["leagues"][sport_name] = {
                "total_games": len(games),
                "game_times": [t.isoformat() for t in game_times],
                "value_games": value_games,
                "next_update_recommended": self._calculate_next_update_time(game_times)
            }

            schedule["total_games"] += len(games)

        return schedule

    def _calculate_next_update_time(self, game_times: List[datetime]) -> str:
        """Calculate optimal next update time based on game schedule"""
        if not game_times:
            return datetime.now().isoformat()

        now = datetime.now(timezone.utc)
        upcoming_games = [t for t in game_times if t > now]

        if upcoming_games:
            # Update 30 minutes before first game
            next_game = min(upcoming_games)
            next_update = next_game - timedelta(minutes=30)
            return max(next_update, now + timedelta(hours=1)).isoformat()
        else:
            # No games today, check tomorrow
            return (now + timedelta(hours=12)).isoformat()

# Factory function for easy instantiation
def create_expert_odds_integrator() -> ExpertOddsIntegrator:
    """Create and initialize expert odds integrator"""
    return ExpertOddsIntegrator()

# Main execution for testing
async def main():
    """Demo the expert odds integration system"""
    logger.info(" MEDUSA VAULT: Starting Expert Odds Integration Demo")

    integrator = create_expert_odds_integrator()

    # Fetch all leagues
    results = await integrator.fetch_all_leagues()

    # Display results
    for sport, games in results.items():
        logger.info(f"\n {sport} Results:")
        logger.info(f" Total Games: {len(games)}")

        for game in games[:3]: # Show first 3 games
            logger.info(f" {game.away_team} @ {game.home_team}")
            logger.info(f" Commence: {game.commence_time}")
            logger.info(f" Markets: {', '.join(game.markets_available)}")

            if game.moneyline_home and game.moneyline_away:
                logger.info(f" Moneyline: {game.home_team} {game.moneyline_home}, {game.away_team} {game.moneyline_away}")

            if game.value_opportunities:
                logger.info(f" 💰 Value Opportunities: {len(game.value_opportunities)}")

    # Get daily schedule
    schedule = await integrator.get_daily_schedule()
    logger.info(f"\n📅 Daily Schedule:")
    logger.info(f" Total Games: {schedule['total_games']}")
    logger.info(f" API Calls Used: {schedule['api_budget_status']['daily_used']}/{schedule['api_budget_status']['daily_limit']}")

if __name__ == "__main__":
    asyncio.run(main())
