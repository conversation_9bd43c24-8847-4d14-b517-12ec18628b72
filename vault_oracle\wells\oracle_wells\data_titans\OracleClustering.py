import asyncio
import logging
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union, Any, Protocol
import warnings
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.manifold import TSNE
from sklearn.mixture import GaussianMixture
from sklearn.exceptions import NotFittedError
from sklearn.metrics import silhouette_score, calinski_harabasz_score
from sklearn.preprocessing import StandardScaler
from vault_oracle.core.oracle_focus import oracle_focus, ExpertOracleFocus
from vault_oracle.core.cosmic_exceptions import BaseCosmicException, ClusterError, PredictionSystemFailure
from vault_oracle.interfaces.expert_messaging_orchestrator import ExpertMessagingOrchestrator
import sys
import traceback

#!/usr/bin/env python3
# DIGITAL FINGERPRINT: UUID=1a2b3c4d-5e6f-7a8b-9c0d-1e2f3a4b5c6d | DATE=2025-06-26
"""
(C) 2025 Hyper Medusa Neural Vault, All Rights Reserved.
PROPRIETARY & CONFIDENTIAL — UNAUTHORIZED USE, COPYING, OR DISTRIBUTION IS STRICTLY PROHIBITED.

This file contains proprietary algorithms, business logic, and intellectual property of Hyper Medusa Neural Vault.
Use, reproduction, or disclosure by any means without explicit written permission is a violation of applicable law.
"""

"""
HYPER MEDUSA NEURAL VAULT - Oracle Clustering Business Value Documentation
============================================================================

OracleClustering.py
-------------------
Advanced clustering engine for heroic archetype modeling and dimensional insights.

Business Value:
- Proprietary clustering: High-dimensional, probabilistic, and quality-driven clustering for player archetypes.
- Competitive Edge: Enables unique player segmentation, team analysis, and prediction enhancement.
- Integration: Connects with Oracle Focus, DataTitan, and simulation modules for advanced analytics.
- Explainability: Provides validation, statistical insights, and production-ready reporting.
- Extensibility: Designed for plugin analytics, new clustering modes, and custom endpoints.

For further details, see module-level docstrings and architecture documentation.
"""

"""
ExpertOracleClustering: Advanced clustering engine for heroic archetype modeling and dimensional insights.
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
Expert Functions:
- High-dimensional feature reduction with optimized TSNE manifold learning
- Probabilistic archetype clustering using advanced Gaussian Mixture Models
- Quality-driven clustering validation and statistical insights
- Oracle Focus integration with expert error handling and messaging
- Production-ready async/sync compatibility for real-time analysis
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
"""


try:
    from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
    from sklearn.decomposition import PCA, TruncatedSVD
    from sklearn.metrics import adjusted_rand_score, normalized_mutual_info_score
    from sklearn.neighbors import NearestNeighbors
    from sklearn.utils import check_array
    from umap import UMAP
except ImportError as e:
    raise ImportError(
        f"ExpertOracleClustering requires scikit-learn, matplotlib, pandas, and numpy: {e}"
    )



class ClusteringMethod(Enum):
    """Expert clustering methodologies."""
    GAUSSIAN_MIXTURE = "gaussian_mixture"
    KMEANS = "kmeans"
    DBSCAN = "dbscan"
    HIERARCHICAL = "hierarchical"


class DimensionalityMethod(Enum):
    """Advanced dimensionality reduction techniques."""
    TSNE = "tsne"
    UMAP = "umap"
    PCA = "pca"
    ICA = "ica"


class ClusteringQuality(Enum):
    """Clustering quality assessment levels."""
    EXCELLENT = "excellent"
    GOOD = "good"
    ACCEPTABLE = "acceptable"
    POOR = "poor"


@dataclass
class ClusteringResult:
    """Comprehensive clustering analysis results."""
    cluster_probabilities: np.ndarray
    cluster_labels: np.ndarray
    embeddings: np.ndarray
    quality_metrics: Dict[str, float]
    n_clusters: int
    method_used: ClusteringMethod
    dimensionality_method: DimensionalityMethod
    quality_assessment: ClusteringQuality
    timestamp: datetime = field(default_factory=datetime.now)

    @property
    def is_high_quality(self) -> bool:
        """Check if clustering meets expert quality standards."""
        return self.quality_assessment in [ClusteringQuality.EXCELLENT, ClusteringQuality.GOOD]


@dataclass
class ClusteringConfig:
    """Expert clustering configuration."""
    n_components: int = 32
    dimensionality_method: DimensionalityMethod = DimensionalityMethod.TSNE
    clustering_method: ClusteringMethod = ClusteringMethod.GAUSSIAN_MIXTURE
    tsne_perplexity: int = 30
    tsne_n_components: int = 3
    random_state: int = 42
    quality_threshold: float = 0.6
    auto_optimize: bool = True
    enable_scaling: bool = True


class ExpertOracleClustering:
    """
    Expert-level clustering engine for heroic archetype modeling and dimensional insights.

    Features:
    - Advanced manifold learning with optimized TSNE/UMAP
    - Probabilistic clustering with quality validation
    - Oracle Focus integration and expert error handling
    - Async/sync compatibility for production environments
    - Statistical quality assessment and auto-optimization
    """

    def __init__(self, config: Optional[ClusteringConfig] = None):
        """
        Initialize the expert clustering engine.

        Args:
        config: Expert clustering configuration
        """
        self.config = config or ClusteringConfig()
        self.logger = logging.getLogger(self.__class__.__name__)
        self.messenger = ExpertMessagingOrchestrator()

        # Initialize models
        self._tsne_model: Optional[TSNE] = None
        self._gmm_model: Optional[GaussianMixture] = None
        self._scaler: Optional[StandardScaler] = None

        # Results storage
        self._last_result: Optional[ClusteringResult] = None
        self._embeddings_cache: Optional[np.ndarray] = None

        # Initialize models based on config
        self._initialize_models()

        self.logger.info(f"ExpertOracleClustering initialized with {self.config.n_components} components")

    def _initialize_models(self) -> None:
        """Initialize clustering and dimensionality reduction models."""
        # Initialize TSNE
        if self.config.dimensionality_method == DimensionalityMethod.TSNE:
            self._tsne_model = TSNE(
                n_components=self.config.tsne_n_components,
                perplexity=self.config.tsne_perplexity,
                random_state=self.config.random_state,
                init="pca",
                learning_rate="auto",
                n_jobs=-1
            )

        # Initialize GMM
        if self.config.clustering_method == ClusteringMethod.GAUSSIAN_MIXTURE:
            self._gmm_model = GaussianMixture(
                n_components=self.config.n_components,
                random_state=self.config.random_state,
                covariance_type='full',
                max_iter=200
            )
        # Initialize scaler if enabled
        if self.config.enable_scaling:
            self._scaler = StandardScaler()

    @oracle_focus()
    async def divine_bloodlines_async(self, features: pd.DataFrame) -> ClusteringResult:
        """
        Async version of divine bloodlines clustering analysis.

        Args:
        features: Input heroic features DataFrame
        Returns:
        Comprehensive clustering results
        """
        return await asyncio.get_event_loop().run_in_executor(
            None, self.divine_bloodlines, features
        )

    @oracle_focus()
    def divine_bloodlines(self, features: pd.DataFrame) -> ClusteringResult:
        """
        Unearth hidden archetypes through expert clustering analysis.

        Args:
        features: Input heroic features DataFrame

        Returns:
        Comprehensive clustering results with quality metrics
        Raises:
        ClusterError: If clustering fails or quality is insufficient
        """
        try:
            if features.empty:
                raise ClusterError("Cannot perform clustering on empty features DataFrame")

            self.logger.info(f" Analyzing {features.shape[0]} samples with {features.shape[1]} features")

            # Preprocessing
            processed_features = self._preprocess_features(features)

            # Dimensionality reduction
            embeddings = self._reduce_dimensions(processed_features)

            # Clustering
            cluster_probs, cluster_labels = self._perform_clustering(embeddings)

            # Quality assessment
            quality_metrics = self._assess_quality(embeddings, cluster_labels)
            quality_assessment = self._determine_quality_level(quality_metrics)

            # Create result
            result = ClusteringResult(
                cluster_probabilities=cluster_probs,
                cluster_labels=cluster_labels,
                embeddings=embeddings,
                quality_metrics=quality_metrics,
                n_clusters=self.config.n_components,
                method_used=self.config.clustering_method,
                dimensionality_method=self.config.dimensionality_method,
                quality_assessment=quality_assessment
            )

            # Cache results
            self._last_result = result
            self._embeddings_cache = embeddings

            # Send expert alert for significant findings
            if result.is_high_quality:
                asyncio.create_task(self.messenger.send_basketball_alert(
                    "expert_clustering_success",
                    f" Expert clustering analysis complete: {quality_assessment.value} quality "
                    f"with {self.config.n_components} archetypes identified",
                    {"quality_metrics": quality_metrics, "n_samples": len(features)}
                ))

            self.logger.info(f" Clustering complete: {quality_assessment.value} quality")
            return result

        except Exception as e:
            error_msg = f"Expert clustering analysis failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)

            asyncio.create_task(self.messenger.send_basketball_alert(
                "expert_clustering_error",
                f" {error_msg}",
                {"error": str(e), "features_shape": features.shape if not features.empty else "empty"}
            ))

            raise ClusterError(error_msg) from e

    def _preprocess_features(self, features: pd.DataFrame) -> np.ndarray:
        """Preprocess features with scaling and validation."""
        # Handle missing values
        features_clean = features.fillna(features.mean())

        # Convert to numpy array
        feature_array = features_clean.values

        # Scale if enabled
        if self.config.enable_scaling and self._scaler is not None:
            feature_array = self._scaler.fit_transform(feature_array)

        return feature_array

    def _reduce_dimensions(self, features: np.ndarray) -> np.ndarray:
        """Perform dimensionality reduction."""
        if self.config.dimensionality_method == DimensionalityMethod.TSNE:
            if self._tsne_model is None:
                raise ClusterError("TSNE model not initialized")
            return self._tsne_model.fit_transform(features)
        else:
            raise ClusterError(f"Dimensionality method {self.config.dimensionality_method} not implemented")

    def _perform_clustering(self, embeddings: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Perform probabilistic clustering."""
        if self.config.clustering_method == ClusteringMethod.GAUSSIAN_MIXTURE:
            if self._gmm_model is None:
                raise ClusterError("GMM model not initialized")

            self._gmm_model.fit(embeddings)
            cluster_probs = self._gmm_model.predict_proba(embeddings)
            cluster_labels = self._gmm_model.predict(embeddings)

            return cluster_probs, cluster_labels
        else:
            raise ClusterError(f"Clustering method {self.config.clustering_method} not implemented")

    def _assess_quality(self, embeddings: np.ndarray, labels: np.ndarray) -> Dict[str, float]:
        """Assess clustering quality using multiple metrics."""
        metrics = {}

        try:
            # Silhouette score
            if len(np.unique(labels)) > 1:
                metrics['silhouette_score'] = silhouette_score(embeddings, labels)
                metrics['calinski_harabasz_score'] = calinski_harabasz_score(embeddings, labels)
            else:
                metrics['silhouette_score'] = 0.0
                metrics['calinski_harabasz_score'] = 0.0

            # GMM-specific metrics
            if self._gmm_model is not None:
                metrics['aic'] = self._gmm_model.aic(embeddings)
                metrics['bic'] = self._gmm_model.bic(embeddings)
                metrics['log_likelihood'] = self._gmm_model.score(embeddings)

        except Exception as e:
            self.logger.warning(f"Could not compute some quality metrics: {e}")
            metrics['silhouette_score'] = 0.0
            metrics['calinski_harabasz_score'] = 0.0

        return metrics

    def _determine_quality_level(self, metrics: Dict[str, float]) -> ClusteringQuality:
        """Determine overall clustering quality level."""
        silhouette = metrics.get('silhouette_score', 0.0)

        if silhouette >= 0.8:
            return ClusteringQuality.EXCELLENT
        elif silhouette >= 0.6:
            return ClusteringQuality.GOOD
        elif silhouette >= 0.4:
            return ClusteringQuality.ACCEPTABLE
        else:
            return ClusteringQuality.POOR

    @oracle_focus()
    def visualize_olympus(self, embeddings: Optional[np.ndarray] = None,
                          result: Optional[ClusteringResult] = None,
                          save_path: Optional[str] = None) -> Optional[str]:
        """
        Create expert visualization of celestial archetype constellations.

        Args:
        embeddings: Custom embeddings to visualize (optional)
        result: ClusteringResult to visualize (optional, uses last result if not provided)
        save_path: Path to save the visualization (optional)

        Returns:
        Path to saved visualization file if save_path provided
        """
        try:
            # Determine data source
            if result is not None:
                plot_embeddings = result.embeddings
                plot_labels = result.cluster_labels
                quality = result.quality_assessment.value
            elif embeddings is not None and self._gmm_model is not None:
                plot_embeddings = embeddings
                plot_labels = self._gmm_model.predict(embeddings)
                quality = "custom"
            elif self._last_result is not None:
                plot_embeddings = self._last_result.embeddings
                plot_labels = self._last_result.cluster_labels
                quality = self._last_result.quality_assessment.value
            else:
                raise ClusterError("No clustering data available for visualization")

            if plot_embeddings.shape[1] < 2:
                raise ClusterError("Embeddings must have at least 2 dimensions for visualization")

            # Create expert visualization
            plt.figure(figsize=(16, 12))

            # Create scatter plot with enhanced styling
            scatter = plt.scatter(
                plot_embeddings[:, 0],
                plot_embeddings[:, 1],
                c=plot_labels,
                cmap='viridis',
                s=100,
                alpha=0.7,
                edgecolors='white',
                linewidth=0.5
            )

            # Add colorbar
            plt.colorbar(scatter, label='Archetype Cluster')

            # Enhanced styling
            plt.title(f' Expert Archetype Clustering Analysis\nQuality: {quality.upper()}',
                      fontsize=16, fontweight='bold', pad=20)
            plt.xlabel(' Primary Dimensional Flow', fontsize=12, fontweight='bold')
            plt.ylabel(' Secondary Archetype Resonance', fontsize=12, fontweight='bold')

            # Add grid and styling
            plt.grid(True, alpha=0.3, linestyle='--')
            plt.tight_layout()

            # Add cluster centers if available
            if self._gmm_model is not None and hasattr(self._gmm_model, 'means_'):
                centers = self._gmm_model.means_
                if centers.shape[1] >= 2:
                    plt.scatter(centers[:, 0], centers[:, 1],
                                c='red', marker='x', s=200,
                                linewidths=3, label='Cluster Centers')
                    plt.legend()

            # Save or show
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"🎨 Visualization saved to: {save_path}")
                plt.close()
                return save_path
            else:
                plt.show()
                return None

        except Exception as e:
            error_msg = f"Visualization failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            raise ClusterError(error_msg) from e

    @oracle_focus()
    def get_archetype_insights(self) -> Dict[str, Any]:
        """
        Generate expert insights from the latest clustering analysis.

        Returns:
        Dictionary containing archetype insights and recommendations
        """
        if self._last_result is None:
            raise ClusterError("No clustering analysis available. Run divine_bloodlines first.")

        result = self._last_result
        insights = {
            'summary': {
                'n_clusters': result.n_clusters,
                'n_samples': len(result.cluster_labels),
                'quality': result.quality_assessment.value,
                'is_high_quality': result.is_high_quality
            },
            'quality_metrics': result.quality_metrics,
            'cluster_distribution': {},
            'recommendations': []
        }

        # Analyze cluster distribution
        unique_labels, counts = np.unique(result.cluster_labels, return_counts=True)
        for label, count in zip(unique_labels, counts):
            percentage = (count / len(result.cluster_labels)) * 100
            insights['cluster_distribution'][f'archetype_{label}'] = {
                'count': int(count),
                'percentage': round(percentage, 2)
            }

        # Generate recommendations
        if not result.is_high_quality:
            insights['recommendations'].append("Consider adjusting clustering parameters for better quality")

        if len(unique_labels) < self.config.n_components:
            insights['recommendations'].append("Some clusters may be empty - consider reducing n_components")

        # Check for imbalanced clusters
        min_cluster_size = min(counts)
        max_cluster_size = max(counts)
        if max_cluster_size / min_cluster_size > 10:
            insights['recommendations'].append("Clusters are highly imbalanced - consider different initialization")

        return insights

    # Legacy compatibility methods
    def get_cluster_probabilities(self, features: pd.DataFrame) -> np.ndarray:
        """Legacy compatibility: Return cluster probabilities."""
        warnings.warn("get_cluster_probabilities is deprecated. Use divine_bloodlines instead.",
                      DeprecationWarning, stacklevel=2)
        result = self.divine_bloodlines(features)
        return result.cluster_probabilities

    @property
    def tsne(self) -> TSNE:
        """Legacy compatibility: Access to TSNE model."""
        warnings.warn("Direct tsne access is deprecated. Use configuration instead.",
                      DeprecationWarning, stacklevel=2)
        return self._tsne_model

    @property
    def gmm(self) -> GaussianMixture:
        """Legacy compatibility: Access to GMM model."""
        warnings.warn("Direct gmm access is deprecated. Use configuration instead.",
                      DeprecationWarning, stacklevel=2)
        return self._gmm_model

    @property
    def n_components(self) -> int:
        """Legacy compatibility: Number of components."""
        warnings.warn("Direct n_components access is deprecated. Use config.n_components instead.",
                      DeprecationWarning, stacklevel=2)
        return self.config.n_components


# Legacy alias for backward compatibility
OracleClustering = ExpertOracleClustering


# Example Usage and Testing
if __name__ == "__main__":
    """
    Expert-level testing and demonstration of the ExpertOracleClustering system.
    """


    # Configure logging
    logging.basicConfig(level=logging.INFO,
                        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    try:
        # Create expert configuration
        config = ClusteringConfig(
            n_components=5,
            dimensionality_method=DimensionalityMethod.TSNE,
            clustering_method=ClusteringMethod.GAUSSIAN_MIXTURE,
            tsne_perplexity=20,
            auto_optimize=True,
            quality_threshold=0.5
        )

        # Initialize expert clustering engine
        expert_clustering = ExpertOracleClustering(config)

        # Generate sample heroic features
        n_samples = 150
        n_features = 12

        # Create more realistic basketball-like features
        np.random.seed(42)
        features_data = np.random.rand(n_samples, n_features) * 100

        # Add some structure to make clustering more meaningful
        for i in range(n_samples):
            cluster_id = i % 5  # Create 5 natural groups
            noise = np.random.normal(0, 10, n_features)
            features_data[i] = features_data[i] + cluster_id * 20 + noise

        features_df = pd.DataFrame(
            features_data,
            columns=[
                'points', 'assists', 'rebounds', 'steals', 'blocks',
                'field_goal_pct', 'three_point_pct', 'free_throw_pct',
                'minutes', 'turnovers', 'fouls', 'basketball_iq'
            ]
        )


        # Perform expert clustering analysis
        clustering_result = expert_clustering.divine_bloodlines(features_df)


        # Get expert insights
        insights = expert_clustering.get_archetype_insights()

        for archetype, stats in insights['cluster_distribution'].items():
            print(f"Archetype {archetype}: {stats}")

        if insights['recommendations']:
            for rec in insights['recommendations']:
                print(f"Recommendation: {rec}")

        # Create visualization
        try:
            expert_clustering.visualize_olympus(result=clustering_result)
        except Exception as viz_error:
            print(f"Visualization error: {viz_error}")

        # Test async functionality
        try:
            async def test_async():
                async_result = await expert_clustering.divine_bloodlines_async(features_df)
                return async_result.quality_assessment.value

            # Run async test
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            async_quality = loop.run_until_complete(test_async())
            loop.close()


        except Exception as async_error:
            print(f"Async error: {async_error}")

        # Test legacy compatibility
        try:
            with warnings.catch_warnings(record=True) as w:
                warnings.simplefilter("always")

                # Test legacy methods
                legacy_probs = expert_clustering.get_cluster_probabilities(features_df)
                legacy_n_components = expert_clustering.n_components


                if w:
                    print(f"Legacy warnings: {len(w)}")

        except Exception as legacy_error:
            print(f"Legacy error: {legacy_error}")


    except Exception as e:
        traceback.print_exc()
        sys.exit(1)
