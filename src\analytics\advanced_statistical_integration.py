import sqlite3
import logging
import json
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from pathlib import Path
import statistics

#!/usr/bin/env python3
"""
 HYPER MEDUSA NEURAL VAULT - Advanced Statistical Integration 
=====================================================================

Phase 2 Component: Advanced Statistical Integration
Detailed integration for 3-pointers, blocks, alternate lines, and combos
into the system's data layer and foundational logic, accounting for 
statistical and stylistic differences between NBA and WNBA.

Features:
- Advanced 3-point analytics with league differences
- Block statistics and defensive metrics
- Alternate lines and combination props
- League-specific statistical modeling
- Real-time statistical updates
"""


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('medusa_advanced_stats')

@dataclass
class AdvancedPlayerMetrics:
    """Advanced player statistical metrics"""
    hero_id: str
    player_name: str
    league: str
    season: str
    
    # 3-Point Metrics
    three_point_attempts: float = 0.0
    three_point_made: float = 0.0
    three_point_percentage: float = 0.0
    three_point_volume_rank: int = 0
    corner_three_percentage: float = 0.0
    above_break_three_percentage: float = 0.0
    contested_three_percentage: float = 0.0
    catch_and_shoot_three_percentage: float = 0.0
    pull_up_three_percentage: float = 0.0
    
    # Defensive Metrics
    blocks_per_game: float = 0.0
    blocks_per_36: float = 0.0
    block_percentage: float = 0.0
    defensive_rating: float = 0.0
    rim_protection_percentage: float = 0.0
    help_blocks: float = 0.0
    transition_blocks: float = 0.0
    
    # Advanced Combo Metrics
    points_rebounds_assists: float = 0.0 # PRA
    points_rebounds: float = 0.0 # PR
    points_assists: float = 0.0 # PA
    rebounds_assists: float = 0.0 # RA
    stocks: float = 0.0 # Steals + Blocks
    double_double_probability: float = 0.0
    triple_double_probability: float = 0.0
    
    # League-Specific Adjustments
    pace_adjustment: float = 1.0
    usage_rate: float = 0.0
    efficiency_rating: float = 0.0

@dataclass
class LeagueStatisticalProfile:
    """League-specific statistical profiles"""
    league: str
    season: str
    
    # League Averages
    avg_three_point_attempts: float = 0.0
    avg_three_point_percentage: float = 0.0
    avg_blocks_per_game: float = 0.0
    avg_pace: float = 0.0
    avg_points_per_game: float = 0.0
    
    # League Style Factors
    three_point_emphasis: float = 1.0 # How much the league emphasizes 3s
    defensive_intensity: float = 1.0 # Defensive rating adjustment
    physicality_factor: float = 1.0 # Physical play style factor
    pace_factor: float = 1.0 # Game pace normalization
    
class AdvancedStatisticalIntegration:
    """
    Advanced Statistical Integration System
    
    Provides sophisticated statistical analysis with league-specific
    adjustments for NBA and WNBA differences.
    """
    
    def __init__(self, db_path: str = "medusa_vault.db"):
        self.db_path = Path(db_path)
        self.league_profiles = {}
        self._initialize_league_profiles()
        logger.info(" MEDUSA VAULT: Advanced Statistical Integration System initialized")
    
    def _initialize_league_profiles(self):
        """Initialize league-specific statistical profiles"""
        # NBA Profile (2024-25 season estimates)
        self.league_profiles["NBA"] = LeagueStatisticalProfile(
            league="NBA",
            season="2024-25",
            avg_three_point_attempts=39.2,
            avg_three_point_percentage=0.358,
            avg_blocks_per_game=5.1,
            avg_pace=99.8,
            avg_points_per_game=114.7,
            three_point_emphasis=1.0, # Baseline
            defensive_intensity=1.0, # Baseline
            physicality_factor=1.0, # Baseline
            pace_factor=1.0 # Baseline
        )
        
        # WNBA Profile (2024 season estimates)
        self.league_profiles["WNBA"] = LeagueStatisticalProfile(
            league="WNBA",
            season="2024",
            avg_three_point_attempts=22.1,
            avg_three_point_percentage=0.337,
            avg_blocks_per_game=3.2,
            avg_pace=83.1,
            avg_points_per_game=83.4,
            three_point_emphasis=0.78, # Less 3-point emphasis than NBA
            defensive_intensity=1.15, # Higher defensive intensity
            physicality_factor=0.85, # Less physical than NBA
            pace_factor=0.83 # Slower pace
        )
    
    def calculate_advanced_three_point_metrics(self, hero_id: str, league: str, 
                                                games_data: List[Dict]) -> Dict[str, float]:
        """
        Calculate advanced 3-point metrics for a player
        
        Args:
        hero_id: Player identifier
        league: NBA or WNBA
        games_data: List of game data dictionaries
        
        Returns:
        Dictionary of advanced 3-point metrics
        """
        logger.info(f" Calculating 3-point metrics for player {hero_id} ({league})")
        
        if not games_data:
            return self._get_default_three_point_metrics(league)
        
        # Extract 3-point data from games
        three_point_data = []
        for game in games_data:
            if 'three_point_attempts' in game and 'three_point_made' in game:
                three_point_data.append({
                    'attempts': game.get('three_point_attempts', 0),
                    'made': game.get('three_point_made', 0),
                    'corner_attempts': game.get('corner_three_attempts', 0),
                    'corner_made': game.get('corner_three_made', 0),
                    'contested_attempts': game.get('contested_three_attempts', 0),
                    'contested_made': game.get('contested_three_made', 0),
                    'catch_shoot_attempts': game.get('catch_shoot_three_attempts', 0),
                    'catch_shoot_made': game.get('catch_shoot_three_made', 0),
                    'pull_up_attempts': game.get('pull_up_three_attempts', 0),
                    'pull_up_made': game.get('pull_up_three_made', 0)
                })
        
        # Calculate metrics
        total_attempts = sum(g['attempts'] for g in three_point_data)
        total_made = sum(g['made'] for g in three_point_data)
        
        if total_attempts == 0:
            return self._get_default_three_point_metrics(league)
        
        # Basic metrics
        three_point_percentage = total_made / total_attempts
        attempts_per_game = total_attempts / len(games_data)
        
        # Advanced metrics
        corner_attempts = sum(g['corner_attempts'] for g in three_point_data)
        corner_made = sum(g['corner_made'] for g in three_point_data)
        corner_percentage = corner_made / corner_attempts if corner_attempts > 0 else 0
        
        above_break_attempts = total_attempts - corner_attempts
        above_break_made = total_made - corner_made
        above_break_percentage = above_break_made / above_break_attempts if above_break_attempts > 0 else 0
        
        contested_attempts = sum(g['contested_attempts'] for g in three_point_data)
        contested_made = sum(g['contested_made'] for g in three_point_data)
        contested_percentage = contested_made / contested_attempts if contested_attempts > 0 else 0
        
        catch_shoot_attempts = sum(g['catch_shoot_attempts'] for g in three_point_data)
        catch_shoot_made = sum(g['catch_shoot_made'] for g in three_point_data)
        catch_shoot_percentage = catch_shoot_made / catch_shoot_attempts if catch_shoot_attempts > 0 else 0
        
        pull_up_attempts = sum(g['pull_up_attempts'] for g in three_point_data)
        pull_up_made = sum(g['pull_up_made'] for g in three_point_data)
        pull_up_percentage = pull_up_made / pull_up_attempts if pull_up_attempts > 0 else 0
        
        # League adjustments
        league_profile = self.league_profiles[league]
        
        # Volume ranking (relative to league)
        volume_percentile = min(attempts_per_game / league_profile.avg_three_point_attempts, 2.0)
        volume_rank = int(volume_percentile * 100)
        
        return {
            'three_point_attempts': attempts_per_game,
            'three_point_made': total_made / len(games_data),
            'three_point_percentage': three_point_percentage,
            'three_point_volume_rank': volume_rank,
            'corner_three_percentage': corner_percentage,
            'above_break_three_percentage': above_break_percentage,
            'contested_three_percentage': contested_percentage,
            'catch_and_shoot_three_percentage': catch_shoot_percentage,
            'pull_up_three_percentage': pull_up_percentage,
            'league_adjusted_percentage': three_point_percentage / league_profile.avg_three_point_percentage,
            'three_point_impact_score': (attempts_per_game * three_point_percentage) / league_profile.avg_three_point_attempts
        }
    
    def calculate_advanced_defensive_metrics(self, hero_id: str, league: str,
                                                games_data: List[Dict]) -> Dict[str, float]:
        """
        Calculate advanced defensive metrics including blocks
        
        Args:
        hero_id: Player identifier
        league: NBA or WNBA
        games_data: List of game data dictionaries
        
        Returns:
        Dictionary of advanced defensive metrics
        """
        logger.info(f" Calculating defensive metrics for player {hero_id} ({league})")
        
        if not games_data:
            return self._get_default_defensive_metrics(league)
        
        # Extract defensive data
        defensive_data = []
        total_minutes = 0
        
        for game in games_data:
            minutes = game.get('minutes', 0)
            total_minutes += minutes
            
            defensive_data.append({
                'blocks': game.get('blocks', 0),
                'steals': game.get('steals', 0),
                'minutes': minutes,
                'defensive_rebounds': game.get('defensive_rebounds', 0),
                'rim_attempts_defended': game.get('rim_attempts_defended', 0),
                'rim_makes_allowed': game.get('rim_makes_allowed', 0),
                'help_blocks': game.get('help_blocks', 0),
                'transition_blocks': game.get('transition_blocks', 0)
            })
        
        # Calculate basic metrics
        total_blocks = sum(g['blocks'] for g in defensive_data)
        total_steals = sum(g['steals'] for g in defensive_data)
        
        blocks_per_game = total_blocks / len(defensive_data)
        blocks_per_36 = (total_blocks / total_minutes) * 36 if total_minutes > 0 else 0
        
        # Advanced metrics
        rim_attempts = sum(g['rim_attempts_defended'] for g in defensive_data)
        rim_makes = sum(g['rim_makes_allowed'] for g in defensive_data)
        rim_protection_pct = 1 - (rim_makes / rim_attempts) if rim_attempts > 0 else 0.5
        
        help_blocks = sum(g['help_blocks'] for g in defensive_data)
        transition_blocks = sum(g['transition_blocks'] for g in defensive_data)
        
        # League adjustments
        league_profile = self.league_profiles[league]
        
        # Block percentage estimation (simplified)
        estimated_team_blocks = blocks_per_game * 5 # Rough team estimation
        block_percentage = blocks_per_game / estimated_team_blocks if estimated_team_blocks > 0 else 0
        
        # Defensive rating estimation (simplified)
        defensive_rating = 100 + ((blocks_per_game + total_steals/len(defensive_data)) - league_profile.avg_blocks_per_game) * 5
        
        return {
            'blocks_per_game': blocks_per_game,
            'blocks_per_36': blocks_per_36,
            'block_percentage': block_percentage,
            'defensive_rating': defensive_rating,
            'rim_protection_percentage': rim_protection_pct,
            'help_blocks': help_blocks / len(defensive_data),
            'transition_blocks': transition_blocks / len(defensive_data),
            'stocks': (total_blocks + total_steals) / len(defensive_data),
            'league_adjusted_blocks': blocks_per_game / league_profile.avg_blocks_per_game
        }
    
    def calculate_combination_metrics(self, hero_id: str, league: str,
                                        games_data: List[Dict]) -> Dict[str, float]:
        """
        Calculate combination prop metrics (PRA, PR, PA, RA, etc.)
        
        Args:
        hero_id: Player identifier 
        league: NBA or WNBA
        games_data: List of game data dictionaries
        
        Returns:
        Dictionary of combination metrics
        """
        logger.info(f"🔢 Calculating combination metrics for player {hero_id} ({league})")
        
        if not games_data:
            return self._get_default_combo_metrics(league)
        
        # Extract basic stats for combinations
        combo_data = []
        double_doubles = 0
        triple_doubles = 0
        
        for game in games_data:
            points = game.get('points', 0)
            rebounds = game.get('rebounds', 0)
            assists = game.get('assists', 0)
            steals = game.get('steals', 0)
            blocks = game.get('blocks', 0)
            
            combo_data.append({
                'points': points,
                'rebounds': rebounds,
                'assists': assists,
                'steals': steals,
                'blocks': blocks,
                'pra': points + rebounds + assists,
                'pr': points + rebounds,
                'pa': points + assists,
                'ra': rebounds + assists,
                'stocks': steals + blocks
            })
            
            # Check for double/triple doubles (per game)
            stats = [points, rebounds, assists, steals, blocks]
            double_digit_stats = sum(1 for stat in stats if stat >= 10)
            
            if double_digit_stats >= 2:
                double_doubles += 1
            if double_digit_stats >= 3:
                triple_doubles += 1
        
        # Calculate averages
        avg_points = statistics.mean([g['points'] for g in combo_data])
        avg_rebounds = statistics.mean([g['rebounds'] for g in combo_data])
        avg_assists = statistics.mean([g['assists'] for g in combo_data])
        avg_steals = statistics.mean([g['steals'] for g in combo_data])
        avg_blocks = statistics.mean([g['blocks'] for g in combo_data])
        
        # Combination averages
        avg_pra = statistics.mean([g['pra'] for g in combo_data])
        avg_pr = statistics.mean([g['pr'] for g in combo_data])
        avg_pa = statistics.mean([g['pa'] for g in combo_data])
        avg_ra = statistics.mean([g['ra'] for g in combo_data])
        avg_stocks = statistics.mean([g['stocks'] for g in combo_data])
        
        # Probabilities
        double_double_prob = double_doubles / len(games_data)
        triple_double_prob = triple_doubles / len(games_data)
        
        # League adjustments
        league_profile = self.league_profiles[league]
        pace_adjustment = league_profile.pace_factor
        
        return {
            'points_rebounds_assists': avg_pra * pace_adjustment,
            'points_rebounds': avg_pr * pace_adjustment,
            'points_assists': avg_pa * pace_adjustment,
            'rebounds_assists': avg_ra * pace_adjustment,
            'stocks': avg_stocks * pace_adjustment,
            'double_double_probability': double_double_prob,
            'triple_double_probability': triple_double_prob,
            'avg_points': avg_points,
            'avg_rebounds': avg_rebounds,
            'avg_assists': avg_assists,
            'avg_steals': avg_steals,
            'avg_blocks': avg_blocks,
            'combo_efficiency': avg_pra / league_profile.avg_points_per_game
        }
    
    def generate_alternate_lines(self, base_metrics: Dict[str, float], 
                                    prop_type: str, league: str) -> Dict[str, Dict[str, float]]:
        """
        Generate alternate lines for props based on statistical distribution
        
        Args:
        base_metrics: Player's base statistical metrics
        prop_type: Type of prop (points, rebounds, etc.)
        league: NBA or WNBA
        
        Returns:
        Dictionary of alternate lines with probabilities
        """
        base_value = base_metrics.get(f'avg_{prop_type}', 20.0)
        std_dev = base_metrics.get(f'std_{prop_type}', base_value * 0.3)
        
        # Generate alternate lines
        alternate_lines = {}
        
        # Standard alternate lines (±0.5, ±1.5, ±2.5, etc.)
        for offset in [-2.5, -1.5, -0.5, 0.5, 1.5, 2.5]:
            line_value = base_value + offset
            
            if line_value > 0: # Only positive lines make sense
                # Calculate probability using normal distribution approximation
                z_score = offset / std_dev
                
                if offset > 0:
                    prob_over = 0.5 - (z_score * 0.15) # Simplified approximation
                else:
                    prob_over = 0.5 + (abs(z_score) * 0.15)
                
                prob_over = max(0.1, min(0.9, prob_over)) # Bound between 10-90%
                
                alternate_lines[f"{prop_type}_{line_value}"] = {
                    'line': line_value,
                    'over_probability': prob_over,
                    'under_probability': 1 - prob_over
                }
        
        return alternate_lines
    
    def create_advanced_player_profile(self, hero_id: str, league: str,
                                        season: str = "2024-25") -> AdvancedPlayerMetrics:
        """
        Create comprehensive advanced player profile
        
        Args:
        hero_id: Player identifier
        league: NBA or WNBA 
        season: Season identifier
        
        Returns:
        AdvancedPlayerMetrics object
        """
        logger.info(f"👤 Creating advanced profile for player {hero_id} ({league})")
        
        # Get player basic info
        player_info = self._get_player_info(hero_id, league)
        player_name = player_info.get('name', f'Player_{hero_id}')
        
        # Get recent games data (mock for now)
        games_data = self._get_player_games_data(hero_id, league)
        
        # Calculate all advanced metrics
        three_point_metrics = self.calculate_advanced_three_point_metrics(hero_id, league, games_data)
        defensive_metrics = self.calculate_advanced_defensive_metrics(hero_id, league, games_data)
        combo_metrics = self.calculate_combination_metrics(hero_id, league, games_data)
        
        # Create comprehensive profile
        profile = AdvancedPlayerMetrics(
            hero_id=hero_id,
            player_name=player_name,
            league=league,
            season=season
        )
        
        # Populate 3-point metrics
        for key, value in three_point_metrics.items():
            if hasattr(profile, key):
                setattr(profile, key, value)
        
        # Populate defensive metrics
        for key, value in defensive_metrics.items():
            if hasattr(profile, key):
                setattr(profile, key, value)
        
        # Populate combination metrics
        for key, value in combo_metrics.items():
            if hasattr(profile, key):
                setattr(profile, key, value)
        
        # League-specific adjustments
        league_profile = self.league_profiles[league]
        profile.pace_adjustment = league_profile.pace_factor
        
        return profile
    
    def _get_default_three_point_metrics(self, league: str) -> Dict[str, float]:
        """Get default 3-point metrics for league"""
        league_profile = self.league_profiles[league]
        
        return {
            'three_point_attempts': league_profile.avg_three_point_attempts / 5, # Per player estimate
            'three_point_made': league_profile.avg_three_point_attempts / 5 * league_profile.avg_three_point_percentage,
            'three_point_percentage': league_profile.avg_three_point_percentage,
            'three_point_volume_rank': 50,
            'corner_three_percentage': league_profile.avg_three_point_percentage + 0.02,
            'above_break_three_percentage': league_profile.avg_three_point_percentage - 0.01,
            'contested_three_percentage': league_profile.avg_three_point_percentage - 0.05,
            'catch_and_shoot_three_percentage': league_profile.avg_three_point_percentage + 0.03,
            'pull_up_three_percentage': league_profile.avg_three_point_percentage - 0.03
        }
    
    def _get_default_defensive_metrics(self, league: str) -> Dict[str, float]:
        """Get default defensive metrics for league"""
        league_profile = self.league_profiles[league]
        
        return {
            'blocks_per_game': league_profile.avg_blocks_per_game / 5, # Per player estimate
            'blocks_per_36': league_profile.avg_blocks_per_game / 5 * 1.2,
            'block_percentage': 0.02,
            'defensive_rating': 105.0,
            'rim_protection_percentage': 0.55,
            'help_blocks': 0.1,
            'transition_blocks': 0.05
        }
    
    def _get_default_combo_metrics(self, league: str) -> Dict[str, float]:
        """Get default combination metrics for league"""
        league_profile = self.league_profiles[league]
        
        base_points = league_profile.avg_points_per_game / 5
        
        return {
            'points_rebounds_assists': base_points + 8, # Estimate
            'points_rebounds': base_points + 5,
            'points_assists': base_points + 3,
            'rebounds_assists': 8,
            'stocks': 1.5,
            'double_double_probability': 0.15,
            'triple_double_probability': 0.02
        }
    
    def _get_player_info(self, hero_id: str, league: str) -> Dict[str, Any]:
        """Get basic player information"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                table = "nba_players" if league == "NBA" else "wnba_players"
                query = f"SELECT name, team FROM {table} WHERE id = ? LIMIT 1"
                
                cursor.execute(query, (hero_id,))
                result = cursor.fetchone()
                
                if result:
                    return {"name": result[0], "team": result[1]}
                else:
                    return {"name": f"Player_{hero_id}", "team": "Unknown"}
        
        except Exception as e:
            logger.error(f"Error getting player info: {e}")
            return {"name": f"Player_{hero_id}", "team": "Unknown"}
    
    def _get_player_games_data(self, hero_id: str, league: str) -> List[Dict[str, Any]]:
        """Get player games data (mock implementation)"""
        # In production, this would query actual game logs
        # For now, return mock data
        mock_games = []
        
        for i in range(20): # Last 20 games
            mock_games.append({
                'titan_clash_id': f"game_{i}",
                'points': np.random.normal(18, 6),
                'rebounds': np.random.normal(7, 3),
                'assists': np.random.normal(4, 2),
                'steals': np.random.normal(1, 0.7),
                'blocks': np.random.normal(0.8, 0.8),
                'three_point_attempts': np.random.poisson(5),
                'three_point_made': np.random.poisson(1.7),
                'minutes': np.random.normal(28, 5),
                'corner_three_attempts': np.random.poisson(1.5),
                'corner_three_made': np.random.poisson(0.6),
                'contested_three_attempts': np.random.poisson(2),
                'contested_three_made': np.random.poisson(0.6),
                'catch_shoot_three_attempts': np.random.poisson(3),
                'catch_shoot_three_made': np.random.poisson(1.2),
                'pull_up_three_attempts': np.random.poisson(2),
                'pull_up_three_made': np.random.poisson(0.6),
                'rim_attempts_defended': np.random.poisson(3),
                'rim_makes_allowed': np.random.poisson(1.5),
                'help_blocks': np.random.poisson(0.2),
                'transition_blocks': np.random.poisson(0.1),
                'defensive_rebounds': np.random.normal(5, 2)
            })
        
        return mock_games

def demo_advanced_stats_system():
    """Demonstrate the advanced statistical integration system"""
    
    stats_system = AdvancedStatisticalIntegration()
    
    # Test players from both leagues
    test_players = [
        {"hero_id": "nba_player_1", "league": "NBA"},
        {"hero_id": "wnba_player_1", "league": "WNBA"}
    ]
    
    for player in test_players:
        print(f"\n🏀 Analyzing {player['league']} Player: {player['hero_id']}")

        # Create advanced profile
        profile = stats_system.create_advanced_player_profile(
            player['hero_id'],
            player['league']
        )

        print(f"📊 Player Profile: {profile.player_name}")
        print(f"   3PT%: {profile.three_point_percentage:.3f}")
        print(f"   Blocks/Game: {profile.blocks_per_game:.2f}")
        print(f"   PRA Average: {profile.points_rebounds_assists:.1f}")

        # Generate alternate lines example
        base_metrics = {'avg_points': 20.5, 'std_points': 6.2}
        alt_lines = stats_system.generate_alternate_lines(base_metrics, 'points', player['league'])

        print(f"📈 Sample Alternate Lines for Points:")
        for line_key, line_data in list(alt_lines.items())[:3]:
            print(f"   {line_data['line']}: Over {line_data['over_probability']:.1%}")

if __name__ == "__main__":
    demo_advanced_stats_system()
