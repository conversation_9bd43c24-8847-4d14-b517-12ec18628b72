import logging
import asyncio
import json
import os
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import traceback
import firebase_admin
from firebase_admin import credentials, messaging, firestore, auth
from firebase_admin.exceptions import FirebaseError
try:
    from firebase_admin import analytics
    FIREBASE_ANALYTICS_AVAILABLE = True
except ImportError:
    analytics = None
    FIREBASE_ANALYTICS_AVAILABLE = False
import aiohttp
try:
    import aiomqtt as asyncio_mqtt  # Use the newer aiomqtt library
    MQTT_AVAILABLE = True
except ImportError:
    asyncio_mqtt = None
    MQTT_AVAILABLE = False
from prometheus_client import Counter, Histogram, Gauge
from vault_oracle.core.config.config_loader import config_loader

"""
🔥 HYPER MEDUSA NEURAL VAULT - Unified Firebase Production System
================================================================

Complete Firebase integration with messaging, alerts, analytics, and real-time features.
All systems consolidated into one production-ready manager.
"""


# Firebase SDK imports
try:
    FIREBASE_AVAILABLE = True
    
    # Try to import analytics separately as it may not be available in all environments
    try:
        FIREBASE_ANALYTICS_AVAILABLE = True
    except ImportError:
        FIREBASE_ANALYTICS_AVAILABLE = False
        
except ImportError:
    FIREBASE_AVAILABLE = False
    FIREBASE_ANALYTICS_AVAILABLE = False

# Additional dependencies
try:
    # Additional dependency imports can go here
    DEPENDENCIES_AVAILABLE = True
except ImportError:
    DEPENDENCIES_AVAILABLE = False

# Configuration
try:
    CONFIG_AVAILABLE = True
except ImportError:
    CONFIG_AVAILABLE = False

logger = logging.getLogger("🔥 FIREBASE_PRODUCTION")


class AlertLevel(Enum):
    """Alert severity levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"
    EMERGENCY = "emergency"


class MessageType(Enum):
    """Message types for different purposes."""
    PREDICTION_ALERT = "prediction_alert"
    SYSTEM_STATUS = "system_status"
    USER_NOTIFICATION = "user_notification"
    TRADING_SIGNAL = "trading_signal"
    MODEL_UPDATE = "model_update"
    MAINTENANCE = "maintenance"
    SECURITY_ALERT = "security_alert"


@dataclass
class FirebaseMessage:
    """Structured message for Firebase."""
    title: str
    body: str
    data: Dict[str, Any]
    topic: Optional[str] = None
    tokens: Optional[List[str]] = None
    priority: str = "high"
    message_type: MessageType = MessageType.USER_NOTIFICATION
    alert_level: AlertLevel = AlertLevel.INFO


@dataclass
class SystemMetrics:
    """System performance metrics."""
    cpu_usage: float
    memory_usage: float
    prediction_accuracy: float
    active_users: int
    api_calls_per_minute: int
    error_rate: float
    response_time_ms: float


class HyperMedusaFirebaseManager:
    """
    🔥 Complete Firebase Production Manager
    
    Handles all Firebase operations:
    - Cloud Messaging (FCM)
    - Real-time Database
    - Firestore
    - Analytics
    - Authentication
    - Alert System
    - Performance Monitoring
    """
    
    def __init__(self):
        self.app = None
        self.db = None
        self.auth_client = None
        self.initialized = False
        self.metrics = {}
        self.alert_subscribers = {}
        self.message_queue = asyncio.Queue()
        
        # Performance counters
        if DEPENDENCIES_AVAILABLE:
            self.messages_sent = Counter('firebase_messages_sent_total', 'Total Firebase messages sent', ['type'])
            self.alerts_triggered = Counter('firebase_alerts_triggered_total', 'Total alerts triggered', ['level'])
            self.response_time = Histogram('firebase_response_time_seconds', 'Firebase operation response time')
        
        logger.info("🔥 Firebase Production Manager initialized")
    
    async def initialize(self) -> bool:
        """Initialize Firebase with production configuration."""
        try:
            if not FIREBASE_AVAILABLE:
                logger.error("❌ Firebase SDK not available")
                return False
            
            # Load Firebase credentials
            cred_path = "vault_oracle/sacred_scrolls/firebase_service_account.json"
            if not os.path.exists(cred_path):
                logger.error(f"❌ Firebase credentials not found at {cred_path}")
                return False
            
            # Initialize Firebase
            cred = credentials.Certificate(cred_path)
            self.app = firebase_admin.initialize_app(cred, {
                'databaseURL': 'https://nba-ai-app-default-rtdb.firebaseio.com/',
                'projectId': 'nba-ai-app'
            })
            
            # Initialize Firestore
            self.db = firestore.client()
            
            # Initialize Auth
            self.auth_client = auth
            
            # Start background tasks
            asyncio.create_task(self._process_message_queue())
            asyncio.create_task(self._monitor_system_health())
            
            self.initialized = True
            logger.info("✅ Firebase Production Manager fully initialized")
            
            # Send initialization notification
            await self.send_system_alert(
                "Firebase Production System Online",
                "All Firebase services are operational and ready for production use.",
                AlertLevel.INFO
            )
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Firebase initialization failed: {e}")
            return False
    
    async def send_message(self, message: FirebaseMessage) -> bool:
        """Send a Firebase Cloud Message."""
        try:
            if not self.initialized:
                logger.warning("⚠️ Firebase not initialized, queuing message")
                await self.message_queue.put(message)
                return False
            
            # Build FCM message
            fcm_message = messaging.Message(
                notification=messaging.Notification(
                    title=message.title,
                    body=message.body
                ),
                data={
                    **message.data,
                    'timestamp': str(datetime.utcnow().isoformat()),
                    'type': message.message_type.value,
                    'alert_level': message.alert_level.value
                },
                android=messaging.AndroidConfig(
                    priority=message.priority,
                    notification=messaging.AndroidNotification(
                        sound='default',
                        color='#FF6B35'  # Medusa orange
                    )
                ),
                apns=messaging.APNSConfig(
                    headers={'apns-priority': '10'},
                    payload=messaging.APNSPayload(
                        aps=messaging.Aps(
                            sound='default',
                            badge=1
                        )
                    )
                )
            )
            
            # Send to topic or specific tokens
            if message.topic:
                fcm_message.topic = message.topic
                response = messaging.send(fcm_message)
            elif message.tokens:
                response = messaging.send_multicast(
                    messaging.MulticastMessage(
                        notification=fcm_message.notification,
                        data=fcm_message.data,
                        tokens=message.tokens[:500]  # FCM limit
                    )
                )
            else:
                logger.warning("⚠️ No topic or tokens specified for message")
                return False
            
            # Log and track metrics
            if DEPENDENCIES_AVAILABLE:
                self.messages_sent.labels(type=message.message_type.value).inc()
            
            logger.info(f"✅ Message sent successfully: {message.title}")
            
            # Store in Firestore for history
            await self._store_message_history(message)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to send message: {e}")
            return False
    
    async def send_prediction_alert(self, 
                                   game_info: Dict[str, Any], 
                                   prediction: Dict[str, Any],
                                   confidence: float) -> bool:
        """Send prediction alert to subscribers."""
        try:
            title = f"🏀 {game_info['away_team']} @ {game_info['home_team']}"
            
            if prediction['type'] == 'moneyline':
                body = f"Prediction: {prediction['winner']} ({confidence:.1%} confidence)"
            else:
                body = f"{prediction['type']}: {prediction['value']} ({confidence:.1%})"
            
            message = FirebaseMessage(
                title=title,
                body=body,
                data={
                    'game_id': str(game_info['game_id']),
                    'prediction_type': prediction['type'],
                    'prediction_value': str(prediction['value']),
                    'confidence': str(confidence),
                    'model_version': prediction.get('model_version', 'unknown')
                },
                topic='prediction_alerts',
                message_type=MessageType.PREDICTION_ALERT,
                alert_level=AlertLevel.INFO if confidence > 0.6 else AlertLevel.WARNING
            )
            
            return await self.send_message(message)
            
        except Exception as e:
            logger.error(f"❌ Failed to send prediction alert: {e}")
            return False
    
    async def send_system_alert(self, 
                               title: str, 
                               message: str, 
                               level: AlertLevel,
                               data: Optional[Dict] = None) -> bool:
        """Send system alert to administrators."""
        try:
            alert_message = FirebaseMessage(
                title=f"🔥 MEDUSA SYSTEM: {title}",
                body=message,
                data=data or {},
                topic='system_alerts',
                message_type=MessageType.SYSTEM_STATUS,
                alert_level=level,
                priority='high' if level in [AlertLevel.ERROR, AlertLevel.CRITICAL] else 'normal'
            )
            
            if DEPENDENCIES_AVAILABLE:
                self.alerts_triggered.labels(level=level.value).inc()
            
            return await self.send_message(alert_message)
            
        except Exception as e:
            logger.error(f"❌ Failed to send system alert: {e}")
            return False
    
    async def send_trading_signal(self, 
                                 signal: Dict[str, Any]) -> bool:
        """Send trading signal to premium subscribers."""
        try:
            confidence = signal.get('confidence', 0)
            roi_expected = signal.get('roi_expected', 0)
            
            title = f"💰 Trading Signal: {signal['game']}"
            body = f"{signal['bet_type']}: {signal['recommendation']} (ROI: {roi_expected:.1%})"
            
            message = FirebaseMessage(
                title=title,
                body=body,
                data={
                    'signal_id': str(signal['id']),
                    'bet_type': signal['bet_type'],
                    'recommendation': signal['recommendation'],
                    'confidence': str(confidence),
                    'roi_expected': str(roi_expected),
                    'odds': str(signal.get('odds', 'N/A'))
                },
                topic='trading_signals',
                message_type=MessageType.TRADING_SIGNAL,
                alert_level=AlertLevel.INFO if confidence > 0.65 else AlertLevel.WARNING
            )
            
            return await self.send_message(message)
            
        except Exception as e:
            logger.error(f"❌ Failed to send trading signal: {e}")
            return False
    
    async def update_user_analytics(self, 
                                   user_id: str, 
                                   event: str, 
                                   properties: Dict[str, Any]) -> bool:
        """Update user analytics in Firestore."""
        try:
            if not self.db:
                return False
            
            # Update user analytics
            user_ref = self.db.collection('user_analytics').document(user_id)
            
            analytics_data = {
                'last_activity': datetime.utcnow(),
                'events': firestore.ArrayUnion([{
                    'event': event,
                    'timestamp': datetime.utcnow(),
                    'properties': properties
                }])
            }
            
            user_ref.set(analytics_data, merge=True)
            
            # Update global analytics
            global_ref = self.db.collection('system_analytics').document('global')
            global_ref.update({
                f'events.{event}': firestore.Increment(1),
                'last_updated': datetime.utcnow()
            })
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to update analytics: {e}")
            return False
    
    async def get_system_health(self) -> Dict[str, Any]:
        """Get comprehensive system health status."""
        try:
            health_data = {
                'firebase_status': 'operational' if self.initialized else 'down',
                'timestamp': datetime.utcnow().isoformat(),
                'services': {
                    'messaging': self.initialized,
                    'firestore': self.db is not None,
                    'auth': self.auth_client is not None
                },
                'metrics': self.metrics,
                'message_queue_size': self.message_queue.qsize()
            }
            
            # Store health status
            if self.db:
                health_ref = self.db.collection('system_health').document('current')
                health_ref.set(health_data)
            
            return health_data
            
        except Exception as e:
            logger.error(f"❌ Failed to get system health: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def subscribe_to_alerts(self, 
                                 user_id: str, 
                                 topics: List[str],
                                 token: str) -> bool:
        """Subscribe user to alert topics."""
        try:
            # Subscribe to topics
            for topic in topics:
                messaging.subscribe_to_topic([token], topic)
            
            # Store subscription in Firestore
            if self.db:
                sub_ref = self.db.collection('subscriptions').document(user_id)
                sub_ref.set({
                    'topics': topics,
                    'token': token,
                    'subscribed_at': datetime.utcnow()
                }, merge=True)
            
            logger.info(f"✅ User {user_id} subscribed to topics: {topics}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to subscribe to alerts: {e}")
            return False
    
    async def _process_message_queue(self):
        """Background task to process queued messages."""
        while True:
            try:
                if self.initialized:
                    message = await asyncio.wait_for(self.message_queue.get(), timeout=5.0)
                    await self.send_message(message)
                else:
                    await asyncio.sleep(5)
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"❌ Error processing message queue: {e}")
                await asyncio.sleep(1)
    
    async def _monitor_system_health(self):
        """Background task to monitor system health."""
        while True:
            try:
                await asyncio.sleep(60)  # Check every minute
                
                if self.initialized:
                    health = await self.get_system_health()
                    
                    # Check for issues and send alerts
                    if health['message_queue_size'] > 100:
                        await self.send_system_alert(
                            "High Message Queue",
                            f"Message queue has {health['message_queue_size']} pending messages",
                            AlertLevel.WARNING
                        )
                
            except Exception as e:
                logger.error(f"❌ Error in health monitoring: {e}")
                await asyncio.sleep(30)
    
    async def _store_message_history(self, message: FirebaseMessage):
        """Store message in Firestore for history."""
        try:
            if self.db:
                history_ref = self.db.collection('message_history')
                history_ref.add({
                    'title': message.title,
                    'body': message.body,
                    'type': message.message_type.value,
                    'alert_level': message.alert_level.value,
                    'sent_at': datetime.utcnow(),
                    'data': message.data
                })
        except Exception as e:
            logger.error(f"❌ Failed to store message history: {e}")


# Global Firebase manager instance
firebase_manager = HyperMedusaFirebaseManager()


async def initialize_firebase_production() -> bool:
    """Initialize Firebase for production use."""
    return await firebase_manager.initialize()


async def send_prediction_notification(game_info: Dict, prediction: Dict, confidence: float) -> bool:
    """Quick function to send prediction notifications."""
    return await firebase_manager.send_prediction_alert(game_info, prediction, confidence)


async def send_system_notification(title: str, message: str, level: str = "info") -> bool:
    """Quick function to send system notifications."""
    alert_level = AlertLevel(level.lower())
    return await firebase_manager.send_system_alert(title, message, alert_level)


async def get_firebase_health() -> Dict[str, Any]:
    """Get Firebase system health."""
    return await firebase_manager.get_system_health()


# Convenience functions for different alert types
class FirebaseAlerts:
    """Quick access to different types of Firebase alerts."""
    
    @staticmethod
    async def model_update(model_name: str, accuracy: float, version: str):
        """Send model update notification."""
        await firebase_manager.send_system_alert(
            f"Model Updated: {model_name}",
            f"New model version {version} deployed with {accuracy:.1%} accuracy",
            AlertLevel.INFO,
            {'model': model_name, 'accuracy': str(accuracy), 'version': version}
        )
    
    @staticmethod
    async def high_value_bet(game: str, bet_type: str, value: str, confidence: float):
        """Send high-value betting opportunity."""
        signal = {
            'id': f"bet_{datetime.utcnow().timestamp()}",
            'game': game,
            'bet_type': bet_type,
            'recommendation': value,
            'confidence': confidence,
            'roi_expected': confidence * 0.15  # Estimate ROI
        }
        await firebase_manager.send_trading_signal(signal)
    
    @staticmethod
    async def system_error(error_message: str, component: str):
        """Send system error alert."""
        await firebase_manager.send_system_alert(
            f"System Error: {component}",
            error_message,
            AlertLevel.ERROR,
            {'component': component, 'error': error_message}
        )
    
    @staticmethod
    async def performance_degradation(metric: str, current_value: float, threshold: float):
        """Send performance degradation alert."""
        await firebase_manager.send_system_alert(
            "Performance Alert",
            f"{metric} degraded to {current_value:.2f} (threshold: {threshold:.2f})",
            AlertLevel.WARNING,
            {'metric': metric, 'value': str(current_value), 'threshold': str(threshold)}
        )


if __name__ == "__main__":
    # Test the Firebase system
    async def test_firebase():
        
        success = await initialize_firebase_production()
        if success:
            
            # Test system alert
            await FirebaseAlerts.model_update("NBA_Ensemble_v2", 0.672, "2.1.5")
            
            # Test prediction alert
            game_info = {
                'game_id': 'test_123',
                'home_team': 'Lakers',
                'away_team': 'Warriors'
            }
            prediction = {
                'type': 'moneyline',
                'winner': 'Lakers',
                'value': 'Lakers ML'
            }
            await send_prediction_notification(game_info, prediction, 0.68)
            
        else:
            print("Firebase not available for testing")

    asyncio.run(test_firebase())
