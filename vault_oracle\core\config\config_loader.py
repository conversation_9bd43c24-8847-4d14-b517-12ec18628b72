import os
import toml
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass

#!/usr/bin/env python3
"""
 HYPER MEDUSA NEURAL VAULT - Configuration Loader Expert
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
 Elite Basketball Intelligence Configuration Management

Expert-level configuration loader that provides environment-aware configuration
loading for the HYPER MEDUSA NEURAL VAULT system. Supports production, development,
and testing environments with basketball intelligence integration.

 FEATURES:
- Single unified configuration file (sacred_config.production.toml)
- Environment variable substitution
- Basketball intelligence configuration
- Eternal vigil runner settings
- Expert messaging system integration
- Neural threat detection parameters
- Production-ready error handling

 Enterprise-Grade Configuration Management
"""


logger = logging.getLogger(__name__)

@dataclass
class ConfigEnvironment:
    """Configuration environment detection and management"""
    name: str
    log_level: str
    debug_mode: bool

    @classmethod
    def detect(cls) -> 'ConfigEnvironment':
        """Detect current environment from environment variables"""
        env_name = os.getenv('HYPER_MEDUSA_ENVIRONMENT', 'development').lower()

        if env_name == 'production':
            return cls(name='production', log_level='INFO', debug_mode=False)
        elif env_name == 'testing':
            return cls(name='testing', log_level='MEDUSA_DEBUG', debug_mode=True)
        else:
            return cls(name='development', log_level='MEDUSA_DEBUG', debug_mode=True)

class ExpertConfigLoader:
    """ Expert Configuration Loader for HYPER MEDUSA NEURAL VAULT"""

    def __init__(self):
        self.config_dir = Path(__file__).parent
        self.config_file = self.config_dir / "sacred_config.production.toml"
        self.environment = ConfigEnvironment.detect()

        logger.info(f" HYPER MEDUSA Config Loader initialized for {self.environment.name} environment")

    def load_config(self) -> Dict[str, Any]:
        """Load and process the unified configuration file"""
        try:
            if not self.config_file.exists():
                raise FileNotFoundError(f"Configuration file not found: {self.config_file}")

            # Load TOML configuration
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = toml.load(f)

            # Apply environment-specific overrides
            config = self._apply_environment_overrides(config)

            # Substitute environment variables
            config = self._substitute_environment_variables(config)

            # Validate basketball intelligence configuration
            self._validate_basketball_config(config)

            logger.info(" MEDUSA VAULT: HYPER MEDUSA configuration loaded successfully")
            return config

        except Exception as e:
            logger.error(f" TITAN PROCESSING FAILED: load HYPER MEDUSA configuration: {e}")
            raise

    def _apply_environment_overrides(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Apply environment-specific configuration overrides"""
        if 'system_config' not in config:
            config['system_config'] = {}

        # Override system config based on environment
        config['system_config']['environment'] = self.environment.name
        config['system_config']['log_level'] = self.environment.log_level

        # Development/testing specific overrides
        if self.environment.debug_mode:
            config['system_config']['whisper_level'] = 'MEDUSA_DEBUG'
            if 'eternal_vigil' in config:
                config['eternal_vigil']['cycle_interval'] = 60 # Faster cycle for testing

        return config

    def _substitute_environment_variables(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Substitute environment variables in configuration values"""
        return self._recursive_env_substitute(config)

    def _recursive_env_substitute(self, obj: Any) -> Any:
        """Recursively substitute environment variables in nested structures"""
        if isinstance(obj, dict):
            return {k: self._recursive_env_substitute(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._recursive_env_substitute(item) for item in obj]
        elif isinstance(obj, str) and obj.startswith('${') and obj.endswith('}'):
            # Extract environment variable with optional default
            env_expr = obj[2:-1] # Remove ${ and }
            if ':-' in env_expr:
                env_var, default = env_expr.split(':-', 1)
                return os.getenv(env_var, default)
            else:
                env_var = env_expr
                value = os.getenv(env_var)
                if value is None:
                    logger.warning(f" Environment variable {env_var} not set")
                    return obj # Return original if not found
                return value
        else:
            return obj

    def _validate_basketball_config(self, config: Dict[str, Any]) -> None:
        """Validate basketball intelligence configuration"""
        required_sections = ['basketball_intelligence', 'eternal_vigil', 'vault_paths']

        for section in required_sections:
            if section not in config:
                logger.warning(f" Missing configuration section: {section}")

        # Validate eternal vigil settings
        if 'eternal_vigil' in config:
            vigil_config = config['eternal_vigil']
            if 'cycle_interval' not in vigil_config:
                logger.warning(" Missing eternal_vigil.cycle_interval - using default 300 seconds")
                vigil_config['cycle_interval'] = 300

        # Validate system config
        if 'system_config' in config:
            sys_config = config['system_config']
            if 'vigil_cycle' not in sys_config:
                # Copy from eternal_vigil for backward compatibility
                if 'eternal_vigil' in config and 'cycle_interval' in config['eternal_vigil']:
                    sys_config['vigil_cycle'] = config['eternal_vigil']['cycle_interval']
                else:
                    sys_config['vigil_cycle'] = 300

        logger.info(" MEDUSA VAULT: Basketball intelligence configuration validated")

# Global configuration loader instance
config_loader = ExpertConfigLoader()

def load_expert_config() -> Dict[str, Any]:
    """Convenience function to load expert configuration"""
    return config_loader.load_config()

def get_eternal_vigil_config() -> Dict[str, Any]:
    """Get eternal vigil specific configuration"""
    config = load_expert_config()
    return {
        'vigil_cycle': config.get('system_config', {}).get('vigil_cycle', 300),
        'eternal_vigil': config.get('eternal_vigil', {}),
        'basketball_intelligence': config.get('basketball_intelligence', {}),
        'vault_config': config
    }

if __name__ == "__main__":
    # Test configuration loading
    logging.basicConfig(level=logging.INFO)

    try:
        config = load_expert_config()
        print("✅ Configuration loaded successfully")
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")
        config = None
