import logging
from typing import Dict, Any, Optional
from threading import Lock
from prometheus_client import Histogram, Counter, Gauge, CollectorRegistry, REGISTRY

#!/usr/bin/env python3
"""
Expert Oracle Metrics Registry - Singleton Pattern for Prometheus Metrics
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

Provides a centralized metrics registry to prevent Prometheus metric duplication
across the expert Oracle system components.
"""


logger = logging.getLogger(__name__)

# Global lock for thread-safe singleton initialization
_registry_lock = Lock()
_metrics_registry: Optional['ExpertMetricsRegistry'] = None

# Check for Prometheus availability
try:
    _PROMETHEUS_AVAILABLE = True
    logger.info(" MEDUSA VAULT: Prometheus metrics available")
except ImportError:
    logger.warning(" Prometheus client not available. Using mock metrics.")
    _PROMETHEUS_AVAILABLE = False
    
    # Mock classes for when Prometheus is not available
    # TODO: Replace with production implementation
    # TODO: Replace with production implementation
    class MockMetric:
        def observe(self, value): pass
        def inc(self, amount=1): pass
        def set(self, value): pass
        def labels(self, **kwargs): return self
    
    Histogram = Counter = Gauge = MockMetric


class ExpertMetricsRegistry:
    """
    Singleton registry for expert Oracle system Prometheus metrics.
    Prevents metric duplication across components.
    """
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        with _registry_lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
            return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._metrics: Dict[str, Any] = {}
            self._initialize_metrics()
            ExpertMetricsRegistry._initialized = True
    
    def _initialize_metrics(self):
        """Initialize all expert Oracle metrics"""
        if not _PROMETHEUS_AVAILABLE:
            logger.info(" MEDUSA VAULT: Using mock metrics - Prometheus not available")
            self._metrics = {name: MockMetric() for name in self._get_metric_names()}
            return
        
        try:
            # Memory metrics
            self._metrics['memory_load_time'] = Histogram(
                "expert_memory_load_seconds", 
                "Time to load expert quantum memory"
            )
            self._metrics['memory_save_time'] = Histogram(
                "expert_memory_save_seconds", 
                "Time to save expert quantum memory"
            )
            self._metrics['memory_entries'] = Gauge(
                "expert_memory_entries_total", 
                "Total expert memory entries"
            )
            self._metrics['memory_errors'] = Counter(
                "expert_memory_errors_total", 
                "Expert memory operation errors", 
                ["type"]
            )
            
            # Quantum Forge metrics
            self._metrics['forge_model_load_time'] = Histogram(
                "quantum_forge_model_load_duration", 
                "Model loading duration"
            )
            self._metrics['forge_prediction_time'] = Histogram(
                "quantum_forge_prediction_duration", 
                "Prediction generation duration"
            )
            self._metrics['forge_cache_hits'] = Counter(
                "quantum_forge_cache_hits_total", 
                "Total prediction cache hits"
            )
            self._metrics['forge_cache_misses'] = Counter(
                "quantum_forge_cache_misses_total", 
                "Total prediction cache misses"
            )
            
            # Basketball-specific metrics
            self._metrics['clutch_memories'] = Gauge(
                "expert_clutch_memories_total", 
                "Total clutch-time memories"
            )
            self._metrics['momentum_score'] = Gauge(
                "expert_momentum_score", 
                "Current basketball momentum score"
            )
            self._metrics['prediction_accuracy'] = Gauge(
                "expert_prediction_accuracy", 
                "Memory access prediction accuracy"
            )
            
            # Quantum coherence metrics
            self._metrics['quantum_coherence'] = Gauge(
                "expert_quantum_coherence", 
                "System quantum coherence level"
            )
            self._metrics['entanglement_strength'] = Gauge(
                "expert_entanglement_strength", 
                "Quantum entanglement strength"
            )
            
            # Oracle Engine metrics
            self._metrics['engine_initialization_time'] = Histogram(
                "oracle_engine_init_seconds",
                "Oracle Engine initialization time"
            )
            self._metrics['engine_prophecy_time'] = Histogram(
                "oracle_engine_prophecy_seconds",                "Oracle Engine prophecy generation time"
            )
            
            logger.info(" MEDUSA VAULT: Expert metrics registry initialized successfully")
            
        except ValueError as e:
            if "Duplicated timeseries" in str(e):
                logger.warning(f" Metrics already exist in registry: {e}")
                # Use mock metrics as fallback
                self._metrics = {name: MockMetric() for name in self._get_metric_names()}
            else:
                raise e
    
    def _get_metric_names(self) -> list:
        """Get list of all metric names"""
        return [
            'memory_load_time', 'memory_save_time', 'memory_entries', 'memory_errors',
            'forge_model_load_time', 'forge_prediction_time', 'forge_cache_hits', 'forge_cache_misses',
            'clutch_memories', 'momentum_score', 'prediction_accuracy',
            'quantum_coherence', 'entanglement_strength',
            'engine_initialization_time', 'engine_prophecy_time'
        ]
    
    def get_metric(self, metric_name: str) -> Any:
        """
        Get a metric by name.
        
        Args:
            metric_name: Name of the metric to retrieve
        
        Returns:
            Prometheus metric object or MockMetric
        """
        return self._metrics.get(metric_name, MockMetric())
    
    def get_all_metrics(self) -> Dict[str, Any]:
        """Get all metrics"""
        return self._metrics.copy()
    
    def is_prometheus_available(self) -> bool:
        """Check if Prometheus is available"""
        return _PROMETHEUS_AVAILABLE
    
    def register_metric(self, name: str, value: Any, category: str = "general") -> None:
        """
        Register a new metric dynamically.
        
        Args:
            name: Metric name
            value: Metric value or configuration
            category: Metric category for organization
        """
        try:
            if not _PROMETHEUS_AVAILABLE:
                self._metrics[name] = MockMetric()
                return
            
            # Handle different metric types
            if isinstance(value, (int, float)):
                self._metrics[name] = Gauge(f"expert_{name}", f"Expert metric: {name}")
                self._metrics[name].set(value)
            elif isinstance(value, bool):
                self._metrics[name] = Gauge(f"expert_{name}_flag", f"Expert flag: {name}")
                self._metrics[name].set(1 if value else 0)
            else:
                # Create a generic gauge
                self._metrics[name] = Gauge(f"expert_{name}_generic", f"Expert metric: {name}")
                
            
        except Exception as e:
            logger.warning(f"🏀 ORACLE WARNING: Failed to register metric '{name}': {e}")
            self._metrics[name] = MockMetric()


def get_expert_metrics() -> ExpertMetricsRegistry:
    """
    Get the singleton expert metrics registry.
    
    Returns:
        ExpertMetricsRegistry: The singleton metrics registry instance
    """
    global _metrics_registry
    
    if _metrics_registry is None:
        with _registry_lock:
            if _metrics_registry is None:
                _metrics_registry = ExpertMetricsRegistry()
    
    return _metrics_registry


# Convenience functions for common metrics
def get_memory_metrics():
    """Get memory-related metrics"""
    registry = get_expert_metrics()
    return {
        'load_time': registry.get_metric('memory_load_time'),
        'save_time': registry.get_metric('memory_save_time'),
        'entries': registry.get_metric('memory_entries'),
        'errors': registry.get_metric('memory_errors'),
    }

def get_forge_metrics():
    """Get Quantum Forge metrics"""
    registry = get_expert_metrics()
    return {
        'model_load_time': registry.get_metric('forge_model_load_time'),
        'prediction_time': registry.get_metric('forge_prediction_time'),
        'cache_hits': registry.get_metric('forge_cache_hits'),
        'cache_misses': registry.get_metric('forge_cache_misses'),
    }

def get_basketball_metrics():
    """Get basketball-specific metrics"""
    registry = get_expert_metrics()
    return {
        'clutch_memories': registry.get_metric('clutch_memories'),
        'momentum_score': registry.get_metric('momentum_score'),
        'prediction_accuracy': registry.get_metric('prediction_accuracy'),
    }

def get_quantum_metrics():
    """Get quantum coherence metrics"""
    registry = get_expert_metrics()
    return {
        'coherence': registry.get_metric('quantum_coherence'),
        'entanglement': registry.get_metric('entanglement_strength'),
    }

def register_metric(name: str, value: Any, category: str = "general") -> None:
    """
    Global function to register a metric with the expert metrics registry.
    
    Args:
        name: Metric name
        value: Metric value
        category: Metric category
    """
    registry = get_expert_metrics()
    registry.register_metric(name, value, category)
