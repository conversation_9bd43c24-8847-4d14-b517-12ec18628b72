import jwt
import time
import hashlib
import secrets
import asyncio
import re # Added for regex operations
from datetime import datetime, timedelta, timezone # Added timezone for consistent UTC usage
from typing import Optional, Dict, Any, Set, List, Union
from enum import Enum
from fastapi import HTTPException, Request, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse
from pydantic import BaseModel, Field
import redis.asyncio as redis
import logging
from collections import defaultdict
from backend.middleware.feature_flags import UserTier, get_user_tier_from_context, is_feature_enabled
from fastapi import FastAPI, Depends
import uvicorn

"""
HYPER MEDUSA NEURAL VAULT™ - Expert Authentication Middleware

=============================================================

Enterprise-grade security with neural threat detection and advanced auth

Version: 1.0.0 | Classification: EXPERT | Security Level: MAXIMUM

"""



try:
    pass
except ImportError:
    redis = None  # Will use MockRedisClient later if redis is not available



# Configure expert logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("HYPER_MEDUSA_AUTH")

class SecurityLevel(str, Enum):
    """Expert security level classifications"""
    PUBLIC = "public"
    STANDARD = "standard"
    PREMIUM = "premium"
    NEURAL_VAULT = "neural_vault"
    QUANTUM_SECURE = "quantum_secure"
    EXPERT = "expert"
    ADMINISTRATIVE = "administrative"

    # Define a custom comparison for levels if needed, higher value means higher security
    def __lt__(self, other):
        if self.__class__ is other.__class__:
            order = list(self.__class__)
            return order.index(self) < order.index(other)
        return NotImplemented

class ThreatLevel(str, Enum):
    """Neural threat assessment levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"
    NEURAL_BLOCK = "neural_block"

class ExpertAuthContext(BaseModel):
    """Expert authentication context model"""
    vault_user_id: str = Field(..., description="User identifier")
    auth_type: str = Field(..., description="Authentication method")
    security_level: SecurityLevel = Field(..., description="Security clearance")
    aegis_session_id: str = Field(..., description="Session identifier")
    neural_score: float = Field(0.0, description="Neural trust score")
    threat_level: ThreatLevel = Field(ThreatLevel.LOW, description="Threat assessment")
    permissions: List[str] = Field(default_factory=list, description="User permissions")
    rate_limit_tier: str = Field("standard", description="Rate limit tier")
    last_activity: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="Last activity") # Use timezone-aware datetime

class ExpertSecurityEvent(BaseModel):
    """Expert security event model"""
    event_id: str = Field(..., description="Event identifier")
    event_type: str = Field(..., description="Event type")
    vault_user_id: Optional[str] = Field(None, description="User ID if available")
    ip_address: str = Field(..., description="Client IP address")
    threat_level: ThreatLevel = Field(..., description="Threat level")
    details: Dict[str, Any] = Field(default_factory=dict, description="Event details")
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="Event timestamp") # Use timezone-aware datetime
    neural_analysis: Dict[str, Any] = Field(default_factory=dict, description="Neural analysis")

class ExpertAuthenticationMiddleware(BaseHTTPMiddleware):
    """HYPER MEDUSA expert authentication middleware with neural threat detection"""
    def __init__(self, app, jwt_secret: str, api_keys: Dict[str, str] = None, redis_client=None):
        super().__init__(app)
        self.jwt_secret = jwt_secret
        # NOTE: For production, API keys should ideally be stored as securely hashed values (e.g., bcrypt)
        # and compared using a secure hashing function, rather than plain text.
        self.api_keys = api_keys or {}
        self.redis_client = redis_client
        self.security_events: List[ExpertSecurityEvent] = [] # In-memory list for demo/debugging

        # Expert endpoint security mapping (cleaned up the list syntax)
        self.public_endpoints = {
            "/", "/health", "/docs", "/redoc", "/openapi.json",
            "/api/v1/info/health"
        }
        self.neural_vault_endpoints = {
            "/api/v1/predictions/neural-predictions",
            "/api/v1/simulation/quantum-", # Consider if this is a prefix or exact path
            "/api/v1/insights/neural-discoveries"
        }

        # Rate limiting tiers
        self.rate_limits = {
            "public": 100, # requests per minute
            "standard": 500,
            "premium": 2000,
            "neural_vault": 10000,
            "quantum_secure": 50000
        }

        # Neural threat detection patterns
        self.threat_patterns = {
            "sql_injection": r"(\bUNION\b|\bSELECT\b|\bINSERT\b|\bDELETE\b|\bDROP\b)",
            "xss_attempt": r"(<script|javascript:|onload=|onerror=)",
            "brute_force": "multiple_failed_attempts", # This is a conceptual flag, handled by _check_brute_force_pattern
            "anomalous_requests": "unusual_pattern_detected" # Conceptual flag
        }

    async def dispatch(self, request: Request, call_next):
        """Expert request processing with neural threat detection"""
        start_time = time.time()
        client_ip = request.client.host if request.client else "unknown" # Handle cases where client might be None

        try:
            # Neural threat assessment
            threat_assessment = await self._assess_neural_threat(request, client_ip)

            # Block critical threats immediately
            if threat_assessment.threat_level == ThreatLevel.CRITICAL:
                await self._log_security_event("CRITICAL_THREAT_BLOCKED", client_ip, threat_assessment.details, ThreatLevel.CRITICAL) # Pass threat_level
                return JSONResponse(
                    status_code=403,
                    content={
                        "error": "HYPER MEDUSA NEURAL VAULT - Access Denied",
                        "code": "NEURAL_THREAT_DETECTED",
                        "message": "Neural security systems have detected suspicious activity"
                    }
                )

            # Skip auth for public endpoints
            if request.url.path in self.public_endpoints:
                # Still apply rate limiting for public endpoints
                if not await self._check_rate_limit(client_ip, "public"):
                    await self._log_security_event("PUBLIC_RATE_LIMITED", client_ip, {"path": request.url.path}, ThreatLevel.MEDIUM)
                    return self._rate_limit_response()
                return await call_next(request)

            # Enhanced API key validation
            api_key = request.headers.get("X-API-Key") or request.headers.get("X-MEDUSA-KEY")
            if api_key:
                auth_result = await self._validate_expert_api_key(api_key, client_ip, request)
                if auth_result:
                    request.state.auth_context = auth_result
                    return await call_next(request)

            # Enhanced JWT validation
            auth_header = request.headers.get("Authorization")
            if auth_header and auth_header.startswith("Bearer "):
                token = auth_header.split(" ")[1]
                auth_result = await self._validate_expert_jwt(token, client_ip, request)
                if auth_result:
                    request.state.auth_context = auth_result
                    return await call_next(request)

            # No valid authentication found
            await self._log_security_event("UNAUTHORIZED_ACCESS_ATTEMPT", client_ip, {
                "path": request.url.path,
                "method": request.method,
                "user_agent": request.headers.get("User-Agent", "Unknown")
            }, ThreatLevel.HIGH) # Mark as high threat
            return JSONResponse(
                status_code=401,
                content={
                    "error": "HYPER MEDUSA NEURAL VAULT - Authentication Required",
                    "code": "AUTHENTICATION_REQUIRED",
                    "message": "Access to neural vault requires valid credentials",
                    "supported_methods": ["Bearer JWT", "X-API-Key", "X-MEDUSA-KEY"]
                }
            )

        except HTTPException as he:
            # Re-raise FastAPI HTTPExceptions directly
            raise he
        except Exception as e:
            logger.exception(f"HYPER MEDUSA AUTH ERROR: {e}") # Use logger.exception for full traceback
            await self._log_security_event("AUTHENTICATION_ERROR", client_ip, {"error": str(e)}, ThreatLevel.CRITICAL)
            return JSONResponse(
                status_code=500,
                content={
                    "error": "HYPER MEDUSA NEURAL VAULT - Authentication Error",
                    "code": "AEGIS_AUTH_SYSTEM_ERROR",
                    "message": "Neural authentication system encountered an error"
                }
            )
        finally:
            # Log request processing time
            processing_time = time.time() - start_time
            logger.info(f"HYPER MEDUSA: Auth processed in {processing_time:.3f}s for {client_ip}")

    async def _assess_neural_threat(self, request: Request, client_ip: str) -> ExpertSecurityEvent:
        """Neural threat assessment using advanced pattern detection"""
        threat_score = 0.0
        threat_details = {}

        # Analyze request path and query string
        path = request.url.path
        query_string = str(request.url.query) if request.url.query else ""

        # Check for injection patterns
        for threat_type, pattern in self.threat_patterns.items():
            if threat_type in ["sql_injection", "xss_attempt"]:
                if re.search(pattern, path + query_string, re.IGNORECASE):
                    threat_score += 50.0
                    threat_details[threat_type] = True

        # Check rate limiting and suspicious patterns (using same underlying Redis check)
        if await self._is_rate_limited(client_ip): # This checks if _rate_limit returns False (meaning rate limited)
            threat_score += 20.0
            threat_details["rate_limit_exceeded"] = True

        # Check for brute force patterns
        if await self._check_brute_force_pattern(client_ip):
            threat_score += 30.0
            threat_details["brute_force_detected"] = True

        # Determine threat level
        if threat_score >= 80:
            threat_level = ThreatLevel.CRITICAL
        elif threat_score >= 60:
            threat_level = ThreatLevel.HIGH
        elif threat_score >= 30:
            threat_level = ThreatLevel.MEDIUM
        else:
            threat_level = ThreatLevel.LOW

        return ExpertSecurityEvent(
            event_id=secrets.token_hex(16),
            event_type="neural_threat_assessment",
            ip_address=client_ip,
            threat_level=threat_level,
            details=threat_details,
            neural_analysis={"threat_score": threat_score}
        )

    async def _validate_expert_api_key(self, api_key: str, client_ip: str, request: Request) -> Optional[ExpertAuthContext]:
        """Validate API key with expert security features"""
        # In a real system, API keys would be securely hashed and compared, not plain text.
        # This implementation assumes self.api_keys is a dictionary of plain_key -> user_id
        # For true security, self.api_keys would map hashed_key -> user_id, and you'd hash `api_key` before lookup.
        
        vault_user_id = self.api_keys.get(api_key)
        
        if not vault_user_id:
            await self._log_security_event("INVALID_API_KEY", client_ip, {"api_key_hash": hashlib.sha256(api_key.encode()).hexdigest()[:16]}, ThreatLevel.MEDIUM)
            return None

        # Check rate limiting for API key (use the identifier for rate limiting)
        rate_limit_identifier = f"api_key:{vault_user_id}" # Rate limit by user linked to API key
        if not await self._check_rate_limit(rate_limit_identifier, "standard"):
            await self._log_security_event("VAULT_API_KEY_RATE_LIMITED", client_ip, {"vault_user_id": vault_user_id}, ThreatLevel.MEDIUM)
            return None

        # Create auth context
        aegis_session_id = secrets.token_hex(16)
        neural_score = await self._calculate_neural_score(client_ip, vault_user_id)

        return ExpertAuthContext(
            vault_user_id=vault_user_id,
            auth_type="api_key",
            security_level=SecurityLevel.STANDARD,
            aegis_session_id=aegis_session_id,
            neural_score=neural_score,
            threat_level=ThreatLevel.LOW, # Initial assessment, can be refined
            permissions=["read", "write"],
            rate_limit_tier="standard"
        )

    async def _validate_expert_jwt(self, token: str, client_ip: str, request: Request) -> Optional[ExpertAuthContext]:
        """Validate JWT token with expert security features"""
        try:
            # Decode JWT
            payload = jwt.decode(token, self.jwt_secret, algorithms=["HS256"])

            vault_user_id = payload.get("vault_user_id")
            role = payload.get("role", "user")

            if not vault_user_id:
                await self._log_security_event("INVALID_JWT_PAYLOAD", client_ip, {"missing": "vault_user_id"}, ThreatLevel.MEDIUM)
                return None

            # Check token blacklist
            if await self._is_token_blacklisted(token):
                await self._log_security_event("BLACKLISTED_TOKEN_USED", client_ip, {"vault_user_id": vault_user_id}, ThreatLevel.HIGH)
                raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="HYPER MEDUSA: Token has been revoked")


            # Determine security level based on role (using more granular checks)
            security_level_str = payload.get("security_level", "standard").lower()
            if security_level_str in [s.value for s in SecurityLevel]:
                security_level = SecurityLevel(security_level_str)
            else:
                security_level = SecurityLevel.STANDARD # Default to standard if invalid

            # Determine rate limit tier
            rate_limit_tier_str = payload.get("rate_limit_tier", role).lower() # Use role as fallback
            rate_limit_tier = rate_limit_tier_str if rate_limit_tier_str in self.rate_limits else "standard"

            # Check rate limiting for user
            if not await self._check_rate_limit(f"user:{vault_user_id}", rate_limit_tier):
                await self._log_security_event("USER_RATE_LIMITED", client_ip, {"vault_user_id": vault_user_id, "tier": rate_limit_tier}, ThreatLevel.MEDIUM)
                raise HTTPException(
                    status_code=429,
                    detail=f"HYPER MEDUSA NEURAL VAULT - AEGIS PROTECTION: Rate limit exceeded for {rate_limit_tier} tier."
                )

            # Create auth context
            aegis_session_id = secrets.token_hex(16)
            neural_score = await self._calculate_neural_score(client_ip, vault_user_id)

            return ExpertAuthContext(
                vault_user_id=vault_user_id,
                auth_type="jwt",
                security_level=security_level,
                aegis_session_id=aegis_session_id,
                neural_score=neural_score,
                threat_level=ThreatLevel.LOW, # Initial assessment
                permissions=payload.get("permissions", []), # Ensure permissions is a list
                rate_limit_tier=rate_limit_tier
            )

        except jwt.ExpiredSignatureError:
            await self._log_security_event("EXPIRED_JWT_TOKEN", client_ip, {}, ThreatLevel.LOW)
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="HYPER MEDUSA: Neural vault token expired")
        except jwt.InvalidTokenError:
            await self._log_security_event("INVALID_JWT_TOKEN", client_ip, {}, ThreatLevel.MEDIUM)
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="HYPER MEDUSA: Invalid neural vault token")
        except Exception as e:
            logger.error(f"JWT validation error: {e}")
            await self._log_security_event("JWT_VALIDATION_ERROR", client_ip, {"error": str(e)}, ThreatLevel.HIGH)
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="HYPER MEDUSA: JWT validation error") # Generic error

    async def _check_rate_limit(self, identifier: str, tier: str) -> bool:
        """Check rate limiting for identifier using Redis (fail open)"""
        if not self.redis_client:
            logger.warning("Redis client not available for rate limiting. Rate limit check bypassed.")
            return True # No Redis, no rate limiting - fail open

        try:
            limit = self.rate_limits.get(tier, 100)
            current_minute = int(time.time() // 60)
            key = f"rate_limit:{identifier}:{current_minute}"
            
            # Use INCR for atomic increment and check
            current_count = await self.redis_client.incr(key)

            if current_count == 1:
                # Set expiration for the key if it's the first hit in this minute
                await self.redis_client.expire(key, 60) # Expire after 60 seconds (end of minute)

            if current_count > limit:
                logger.warning(f"Rate limit exceeded for {identifier} (tier: {tier}, limit: {limit}, current: {current_count})")
                return False
            return True
        except Exception as e:
            logger.error(f"Redis rate limit check error for {identifier}: {e}. Falling back to fail-open.")
            return True # Fail open on Redis errors

    async def _is_rate_limited(self, client_ip: str) -> bool:
        """Check if client IP is currently rate limited (more accurate if Redis is used for this specific check)"""
        # This re-uses the _check_rate_limit function for consistency.
        # If _check_rate_limit returns False (meaning rate limit *is* exceeded), then this function returns True.
        # If _check_rate_limit returns True (meaning request *is* allowed), then this function returns False.
        return not await self._check_rate_limit(client_ip, "public")

    async def _check_brute_force_pattern(self, client_ip: str) -> bool:
        """Check for brute force attack patterns (using Redis for failed attempts)"""
        if not self.redis_client:
            return False # Cannot check without Redis

        try:
            key = f"failed_attempts:{client_ip}"
            failed_count = await self.redis_client.get(key)
            return int(failed_count or 0) > 10 # Threshold for brute force
        except Exception as e:
            logger.error(f"Error checking brute force pattern for {client_ip}: {e}")
            return False # Assume no brute force on error

    async def _is_token_blacklisted(self, token: str) -> bool:
        """Check if token is blacklisted using Redis"""
        if not self.redis_client:
            return False

        try:
            token_hash = hashlib.sha256(token.encode()).hexdigest()
            is_blacklisted = await self.redis_client.get(f"blacklist:{token_hash}")
            return is_blacklisted is not None
        except Exception as e:
            logger.error(f"Error checking token blacklist: {e}")
            return False

    async def _calculate_neural_score(self, client_ip: str, vault_user_id: str) -> float:
        """Calculate neural trust score based on various factors"""
        try:
            score = 50.0 # Base neutral score

            if self.redis_client:
                # Check user history/reputation
                user_reputation_key = f"user_reputation:{vault_user_id}"
                user_reputation = await self.redis_client.get(user_reputation_key)
                if user_reputation:
                    # Assume reputation is a float value from -50 to 50
                    score += float(user_reputation)

                # Check IP reputation
                ip_reputation_key = f"ip_reputation:{client_ip}"
                ip_reputation = await self.redis_client.get(ip_reputation_key)
                if ip_reputation:
                    score += float(ip_reputation) # Assume IP reputation is also a float

                # Add factors like recent failed login attempts, unusual access times etc.
                failed_login_key = f"failed_logins:{vault_user_id}"
                failed_count = await self.redis_client.get(failed_login_key)
                if failed_count and int(failed_count) > 0:
                    score -= (int(failed_count) * 5) # Deduct points for failed logins

            # Normalize score between 0-100
            return max(0.0, min(100.0, score))
        except Exception as e:
            logger.error(f"Neural score calculation error: {e}. Returning default score.")
            return 50.0 # Default neutral score on error

    async def _log_security_event(self, event_type: str, client_ip: str, details: Dict[str, Any], threat_level: ThreatLevel = ThreatLevel.MEDIUM, vault_user_id: Optional[str] = None):
        """Log security event to in-memory list and Redis (if available)"""
        event = ExpertSecurityEvent(
            event_id=secrets.token_hex(16),
            event_type=event_type,
            vault_user_id=vault_user_id, # Pass user_id if available
            ip_address=client_ip,
            threat_level=threat_level,
            details=details,
            neural_analysis={"event_score": self._calculate_event_score(event_type, threat_level)} # Placeholder for real neural analysis
        )
        self.security_events.append(event) # Store in memory (for this instance)
        logger.warning(f"HYPER MEDUSA SECURITY EVENT: {event_type} from {client_ip} (Threat: {threat_level.value}) - {details}")

        # Store in Redis if available
        if self.redis_client:
            try:
                event_key = f"security_event:{event.event_id}"
                # Use .model_dump_json() for Pydantic V2 or .json() for Pydantic V1 for serialization
                await self.redis_client.setex(event_key, 86400, event.model_dump_json()) # 24 hours
            except Exception as e:
                logger.error(f"TITAN PROCESSING FAILED: store security event to Redis: {e}")

    def _calculate_event_score(self, event_type: str, threat_level: ThreatLevel) -> float:
        """Simple helper to assign a score to a security event for neural analysis"""
        score_map = {
            ThreatLevel.LOW: 10,
            ThreatLevel.MEDIUM: 30,
            ThreatLevel.HIGH: 60,
            ThreatLevel.CRITICAL: 90,
            ThreatLevel.NEURAL_BLOCK: 100
        }
        base_score = score_map.get(threat_level, 0)
        # Further refine based on event_type if needed
        return base_score

    def _rate_limit_response(self) -> JSONResponse:
        """Return AEGIS PROTECTION: Rate limit exceeded response"""
        return JSONResponse(
            status_code=429,
            content={
                "error": "HYPER MEDUSA NEURAL VAULT - AEGIS PROTECTION: Rate limit exceeded",
                "code": "RATE_LIMIT_EXCEEDED",
                "message": "Neural vault protection activated - too many requests",
                "retry_after": 60
            },
            headers={"Retry-After": "60"}
        )

# Expert rate limit middleware (moved outside ExpertAuthenticationMiddleware for better separation)
class ExpertRateLimitMiddleware(BaseHTTPMiddleware):
    """HYPER MEDUSA Neural Vault Rate Limiting with Advanced Analytics"""
    def __init__(self, app, redis_client=None, default_limit: int = 100):
        super().__init__(app)
        self.redis_client = redis_client
        self.default_limit = default_limit
        self.request_counts: Dict[str, Dict[str, int]] = {} # In-memory fallback
        self._memory_lock = asyncio.Lock() # Lock for in-memory rate limiting

        # Tier-based rate limits
        self.tier_limits = {
            "public": 60, # requests per minute
            "standard": 300,
            "premium": 1000,
            "neural_vault": 5000,
            "quantum_secure": 25000
        }

    async def dispatch(self, request: Request, call_next):
        """Neural vault rate limiting with intelligent tier detection"""
        client_ip = request.client.host if request.client else "unknown"
        current_minute = int(time.time() // 60)

        # Determine rate limit tier from auth context (if authentication middleware has run)
        tier = "public"
        if hasattr(request.state, "auth_context") and request.state.auth_context:
            auth_context: ExpertAuthContext = request.state.auth_context
            tier = auth_context.rate_limit_tier # Use rate_limit_tier from auth_context
        
        limit = self.tier_limits.get(tier, self.default_limit)
        identifier = client_ip # Default to IP
        if hasattr(request.state, "auth_context") and request.state.auth_context:
            identifier = f"user:{request.state.auth_context.vault_user_id}" # Use user ID if authenticated

        # Use Redis for distributed rate limiting if available
        if self.redis_client:
            is_allowed = await self._check_redis_rate_limit(identifier, tier, limit, current_minute)
        else:
            is_allowed = await self._check_memory_rate_limit(identifier, limit, current_minute)

        if not is_allowed:
            return JSONResponse(
                status_code=429,
                content={
                    "error": "HYPER MEDUSA NEURAL VAULT - AEGIS PROTECTION: Rate limit exceeded",
                    "code": "NEURAL_RATE_LIMIT",
                    "message": f"Neural vault protection: {tier} tier limit exceeded",
                    "tier": tier,
                    "limit": limit,
                    "retry_after": 60
                },
                headers={"Retry-After": "60"}
            )

        return await call_next(request)

    async def _check_redis_rate_limit(self, identifier: str, tier: str, limit: int, current_minute: int) -> bool:
        """Check rate limit using Redis (fail open)"""
        try:
            key = f"neural_rate:{tier}:{identifier}:{current_minute}"
            current_count = await self.redis_client.incr(key)

            if current_count == 1:
                # Set expiration for the key (the start of the next minute)
                await self.redis_client.expireat(key, (current_minute + 1) * 60)

            if current_count > limit:
                logger.warning(f"Redis rate limit exceeded for {identifier} (tier: {tier}, limit: {limit}, current: {current_count})")
                return False
            return True
        except Exception as e:
            logger.error(f"Redis rate limit check error for {identifier}: {e}. Falling back to fail-open.")
            return True # Fail open on Redis errors

    async def _check_memory_rate_limit(self, identifier: str, limit: int, current_minute: int) -> bool:
        """Check rate limit using in-memory storage (with lock for async safety)"""
        async with self._memory_lock:
            # Initialize client tracking
            if identifier not in self.request_counts:
                self.request_counts[identifier] = {}

            # Clean old entries (keep current and last minute)
            # This ensures counts from previous minutes are cleared
            self.request_counts[identifier] = {
                minute: count for minute, count in self.request_counts[identifier].items()
                if minute >= current_minute - 1 # Keep relevant past minute for smoothing, or just current_minute
            }

            # Count current request
            if current_minute not in self.request_counts[identifier]:
                self.request_counts[identifier][current_minute] = 0
            self.request_counts[identifier][current_minute] += 1

            # Check rate limit (summing up requests in the window)
            total_requests = sum(self.request_counts[identifier].values())
            
            if total_requests > limit:
                logger.warning(f"In-memory rate limit exceeded for {identifier} (limit: {limit}, current: {total_requests})")
                return False
            return True

class ExpertJWTHandler:
    """HYPER MEDUSA Neural Vault JWT Management"""
    def __init__(self, secret: str, redis_client=None):
        self.secret = secret
        self.redis_client = redis_client

    def create_expert_token(self, vault_user_id: str, role: str = "user", security_level: str = "standard",
                            expires_hours: int = 24, permissions: Optional[List[str]] = None) -> str:
        """Create a neural vault JWT token with expert features"""
        payload = {
            "vault_user_id": vault_user_id,
            "role": role,
            "security_level": security_level,
            "permissions": permissions if permissions is not None else ["read"], # Ensure it's a list
            "neural_score": 75.0, # Base neural score
            "aegis_session_id": secrets.token_hex(16),
            "exp": datetime.now(timezone.utc) + timedelta(hours=expires_hours), # Use timezone-aware datetime
            "iat": datetime.now(timezone.utc), # Use timezone-aware datetime
            "iss": "HYPER_MEDUSA_NEURAL_VAULT",
            "vault_version": "1.0.0"
        }
        return jwt.encode(payload, self.secret, algorithm="HS256")

    async def verify_expert_token(self, token: str) -> ExpertAuthContext:
        """Verify neural vault JWT token with enhanced validation"""
        try:
            # Check token blacklist first
            if await self._is_token_blacklisted(token):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="HYPER MEDUSA: Token has been revoked"
                )

            payload = jwt.decode(token, self.secret, algorithms=["HS256"])

            # Validate required fields
            vault_user_id = payload.get("vault_user_id")
            if not vault_user_id:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="HYPER MEDUSA: Invalid token payload - missing user ID"
                )
            
            # Determine security level
            security_level_str = payload.get("security_level", "standard").lower()
            if security_level_str in [s.value for s in SecurityLevel]:
                security_level = SecurityLevel(security_level_str)
            else:
                security_level = SecurityLevel.STANDARD # Default to standard if invalid

            # Create expert auth context
            return ExpertAuthContext(
                vault_user_id=vault_user_id,
                auth_type="jwt",
                security_level=security_level, # Use parsed SecurityLevel enum
                aegis_session_id=payload.get("aegis_session_id", secrets.token_hex(16)),
                neural_score=payload.get("neural_score", 50.0),
                threat_level=ThreatLevel.LOW, # This would be set by a prior neural assessment if integrated
                permissions=payload.get("permissions", []), # Ensure permissions is a list
                rate_limit_tier=payload.get("role", "standard") # Use role for rate limit tier by default
            )

        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="HYPER MEDUSA: Neural vault token expired"
            )
        except jwt.InvalidTokenError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="HYPER MEDUSA: Invalid neural vault token"
            )
        except Exception as e:
            logger.error(f"Unhandled error during JWT verification: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="HYPER MEDUSA: Internal error during token verification"
            )

    async def _is_token_blacklisted(self, token: str) -> bool:
        """Check if token is blacklisted using Redis"""
        if not self.redis_client:
            return False
        try:
            token_hash = hashlib.sha256(token.encode()).hexdigest()
            is_blacklisted = await self.redis_client.get(f"neural_blacklist:{token_hash}")
            return is_blacklisted is not None
        except Exception as e:
            logger.error(f"Error checking token blacklist (Redis): {e}")
            return False

    async def blacklist_token(self, token: str, reason: str = "revoked"):
        """Add token to blacklist in Redis"""
        if not self.redis_client:
            logger.warning("Redis client not available for token blacklisting.")
            return
        try:
            token_hash = hashlib.sha256(token.encode()).hexdigest()
            await self.redis_client.setex(f"neural_blacklist:{token_hash}", 86400 * 30, reason) # 30 days expiry
            logger.info(f"Token blacklisted: {token_hash[:10]}... Reason: {reason}")
        except Exception as e:
            logger.error(f"TITAN PROCESSING FAILED: blacklist token to Redis: {e}")

# Expert dependency injection
async def get_expert_auth_context(request: Request) -> ExpertAuthContext:
    """FastAPI dependency to get expert authentication context"""
    # This dependency assumes ExpertAuthenticationMiddleware has already run and populated request.state.auth_context
    if not hasattr(request.state, "auth_context") or not request.state.auth_context:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="HYPER MEDUSA: Neural vault authentication required"
        )
    return request.state.auth_context

def require_tier(min_tier: UserTier):
    """Decorator for FastAPI routes to enforce minimum user tier (Free/Pro/Enterprise)."""
    def decorator(func):
        async def wrapper(request, *args, **kwargs):
            auth_context = getattr(request.state, "auth_context", None)
            user_tier = get_user_tier_from_context(auth_context)
            if UserTier._value2member_map_[user_tier] < UserTier._value2member_map_[min_tier]:
                raise HTTPException(status_code=403, detail=f"Minimum tier '{min_tier}' required (current: {user_tier})")
            return await func(request, *args, **kwargs)
        return wrapper
    return decorator

# Enhanced RBAC: Optionally require a feature flag or tier in addition to security level
# Usage: @require_security_level(SecurityLevel.PREMIUM, feature="enable_quantum_recalibration", min_tier=UserTier.PRO)
def require_security_level(required_level: SecurityLevel, feature: str = None, min_tier: UserTier = None):
    def decorator(func):
        async def wrapper(request: Request, *args, **kwargs):
            auth_context: ExpertAuthContext = await get_expert_auth_context(request)
            # Security level check
            if auth_context.security_level < required_level:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"HYPER MEDUSA: Insufficient security clearance - {required_level.value} required (current: {auth_context.security_level.value})"
                )
            # Feature flag check
            if feature:
                tier = get_user_tier_from_context(auth_context)
                if not is_feature_enabled(feature, tier):
                    raise HTTPException(status_code=403, detail=f"Feature '{feature}' not available for your tier ({tier})")
            # Tier check
            if min_tier:
                tier = get_user_tier_from_context(auth_context)
                if UserTier._value2member_map_[tier] < UserTier._value2member_map_[min_tier]:
                    raise HTTPException(status_code=403, detail=f"Minimum tier '{min_tier}' required (current: {tier})")
            return await func(request=request, *args, **kwargs)
        return wrapper
    return decorator

# Optionally, update require_permission to accept a feature or tier as well
def require_permission(required_permission: str, feature: str = None, min_tier: UserTier = None):
    def decorator(func):
        async def wrapper(request: Request, *args, **kwargs):
            auth_context: ExpertAuthContext = await get_expert_auth_context(request)
            if required_permission not in auth_context.permissions:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"HYPER MEDUSA: Permission denied - '{required_permission}' required"
                )
            # Feature flag check
            if feature:
                tier = get_user_tier_from_context(auth_context)
                if not is_feature_enabled(feature, tier):
                    raise HTTPException(status_code=403, detail=f"Feature '{feature}' not available for your tier ({tier})")
            # Tier check
            if min_tier:
                tier = get_user_tier_from_context(auth_context)
                if UserTier._value2member_map_[tier] < UserTier._value2member_map_[min_tier]:
                    raise HTTPException(status_code=403, detail=f"Minimum tier '{min_tier}' required (current: {tier})")
            return await func(request=request, *args, **kwargs)
        return wrapper
    return decorator

# Legacy compatibility (deprecated) - These classes should be phased out
class RateLimitMiddleware(ExpertRateLimitMiddleware):
    """Legacy rate limiting - use ExpertRateLimitMiddleware instead"""
    pass

class JWTHandler(ExpertJWTHandler):
    """Legacy JWT handler - use ExpertJWTHandler instead"""
    def create_token(self, vault_user_id: str, role: str = "user", expires_hours: int = 24) -> str:
        # Default to standard security level for legacy compatibility
        return self.create_expert_token(vault_user_id, role, "standard", expires_hours)

    def verify_token(self, token: str) -> Dict[str, Any]:
        """Legacy verify method - returns raw payload (less secure)"""
        try:
            # Note: This version does not use ExpertAuthContext or blacklist checks
            return jwt.decode(token, self.secret, algorithms=["HS256"])
        except jwt.ExpiredSignatureError:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Token expired")
        except jwt.InvalidTokenError:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")

# FastAPI dependency for route protection (legacy)
security = HTTPBearer()

def get_current_user(credentials: HTTPAuthorizationCredentials = security):
    """FastAPI dependency to get current authenticated user (legacy placeholder)"""
    # This is a legacy function - use get_expert_auth_context instead for full context
    # Implement actual user retrieval here if needed, based on credentials.token
    # For now, it's just a placeholder as the ExpertAuthContext is the preferred method.
    raise HTTPException(status_code=status.HTTP_501_NOT_IMPLEMENTED, detail="Legacy get_current_user is a placeholder. Use get_expert_auth_context.")


def require_role(required_role: str):
    """Decorator for role-based access control (legacy placeholder)"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Legacy function - use require_security_level or require_permission instead
            raise HTTPException(status_code=status.HTTP_501_NOT_IMPLEMENTED, detail="Legacy require_role is a placeholder. Use require_security_level or require_permission.")
        return wrapper
    return decorator

# --- End of Legacy Compatibility Section ---

# Mock Redis Client for testing environments
# TODO: Replace with production implementation
# TODO: Replace with production implementation
class MockRedisClient:
    """Mock Redis client for testing when a real Redis server is not available."""
    def __init__(self):
        self._data = {}
        self._locks = defaultdict(asyncio.Lock) # For mocking atomic operations
        
    async def get(self, key: str):
        """Mock Redis GET command."""
        return self._data.get(key)

    async def set(self, key: str, value: Any, ex: Optional[int] = None):
        """Mock Redis SET command."""
        self._data[key] = value
        # Simplified expiration handling for mock
        if ex:
            # Schedule a task to remove the key after 'ex' seconds
            asyncio.get_event_loop().call_later(ex, lambda: self._data.pop(key, None))
        return True

    async def setex(self, name: str, time: int, value: Any):
        """Mock Redis SETEX command."""
        return await self.set(name, value, ex=time)

    async def incr(self, key: str):
        """Mock Redis INCR command."""
        async with self._locks[key]:
            current_value = int(self._data.get(key, 0) or 0) # Handle None or non-numeric
            new_value = current_value + 1
            self._data[key] = new_value
            return new_value

    async def expire(self, key: str, ttl: int):
        """Mock Redis EXPIRE command."""
        if key in self._data:
            asyncio.get_event_loop().call_later(ttl, lambda: self._data.pop(key, None))
            return True
        return False
    
    async def expireat(self, key: str, timestamp: int):
        """Mock Redis EXPIREAT command."""
        now = int(time.time())
        ttl = max(0, timestamp - now) # Calculate TTL from timestamp
        return await self.expire(key, ttl)

    async def delete(self, *keys):
        """Mock Redis DELETE command."""
        deleted_count = 0
        for key in keys:
            if key in self._data:
                del self._data[key]
                deleted_count += 1
        return deleted_count

    async def ping(self):
        """Mock Redis PING command."""
        return True

    async def close(self):
        """Mock Redis CLOSE command."""
        # No actual connection to close for mock
        self._data.clear() # Clear internal data for a clean state
        # No explicit lock cleanup needed, they'll be garbage collected

# Factory function for getting Redis client (real or mock)
_redis_client_instance: Optional[Any] = None

async def get_redis_client_for_auth(config_host: str = "localhost", config_port: int = 6379, config_password: Optional[str] = None, config_db: int = 0) -> Any:
    """
    Get a Redis client instance, attempting a real connection first,
    then falling back to a mock client for testing or if Redis is unavailable.
    """
    global _redis_client_instance
    if _redis_client_instance is None:
        try:
            _redis_client_instance = redis.Redis(
                host=config_host,
                port=config_port,
                password=config_password,
                db=config_db,
                decode_responses=True, # Decode responses to strings
                socket_timeout=5,
                socket_connect_timeout=5,
                health_check_interval=30 # Periodically check connection health
            )
            await _redis_client_instance.ping()
            logger.info(f"Auth Redis client connected to {config_host}:{config_port}")
        except Exception as e:
            logger.warning(f"Auth Redis connection failed ({e}), using mock client for authentication.")
            _redis_client_instance = MockRedisClient()
    return _redis_client_instance

async def close_redis_client_for_auth():
    """Close the global Redis client connection used by authentication middleware."""
    global _redis_client_instance
    if _redis_client_instance and hasattr(_redis_client_instance, 'close'):
        await _redis_client_instance.close()
    _redis_client_instance = None
    logger.info("Auth Redis client connection closed.")

if __name__ == '__main__':
    # Basic FastAPI app setup for testing middleware

    app = FastAPI(title="HYPER MEDUSA Auth Test")

    # Mock JWT Secret and API Keys
    TEST_JWT_SECRET = "supersecretneuralvaultkey"
    TEST_API_KEYS = {
        "valid_api_key_123": "test_user_api",
        "another_key_456": "premium_user_api"
    }

    # Initialize Redis client for the middleware
    # In a real app, this would be part of startup/shutdown events
    TEST_REDIS_CLIENT = None

    @app.on_event("startup")
    async def startup_event():
        global TEST_REDIS_CLIENT
        # This simulates getting the config from `backend.config.production_config`
        # For a full test, you'd ensure a real Redis instance is running or use the mock as default.
        # Here we hardcode mock details to ensure it runs standalone.
        TEST_REDIS_CLIENT = await get_redis_client_for_auth(
            config_host="localhost", # Or your Redis host
            config_port=6379,       # Or your Redis port
            config_password=None,   # Your Redis password
            config_db=1             # Use a different DB than other services if possible
        )
        logger.info("Application startup: Redis client for auth initialized.")
        # Add the authentication middleware to the app
        app.add_middleware(
            ExpertAuthenticationMiddleware,
            jwt_secret=TEST_JWT_SECRET,
            api_keys=TEST_API_KEYS,
            redis_client=TEST_REDIS_CLIENT
        )
        app.add_middleware(
            ExpertRateLimitMiddleware,
            redis_client=TEST_REDIS_CLIENT
        )
        logger.info("Authentication and Rate Limit Middlewares added.")

    @app.on_event("shutdown")
    async def shutdown_event():
        await close_redis_client_for_auth()
        logger.info("Application shutdown: Redis client for auth closed.")

    # Test Endpoints
    @app.get("/health", tags=["Public"])
    async def health_check():
        return {"status": "HYPER MEDUSA Auth is operational!"}

    @app.get("/api/v1/public-data", tags=["Public"])
    async def get_public_data():
        return {"message": "This is public data, accessible without authentication."}

    @app.get("/api/v1/standard-data", tags=["Standard User"])
    async def get_standard_data(auth_context: ExpertAuthContext = Depends(get_expert_auth_context)):
        return {
            "message": "This is standard user data.",
            "auth_context": auth_context.model_dump() # Use model_dump() for Pydantic V2
        }

    @app.get("/api/v1/premium-data", tags=["Premium User"])
    @require_security_level(SecurityLevel.PREMIUM)
    async def get_premium_data(request: Request): # Must accept request because decorator adds it
        auth_context: ExpertAuthContext = request.state.auth_context # Access from request.state
        return {
            "message": "This is premium user data.",
            "auth_context": auth_context.model_dump()
        }

    @app.get("/api/v1/admin-action", tags=["Admin"])
    @require_permission("admin:full_access")
    async def perform_admin_action(request: Request): # Must accept request
        auth_context: ExpertAuthContext = request.state.auth_context
        return {
            "message": "Admin action performed successfully.",
            "auth_context": auth_context.model_dump()
        }
    
    @app.post("/api/v1/auth/login", tags=["Authentication"])
    async def login_user():
        # In a real app, this would receive username/password, validate, and return a JWT
        # For testing, we'll mock token creation
        jwt_handler = ExpertJWTHandler(TEST_JWT_SECRET, TEST_REDIS_CLIENT)
        
        # Example: Create a standard user token
        standard_token = jwt_handler.create_expert_token(
            vault_user_id="test_standard_user", 
            role="user", 
            security_level="standard", 
            permissions=["read", "write"]
        )
        
        # Example: Create a premium user token
        premium_token = jwt_handler.create_expert_token(
            vault_user_id="test_premium_user", 
            role="premium", 
            security_level="premium", 
            permissions=["read", "write", "premium_features"]
        )

        # Example: Create an admin token
        admin_token = jwt_handler.create_expert_token(
            vault_user_id="test_admin_user",
            role="admin",
            security_level="neural_vault",
            permissions=["read", "write", "premium_features", "admin:full_access"],
            expires_hours=0.01 # Very short expiry for admin token example
        )

        return {
            "message": "Mock login successful",
            "standard_user_token": standard_token,
            "premium_user_token": premium_token,
            "admin_user_token": admin_token,
            "api_key_example": list(TEST_API_KEYS.keys())[0] # Provide an example API key
        }

    @app.post("/api/v1/auth/blacklist-token", tags=["Authentication"])
    async def blacklist_jwt(token: Dict[str, str], auth_context: ExpertAuthContext = Depends(get_expert_auth_context)):
        if "admin:full_access" not in auth_context.permissions:
             raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Admin access required to blacklist tokens.")
        
        jwt_token = token.get("token")
        if not jwt_token:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Token not provided.")
        
        jwt_handler = ExpertJWTHandler(TEST_JWT_SECRET, TEST_REDIS_CLIENT)
        await jwt_handler.blacklist_token(jwt_token, reason="manual_blacklist_by_admin")
        return {"message": "Token blacklisted successfully."}


    # To run this example:
    # 1. Save the code as a Python file (e.g., `auth_app.py`).
    # 2. Install dependencies: `pip install fastapi uvicorn python-jose[cryptography] redis`
    # 3. Run from your terminal: `uvicorn auth_app:app --reload`
    # 4. Access: http://127.0.0.1:8000/docs for Swagger UI to test endpoints.
    #    - Get tokens from /api/v1/auth/login, then use them in other endpoints.

    # Example of how to use MockRedisClient if a real Redis server isn't available:
    # In `startup_event`, modify:
    # TEST_REDIS_CLIENT = MockRedisClient() # This will force the mock client
    # This mock has limited functionality compared to a real Redis.
    
    # You can uncomment this to run the FastAPI app directly for testing purposes.
    # if __name__ == "__main__":
    #     uvicorn.run(app, host="0.0.0.0", port=8000)
