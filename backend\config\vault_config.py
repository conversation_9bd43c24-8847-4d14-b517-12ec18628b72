import os
from pathlib import Path
from pydantic import BaseModel, Field, ValidationError, Extra
from typing import Optional, Dict, List, Any, Literal, Union
from src.schemas.api_models import AegisDefenseConfig, AmbrosiaGateConfig
import logging
from urllib.parse import quote_plus
import yaml

#!/usr/bin/env python3
# DIGITAL FINGERPRINT: UUID=1a2b3c4d-5e6f-7a8b-9c0d-1e2f3a4b5c6d | DATE=2025-06-26
"""
(C) 2025 Hyper Medusa Neural Vault, All Rights Reserved.
PROPRIETARY & CONFIDENTIAL — UNAUTHORIZED USE, COPYING, OR DISTRIBUTION IS STRICTLY PROHIBITED.

This file contains proprietary algorithms, business logic, and intellectual property of Hyper Medusa Neural Vault.
Use, reproduction, or disclosure by any means without explicit written permission is a violation of applicable law.
"""

"""
HYPER MEDUSA NEURAL VAULT - Comprehensive Configuration System Business Value Documentation
=========================================================================================

vault_config.py
---------------
Advanced configuration management with Pydantic validation, environment-specific settings, and integrated security configurations for the complete neural vault system.

Business Value:
- Ensures robust, secure, and flexible configuration management for all environments.
- Accelerates onboarding, deployment, and scaling of the Medusa Vault platform.
- Supports compliance, security, and operational excellence.
- Enables seamless integration between YAML-based and environment variable-based config systems.

For further details, see module-level docstrings and architecture documentation.
"""

"""
HYPER MEDUSA NEURAL VAULT - Comprehensive Configuration System
============================================================

Advanced configuration management with Pydantic validation, environment-specific settings,
and integrated security configurations for the complete neural vault system.

INTEGRATION FEATURES:
====================

This module provides seamless integration between two configuration systems:

1. **VaultConfig System** (YAML-based with Pydantic validation)
   - Supports development.yaml, production.yaml, testing.yaml
   - Pydantic validation and type safety
   - Comprehensive configuration structure

2. **ProductionConfig System** (Environment variable-based)
   - Production-grade configuration management
   - Environment variable overrides
   - Enterprise security features

USAGE EXAMPLES:
===============

Basic Configuration Loading:
```python

# Load configuration (automatically detects environment)
config = get_config()
```

Unified Configuration (Production-Ready):
```python

# Gets production config in production, vault config otherwise
config = get_production_config_compatible()
```

Dictionary-Based Access (Universal Compatibility):
```python
    get_database_config_dict,
    get_redis_config_dict,
    get_security_config_dict
)

# Get configuration as dictionaries for universal compatibility
db_config = get_database_config_dict()
redis_config = get_redis_config_dict()
security_config = get_security_config_dict()
```

Environment-Specific Loading:
```python

loader = ConfigLoader()
dev_config = loader.load_config(env="development")
prod_config = loader.load_config(env="production")
```

CONFIGURATION FILES:
===================

- `config/development.yaml` - Development environment settings
- `config/production.yaml` - Production environment settings  
- `config/testing.yaml` - Testing environment settings
- `.env.production` - Production environment variables

ENVIRONMENT VARIABLES:
=====================

Set `APP_ENV` to control which configuration is loaded:
- `APP_ENV=development` - Loads development.yaml
- `APP_ENV=production` - Loads production.yaml + .env.production
- `APP_ENV=testing` - Loads testing.yaml

INTEGRATION STATUS: ✅ FULLY INTEGRATED
======================================

This configuration system is fully integrated with:
✅ Production config system (backend.config.production_config)
✅ Database infrastructure (backend.infrastructure.database)
✅ Redis caching (backend.infrastructure.realtime)
✅ Security systems (src.schemas.api_models)
✅ Environment variable loading (.env files)
✅ YAML configuration files (config/*.yaml)
✅ Backward compatibility with existing code
✅ Type safety and validation
✅ Development and production modes
"""


# Import security configurations

logger = logging.getLogger(__name__)

# Integration with production config system for backward compatibility
try:
    from backend.config.production_config import (
        ProductionConfig as ProdConfig,
        get_production_config,
        ProductionConfigManager
    )
    PRODUCTION_CONFIG_AVAILABLE = True
except ImportError:
    logger.warning("Production config system not available, using vault config only")
    PRODUCTION_CONFIG_AVAILABLE = False

class MonitoringConfig(BaseModel):
    """Configuration for application monitoring and metrics."""
    enabled: bool = Field(True, description="Enable or disable monitoring.")
    prometheus_port: Optional[int] = Field(None, description="Port for Prometheus metrics exposure.")
    log_level: str = Field("INFO", description="Logging level for monitoring components.")
    health_check_interval_seconds: int = Field(30, description="Interval for automated health checks.")

class DatabaseConfig(BaseModel):
    """Database specific configurations."""
    provider: str = Field("sqlite", description="Database provider (e.g., 'sqlite', 'postgresql').")
    name: str = Field("medusa_vault", description="Name of the database file or schema.")
    host: Optional[str] = Field(None, description="Database host for network databases.")
    port: Optional[int] = Field(None, description="Database port for network databases.")
    user: Optional[str] = Field(None, description="Database user for network databases.")
    password: Optional[str] = Field(None, description="Database password for network databases.")
    pool_size: int = Field(10, description="Maximum number of connections in the database pool.")
    timeout: int = Field(30, description="Connection timeout in seconds.")
    migrations_path: Path = Field(Path("./migrations"), description="Path to database migration scripts.")
    # Additional properties needed by the database infrastructure
    connection_string: Optional[str] = Field(None, description="Full database connection string.")
    max_connections: int = Field(10, description="Alias for pool_size for compatibility.")
    connection_timeout: int = Field(30, description="Alias for timeout for compatibility.")
    echo: bool = Field(False, description="Enable SQLAlchemy query echo for debugging.")
    
    def __init__(self, **data):
        """Ensure compatibility aliases are set."""
        super().__init__(**data)
        # Sync compatibility fields
        if 'pool_size' in data and 'max_connections' not in data:
            self.max_connections = self.pool_size
        if 'timeout' in data and 'connection_timeout' not in data:
            self.connection_timeout = self.timeout
    
    def get_connection_url(self, is_async: bool = True) -> str:
        """
        Generate database connection URL for compatibility with production config.
        
        Args:
            is_async: Whether to use async driver
            
        Returns:
            Database connection URL string
        """
        # Use direct connection string if provided
        if self.connection_string:
            return self.connection_string
        
        # Handle SQLite
        if self.provider.lower() == "sqlite":
            return f"sqlite:///{self.name}.db"
        
        # Handle PostgreSQL
        if self.provider.lower() in ["postgresql", "postgres"]:
            
            driver = "postgresql+asyncpg" if is_async else "postgresql+psycopg2"
            if self.password:
                password_encoded = quote_plus(self.password)
                url = f"{driver}://{self.user}:{password_encoded}@{self.host}:{self.port}/{self.name}"
            else:
                url = f"{driver}://{self.user}@{self.host}:{self.port}/{self.name}"
            return url
        
        # Fallback for other providers
        logger.warning(f"Unknown database provider: {self.provider}")
        return f"{self.provider}://{self.user}@{self.host}:{self.port}/{self.name}"

class RedisConfig(BaseModel):
    """Redis cache and real-time messaging configurations."""
    host: str = Field("localhost", description="Redis server host.")
    port: int = Field(6379, description="Redis server port.")
    db: int = Field(0, description="Redis database number.")
    password: Optional[str] = Field(None, description="Redis server password.")
    enabled: bool = Field(True, description="Enable or disable Redis caching and real-time features.")
    timeout: int = Field(5, description="Connection timeout in seconds for Redis.")

class ModelServiceConfig(BaseModel):
    """Configuration for AI/ML model services."""
    nba_model_path: Path = Field(Path("./models/nba/ensemble_v1.pkl"), description="Path to NBA ensemble model.")
    wnba_model_path: Path = Field(Path("./models/wnba/ensemble_v1.pkl"), description="Path to WNBA ensemble model.")
    retraining_schedule_cron: str = Field("0 0 * * SUN", description="Cron string for model retraining schedule.")
    drift_detection_enabled: bool = Field(True, description="Enable or disable model drift detection.")
    shap_explainability_enabled: bool = Field(True, description="Enable SHAP explainability for models.")

class PredictionServiceConfig(BaseModel):
    """Configuration for prediction related services."""
    live_update_interval_seconds: int = Field(60, description="Interval for live prediction updates.")
    confidence_threshold: float = Field(0.75, description="Minimum confidence for high-value predictions.")

class OracleEngineConfig(BaseModel):
    """Configuration for the core Oracle Engine."""
    max_inference_batch_size: int = Field(32, description="Maximum batch size for model inference.")
    data_retention_days: int = Field(365, description="Number of days to retain historical data.")
    vigil_cycle: int = Field(60, description="Eternal vigil monitoring cycle in seconds.")
    feature_engineering_pipelines: List[str] = Field(
        default_factory=lambda: ["player_stats", "team_stats", "matchup_history"],
        description="List of feature engineering pipelines to use."
    )

class NbaApiConfig(BaseModel):
    """Configuration for NBA API integration."""
    rate_limit_per_hour: int = Field(500, description="Rate limit for NBA API calls per hour.")
    timeout_seconds: int = Field(15, description="Timeout for NBA API requests in seconds.")
    cache_ttl_seconds: int = Field(60, description="Cache time-to-live for NBA API responses in seconds.")

class VaultConfig(BaseModel):
    """
    Comprehensive configuration for the HYPER MEDUSA NEURAL VAULT.
    This class loads from a YAML file and holds all nested configurations.
    """
    environment: str = Field("development", description="Application environment (development, testing, production).")
    log_level: str = Field("INFO", description="Overall application logging level.")
    api_prefix: str = Field("/api/v1", description="Base API prefix for all routes.") # Nested configurations - ensure these are correctly passed/initialized
    database: DatabaseConfig = Field(default_factory=DatabaseConfig, description="Database configuration.")
    redis: RedisConfig = Field(default_factory=RedisConfig, description="Redis configuration.")
    monitoring: MonitoringConfig = Field(default_factory=MonitoringConfig, description="Monitoring and metrics configuration.")
    security: AegisDefenseConfig = Field(default_factory=AegisDefenseConfig, description="Security configuration (Aegis Defense Matrix).")
    authentication: AmbrosiaGateConfig = Field(default_factory=AmbrosiaGateConfig, description="Authentication and Authorization configuration (Ambrosia Gatekeeper).")
    model_service: ModelServiceConfig = Field(default_factory=ModelServiceConfig, description="AI/ML Model Services configuration.")
    prediction_service: PredictionServiceConfig = Field(default_factory=PredictionServiceConfig, description="Prediction Services configuration.")
    oracle_engine: OracleEngineConfig = Field(default_factory=OracleEngineConfig, description="Oracle Engine configuration.")
    nba_api: NbaApiConfig = Field(default_factory=NbaApiConfig, description="NBA API integration configuration.")

    class Config:
        """Pydantic configuration settings."""
        extra = Extra.allow  # Allow extra fields for flexibility
    
    def get(self, key: str, default=None):
        """Dictionary-like get method for backward compatibility."""
        try:
            return getattr(self, key, default)
        except AttributeError:
            return default
    
    def __getitem__(self, key):
        """Dictionary-like access for backward compatibility."""
        try:
            return getattr(self, key)
        except AttributeError:
            raise KeyError(f"'{key}' not found in VaultConfig")

    @classmethod
    def from_yaml(cls, file_path: Path):
        """Loads configuration from a YAML file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            return cls(**config_data)
        except FileNotFoundError:
            logger.warning(f"Configuration file not found: {file_path}, using defaults")
            return cls()
        except ValidationError as e:
            logger.error(f"Configuration validation MEDUSA ERROR: {file_path}: {e}")
            raise
        except yaml.YAMLError as e:
            logger.error(f"Error parsing YAML configuration file {file_path}: {e}")
            raise
        except Exception as e:
            logger.error(f"An unexpected error occurred while loading config from {file_path}: {e}")
            raise

    # Environment check methods
    def is_production(self) -> bool:
        """Checks if the current environment is production."""
        return self.environment == "production"

    def is_development(self) -> bool:
        """Checks if the current environment is development."""
        return self.environment == "development"

    def is_testing(self) -> bool:
        """Checks if the current environment is testing."""
        return self.environment == "testing"

class ProductionConfig(VaultConfig):
    """
    Specific configuration for the production environment.
    Inherits from VaultConfig and sets production-specific defaults or overrides.
    """
    environment: Literal["production"] = "production"
    
    # Additional production-specific fields from YAML
    debug: bool = Field(False, description="Debug mode flag for production.")
    workers: int = Field(4, description="Number of worker processes for production.")

    database: DatabaseConfig = Field(
        default_factory=lambda: DatabaseConfig(
            provider="postgresql",
            name="hyper_medusa_prod",
            host=os.getenv("DB_HOST", "prod-db.example.com"),
            port=int(os.getenv("DB_PORT", "5432")),
            user=os.getenv("DB_USER", "prod_user"),
            password=os.getenv("DB_PASSWORD", "prod_pass"),
            pool_size=20,
            timeout=60,
            max_connections=20,
            connection_timeout=60
        )
    )
    redis: RedisConfig = Field(
        default_factory=lambda: RedisConfig(
            host=os.getenv("CHRONOS_CACHE_HOST", "prod-redis.example.com"),
            port=int(os.getenv("CHRONOS_CACHE_PORT", "6379")),
            password=os.getenv("CHRONOS_CACHE_PASSWORD", None),
            enabled=True
        )
    )
    log_level: str = Field("WARNING")
    
    class Config:
        """Pydantic configuration settings for production."""
        extra = Extra.allow  # Allow extra fields for production flexibility

class ConfigLoader:
    """Utility to load configuration based on environment."""
    _instance: Optional["ConfigLoader"] = None
    _config: Optional[VaultConfig] = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def load_config(self, env: Optional[str] = None, config_path: Optional[Path] = None, force_reload: bool = False) -> VaultConfig:
        """Load configuration synchronously for compatibility."""
        if self._config is not None and not force_reload:
            return self._config

        if env is None:
            env = os.getenv("APP_ENV", "development")

        if config_path is None:
            base_config_dir = Path(__file__).resolve().parent.parent.parent / "config"
            config_file = base_config_dir / f"{env}.yaml"
        else:
            config_file = config_path

        logger.info(f"Loading configuration for environment: {env} from {config_file}")

        try:
            if env == "production":
                self._config = ProductionConfig.from_yaml(config_file)
            else: # Default to development or base VaultConfig for testing
                self._config = VaultConfig.from_yaml(config_file)

            logger.info(f" Configuration loaded successfully for environment: {self._config.environment}")
            return self._config
        except Exception as e:
            logger.error(f" TITAN PROCESSING FAILED: load configuration: {e}")
            # Return default config instead of raising for development
            logger.warning(" TITAN WARNING: Using default configuration due to load failure")
            if env == "production":
                self._config = ProductionConfig()
            else:
                self._config = VaultConfig()
            return self._config

    async def async_load_config(self, env: Optional[str] = None, config_path: Optional[Path] = None) -> VaultConfig:
        """Async version of load_config for compatibility."""
        return self.load_config(env, config_path)
    
    def reset_config(self):
        """Reset cached configuration for testing purposes."""
        self._config = None

# Global configuration loader instance
config_loader = ConfigLoader()

def get_config() -> VaultConfig:
 """Get the current vault configuration."""
 return config_loader.load_config()

def get_vault_config() -> VaultConfig:
 """Get the current vault configuration (alias for get_config)."""
 return config_loader.load_config()


class ConfigIntegrationAdapter:
    """
    Adapter class to provide seamless integration between VaultConfig and ProductionConfig systems.
    This ensures backward compatibility while allowing the use of either configuration system.
    """
    
    def __init__(self):
        self._vault_config = None
        self._production_config = None
    
    def get_unified_config(self, prefer_production: bool = True) -> Union[VaultConfig, Any]:
        """
        Get a unified configuration object, preferring production config in production environments.
        
        Args:
            prefer_production: Whether to prefer production config over vault config
            
        Returns:
            The appropriate configuration object
        """
        env = os.getenv("APP_ENV", "development").lower()
        
        if prefer_production and PRODUCTION_CONFIG_AVAILABLE and env == "production":
            try:
                if self._production_config is None:
                    self._production_config = get_production_config()
                return self._production_config
            except Exception as e:
                logger.warning(f"Failed to load production config, falling back to vault config: {e}")
        
        # Default to vault config
        if self._vault_config is None:
            self._vault_config = get_config()
        return self._vault_config
    
    def get_database_config(self) -> Dict[str, Any]:
        """Get database configuration compatible with both systems."""
        config = self.get_unified_config()
        
        if hasattr(config, 'database'):
            db_config = config.database
            # Convert to dict for universal compatibility
            if hasattr(db_config, '__dict__'):
                return db_config.__dict__
            elif hasattr(db_config, 'dict'):
                return db_config.dict()
            else:
                return db_config
        
        # Fallback to default database config
        logger.warning("No database configuration found, using defaults")
        return DatabaseConfig().dict()
    
    def get_redis_config(self) -> Dict[str, Any]:
        """Get Redis configuration compatible with both systems."""
        config = self.get_unified_config()
        
        if hasattr(config, 'redis'):
            redis_config = config.redis
            # Convert to dict for universal compatibility
            if hasattr(redis_config, '__dict__'):
                return redis_config.__dict__
            elif hasattr(redis_config, 'dict'):
                return redis_config.dict()
            else:
                return redis_config
        
        # Fallback to default Redis config
        logger.warning("No Redis configuration found, using defaults")
        return RedisConfig().dict()
    
    def get_security_config(self) -> Dict[str, Any]:
        """Get security configuration compatible with both systems."""
        config = self.get_unified_config()
        
        # Try vault config security structure first
        if hasattr(config, 'security'):
            security_config = config.security
            if hasattr(security_config, '__dict__'):
                return security_config.__dict__
            elif hasattr(security_config, 'dict'):
                return security_config.dict()
            else:
                return security_config
        
        # Fallback to default security config
        logger.warning("No security configuration found, using defaults")
        return AegisDefenseConfig().dict()
    
    def is_production(self) -> bool:
        """Check if running in production environment."""
        config = self.get_unified_config()
        
        if hasattr(config, 'is_production') and callable(config.is_production):
            return config.is_production()
        elif hasattr(config, 'environment'):
            return config.environment.lower() == "production"
        else:
            return os.getenv("APP_ENV", "development").lower() == "production"


# Global integration adapter
config_adapter = ConfigIntegrationAdapter()

# Compatibility functions for existing codebase
def get_production_config_compatible():
    """
    Compatibility function that works with both config systems.
    Returns production config if available, otherwise vault config.
    """
    return config_adapter.get_unified_config(prefer_production=True)

def get_database_config_dict() -> Dict[str, Any]:
    """Get database configuration as a dictionary for universal compatibility."""
    return config_adapter.get_database_config()

def get_redis_config_dict() -> Dict[str, Any]:
    """Get Redis configuration as a dictionary for universal compatibility."""
    return config_adapter.get_redis_config()

def get_security_config_dict() -> Dict[str, Any]:
    """Get security configuration as a dictionary for universal compatibility."""
    return config_adapter.get_security_config()
