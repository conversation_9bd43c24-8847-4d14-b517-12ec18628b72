#!/usr/bin/env python3
"""
Fix Cleanup Issues Script
Addresses structural and import issues caused by the cleanup tool
"""

import os
import sys
import re
from pathlib import Path

def fix_data_processing_utils():
    """Fix the data_processing_utils.py file"""
    file_path = "vault_oracle/core/data_processing_utils.py"
    
    print(f"🔧 Fixing {file_path}...")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Fix the malformed logging statement around line 224
        content = re.sub(
            r'else:\s+f"Skipping category conversion.*?\s+\)',
            'else:\n                    logger.warning(\n                        f"Skipping category conversion for column \'{col}\' (too many unique values)."\n                    )',
            content,
            flags=re.DOTALL
        )
        
        # Fix the incomplete if statement around line 415-418
        content = re.sub(
            r'if clipped_count > 0:\s+# Basketball-specific derived features',
            'if clipped_count > 0:\n                    logger.info(f"Clipped {clipped_count} outliers")\n\n        # Basketball-specific derived features',
            content,
            flags=re.DOTALL
        )
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ Fixed {file_path}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to fix {file_path}: {e}")
        return False

def fix_import_issues():
    """Fix common import issues in key files"""
    
    # Files that might have import issues
    files_to_check = [
        "src/model_forge/FateArchetypeStrategy.py",
        "backend/routers/live.py", 
        "backend/routers/websocket.py"
    ]
    
    for file_path in files_to_check:
        if not os.path.exists(file_path):
            continue
            
        print(f"🔧 Checking imports in {file_path}...")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check if file has obvious syntax issues
            try:
                compile(content, file_path, 'exec')
                print(f"✅ {file_path} syntax is valid")
            except SyntaxError as e:
                print(f"⚠️  {file_path} has syntax error: {e}")
                
        except Exception as e:
            print(f"❌ Failed to check {file_path}: {e}")

def test_key_imports():
    """Test importing key modules"""
    
    test_modules = [
        "src.model_forge.DivineArchetypeStrategy",
        "src.model_forge.FateArchetypeStrategy", 
        "backend.routers.live",
        "backend.routers.websocket"
    ]
    
    print("🧪 Testing key module imports...")
    
    for module_name in test_modules:
        try:
            __import__(module_name)
            print(f"✅ {module_name} imports successfully")
        except Exception as e:
            print(f"❌ {module_name} import failed: {e}")

def main():
    """Main fix function"""
    print("🚀 Starting cleanup issue fixes...")
    
    # Change to the project directory
    os.chdir(Path(__file__).parent)
    
    # Fix data processing utils
    fix_data_processing_utils()
    
    # Check import issues
    fix_import_issues()
    
    # Test imports
    test_key_imports()
    
    print("🎉 Cleanup issue fixes completed!")

if __name__ == "__main__":
    main()
