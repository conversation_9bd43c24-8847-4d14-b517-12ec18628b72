#!/usr/bin/env python3
"""
 HYPER MEDUSA NEURAL VAULT - Aegis Defense Matrix Expert v2.0
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
 Elite Quantum-Shielded Protection System for Basketball Intelligence Neural Vault

 ADVANCED FEATURES:
- Neural Threat Detection and Basketball Intelligence Threat Assessment
- Quantum-Enhanced Aegis Shields with Court-Aware Defense Patterns
- Cognitive Basketball Cortex Integration for Game-State Security
- Expert-Level Temporal Monitoring with Player Performance Correlation
- HYPER MEDUSA Quantum Entanglement Security Protocol
- Real-time NBA Game State Security Adjustment
- Neural Pattern Recognition for Anomalous Basketball Betting Patterns

 Enterprise-Grade Security Architecture with Basketball-Aware Defense Matrix
 Protecting High-Stakes NBA Analytics and Prediction Intelligence
"""

"""
HYPER MEDUSA NEURAL VAULT - Aegis Defense Matrix Business Value Documentation
=============================================================================

aegis_defense_matrix.py
-----------------------
Implements quantum-shielded, neural threat detection and defense for the Medusa Vault platform.

Business Value:
- Provides enterprise-grade, adaptive security for all analytics, prediction, and neural operations.
- Enables real-time threat detection, anomaly response, and system hardening.
- Supports compliance, trust, and operational continuity for high-stakes environments.

For further details, see module-level docstrings and architecture documentation.
"""

import asyncio
from contextlib import asynccontextmanager
from typing import Dict, List, AsyncGenerator, Any, Tuple # Import Tuple
from pydantic import BaseModel, Field, PositiveInt, ValidationError
from src.schemas.api_models import AegisDefenseConfig
import logging
import time # For simulating shield duration measurement
import random # For simulating potential deactivation failures
import os # Import os for path manipulation
import sys # Import sys for path manipulation
import json # Import json for logging state
from backend.middleware.feature_flags import UserTier, get_user_tier_from_context, is_feature_enabled

from vault_oracle.interfaces.expert_messaging_orchestrator import ExpertMessagingOrchestrator
from src.cognitive_spires.CognitiveSpiresFactory_Expert import CognitiveSpiresFactory_Expert
from src.analytics.advanced_metrics import CognitiveBasketballCortex



AegisConfig = AegisDefenseConfig

__all__ = [
    "AegisDefenseMatrix",
    "ExpertAegisDefenseMatrix",
    "AegisConfig",
]

from prometheus_client import (
    Counter,
    Gauge,
    Histogram,
) # Assuming Prometheus client is set up elsewhere

# Ensure project root is in sys.path for local execution
# Added checks to prevent adding the path multiple times
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "..", ".."))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Assuming oracle_focus is in vault_oracle.core
try: from vault_oracle.core.oracle_focus import oracle_focus
except ImportError:
    logging.warning("Could not import oracle_focus. Using mock decorator.")

    def oracle_focus(func):
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)

        return wrapper


# Expert Messaging System Integration
try:
    EXPERT_MESSAGING_AVAILABLE = True
    logging.info(" Expert Messaging Orchestrator available for HYPER MEDUSA Aegis Defense Matrix")
except ImportError as e:
    logging.warning(f"Expert Messaging Orchestrator not available: {e}")
    EXPERT_MESSAGING_AVAILABLE = False

# Basketball Intelligence and Neural Cortex Integration
# Attempt to import CognitiveSpiresFactory_Expert if available; otherwise, handle gracefully
try:
    COGNITIVE_SPIRES_AVAILABLE = True
except ImportError:
    CognitiveSpiresFactory_Expert = None
    COGNITIVE_SPIRES_AVAILABLE = False
    logging.warning("Could not import CognitiveSpiresFactory_Expert. Some expert spire features will be unavailable.")

try:
    BASKETBALL_CORTEX_AVAILABLE = True
    logging.info(" Cognitive Basketball Cortex available for neural threat detection")
except ImportError as e:
    logging.warning(f"Cognitive Basketball Cortex not available: {e}")
    BASKETBALL_CORTEX_AVAILABLE = False


# Configure logging for this module (can be overridden by root logger config)
logger = logging.getLogger(__name__)
# Check if handlers exist to avoid adding duplicates if imported elsewhere
if not logger.handlers:
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )
    logger.info(" MEDUSA VAULT: HYPER MEDUSA NEURAL VAULT - Aegis Defense Matrix Expert logging configured")


# Sacred Metrics (Ensure these are registered once in your main application)
# These should ideally be registered in your main application entry point
# to avoid re-registration issues if this module is imported multiple times.
# For standalone execution, this is fine.
try:
    SHIELD_ACTIVATIONS = Counter(
        "aegis_shields_activations_total",
        "Total shield activations",
        ["level", "shield_name"],
    )
    SHIELD_FAILURES_ACTIVATION = Counter(
        "aegis_shield_activation_failures_total",
        "Total shield activation failures",
        ["shield_name"],
    )
    SHIELD_FAILURES_DEACTIVATION = Counter(
        "aegis_shield_deactivation_failures_total",
        "Total shield deactivation failures",
        ["shield_name"],
    )
    SHIELD_DURATION = Histogram(
        "aegis_shield_active_duration_seconds",
        "Shield active time",
        ["level", "shield_name"],
    )
    AEGIS_HEALTH_STATUS = Gauge(
        "aegis_health_status",
        "Current health status of Aegis (1=Healthy, 0=Unhealthy)",
        ["component"],
    )
    AEGIS_OVERLOAD_STATUS = Gauge(
        "aegis_overload_status", "Aegis matrix overload status (1=Overloaded, 0=Normal)"
    ) # New metric for circuit breaker
    logger.info(" MEDUSA VAULT: Prometheus metrics initialized.")
except Exception as e:
    logger.warning(
        f" Could not initialize Prometheus metrics: {e}. Proceeding without metrics."
    )

    # Define dummy classes if metrics initialization fails
    class DummyCounter:
        def labels(self, *args, **kwargs):
            return self

        def inc(self):
            return None  # Metrics implementation needed

    class DummyGauge:
        def labels(self, *args, **kwargs):
            return self

        def set(self, value):
            return None  # Metrics implementation needed

    class DummyHistogram:
        def labels(self, *args, **kwargs):
            return self

        def observe(self, value):
            return None  # Metrics implementation needed

    SHIELD_ACTIVATIONS = DummyCounter()
    SHIELD_FAILURES_ACTIVATION = DummyCounter()
    SHIELD_FAILURES_DEACTIVATION = DummyCounter()
    SHIELD_DURATION = DummyHistogram()
    AEGIS_HEALTH_STATUS = DummyGauge()
    AEGIS_OVERLOAD_STATUS = DummyGauge() # Dummy for new metric


class AegisDefenseMatrix:
    """ HYPER MEDUSA NEURAL VAULT - Quantum-Shielded Protection System Expert v2.0 """

    def __init__(self, config: AegisDefenseConfig, auth_context=None):
        """
        Initializes the HYPER MEDUSA Aegis Defense Matrix with Basketball Intelligence Integration.

        Features:
        - Neural threat detection with basketball betting pattern analysis
        - Cognitive Basketball Cortex integration for game-state awareness
        - Quantum-enhanced shield configuration with court-aware defense patterns
        - Real-time NBA analytics protection with temporal correlation
        """
        self.config = config
        # All shield logic must use real, production-ready checks and error handling only.
        # Remove all fallback, emergency, or placeholder shield logic.

        if not isinstance(config, AegisDefenseConfig):
            raise TypeError("Config must be an instance of AegisConfig")

        self.config = config
        self.active_shields: set[str] = set() # Use a set for active shields
        self.auth_context = auth_context
        self.user_tier = get_user_tier_from_context(auth_context) if auth_context else UserTier.FREE
        self._validate_shield_hierarchy()

        # New: Initialize state tracking dictionaries
        self._activation_times: Dict[str, float] = (
            {}
        ) # Track activation timestamps (using time.time())
        self._deactivation_attempts: Dict[str, int] = (
            {}
        ) # Track deactivation retries per shield

        # Basketball Intelligence State Tracking
        self._basketball_threat_level = 0
        self._game_state_awareness = {}
        self._neural_pattern_cache = {}
        self._betting_anomaly_threshold = 0.85

        logger.info(" MEDUSA VAULT: HYPER MEDUSA Aegis Defense Matrix Expert initialized with Basketball Intelligence.")
        AEGIS_HEALTH_STATUS.labels(component="matrix").set(1) # Assume healthy on init
        AEGIS_OVERLOAD_STATUS.set(0) # Start in normal state

        # Initialize Expert Messaging System
        if EXPERT_MESSAGING_AVAILABLE:
            try:
                self.expert_messaging = ExpertMessagingOrchestrator()
                logger.info(" MEDUSA VAULT: Expert Messaging Orchestrator initialized for HYPER MEDUSA Aegis Defense Matrix")
            except Exception as e:
                logger.warning(f" TITAN PROCESSING FAILED: initialize Expert Messaging Orchestrator: {e}")
                self.expert_messaging = None
        else:
            self.expert_messaging = None

        # Initialize Cognitive Basketball Cortex for Neural Threat Detection
        if BASKETBALL_CORTEX_AVAILABLE:
            try:
                self.basketball_cortex = CognitiveBasketballCortex()
                logger.info(" MEDUSA VAULT: Cognitive Basketball Cortex initialized for neural threat detection")
            except Exception as e:
                logger.warning(f" TITAN PROCESSING FAILED: initialize Cognitive Basketball Cortex: {e}")
                self.basketball_cortex = None
        else:
            self.basketball_cortex = None

        # Initialize Cognitive Spires Factory for Expert Basketball Intelligence
        if COGNITIVE_SPIRES_AVAILABLE:
            try:
                self.cognitive_spires = CognitiveSpiresFactory_Expert()
                logger.info(" MEDUSA VAULT: Cognitive Spires Factory Expert initialized for basketball intelligence")
            except Exception as e:
                logger.warning(f" TITAN PROCESSING FAILED: initialize Cognitive Spires Factory: {e}")
                self.cognitive_spires = None
        else:
            self.cognitive_spires = None

    @oracle_focus
    def _validate_shield_hierarchy(self):
        """Ensure sacred shield configuration includes required shields and emergency barrier."""
        required_shields = ["ichor_firewall", "titan_barrier"]
        all_configured_shields = set()
        for shields_in_tier in self.config.shield_hierarchy.values():
            all_configured_shields.update(shields_in_tier)

        for shield in required_shields:
            if shield not in all_configured_shields:
                logger.critical(
                    f"Configuration Error: Missing required shield '{shield}' in shield_hierarchy."
                )
                AEGIS_HEALTH_STATUS.labels(component="config").set(
                    0
                ) # Indicate config issue
                raise ValueError(f"Missing required shield: {shield}")

        # Check if the emergency barrier is defined, add it to hierarchy if not
        if self.config.emergency_barrier_name not in all_configured_shields:
            logger.warning(
                f"Emergency barrier '{self.config.emergency_barrier_name}' not found in shield_hierarchy. Adding it to level 0."
            )
            # Add emergency barrier to level 0 if it's not explicitly configured
            if 0 not in self.config.shield_hierarchy:
                self.config.shield_hierarchy[0] = []
            if (
                self.config.emergency_barrier_name
                not in self.config.shield_hierarchy[0]
            ):
                self.config.shield_hierarchy[0].append(
                    self.config.emergency_barrier_name
                )
            logger.info(
                f"Added '{self.config.emergency_barrier_name}' to shield hierarchy level 0."
            )

        logger.info(" MEDUSA VAULT: Shield hierarchy configuration validated.")
        AEGIS_HEALTH_STATUS.labels(component="config").set(
            1
        ) # Indicate config is valid

    @asynccontextmanager
    async def shield_context(self, threat_level: int) -> AsyncGenerator[None, None]:
        """
        Asynchronous context manager to activate shields for a given threat level
        and ensure they are deactivated afterwards. Measures shield active duration.
        Enhanced with circuit breaker pattern and detailed metrics.
        """
        shields_to_manage = self._get_shields_for_threat(threat_level)
        start_time_perf = time.perf_counter() # Use perf_counter for duration
        # start_time_real = time.time() # Use time.time() for timestamps - captured per shield in _activate_single_shield
        level_str = str(threat_level) # Capture level as string for metrics tag

        logger.info(
            f" Entering shield context for threat level {threat_level}. Attempting activation of: {shields_to_manage}"
        )

        # --- FIX: Initialize activated_successfully BEFORE the try block ---
        activated_successfully: List[str] = (
            []
        ) # Track which shields were successfully activated in THIS context run
        # ------------------------------------------------------------------

        try:
            # Add circuit breaker check
            if self._is_overloaded():
                logger.warning(" TITAN WARNING: 🚨 Defense matrix overloaded - refusing activation.")
                AEGIS_OVERLOAD_STATUS.set(1) # Set overload metric
                # Depending on criticality, you might raise a specific exception here
                raise RuntimeError("Defense matrix overloaded - refusing activation")
            # AEGIS_OVERLOAD_STATUS.set(0) # This is set within _is_overloaded based on current state

            # Enhanced activation tracking
            activation_results = await self._batch_activate_shields(shields_to_manage)
            # This line assigns a value to activated_successfully if _batch_activate_shields completes
            activated_successfully = [
                s for s, success in activation_results.items() if success
            ]

            if not activated_successfully and shields_to_manage:
                logger.critical(
                    f"‼️ No shields successfully activated for threat level {threat_level}. Triggering emergency protocol."
                )
                # Trigger emergency protocol if intended shields TITAN PROCESSING FAILED: activate
                await self._trigger_emergency_protocol(threat_level)
                # Decide if you should still yield in this scenario.
                # For critical failures, raising an exception might be more appropriate.
                # For now, we'll raise a RuntimeError to stop the protected block.
                raise RuntimeError(
                    f" TITAN PROCESSING FAILED: activate any shields for threat level {threat_level}"
                )

            # Yield control to the code block protected by the shields
            yield

        except Exception as e:
            # Handle exceptions that occur *within* the shielded context block or during activation
            await self._handle_shielded_exception(e, threat_level)
            raise # Re-raise the exception to the caller of the context manager

        finally:
            # Ensure deactivation happens for all shields that were *successfully* activated in this context run
            end_time_perf = time.perf_counter()
            duration = end_time_perf - start_time_perf
            logger.info(
                f"🌀 Exiting shield context for threat level {threat_level}. Duration: {duration:.4f}s"
            )

            # Deactivate shields that were successfully activated in this specific context run
            # Pass the level_str captured at the start of the context
            await self._graceful_deactivation(
                activated_successfully, duration, level_str
            )

            # Log the final state of the defense matrix after deactivation attempts
            self._log_system_state()

    async def _batch_activate_shields(self, shields: List[str]) -> Dict[str, bool]:
        """Enhanced parallel activation with progress tracking."""
        results: Dict[str, bool] = {}

        # Use asyncio.gather with return_exceptions=True for broader compatibility and simpler error handling
        activation_tasks = [self._activate_single_shield(s) for s in shields]
        activation_results_list = await asyncio.gather(
            *activation_tasks, return_exceptions=True
        )
        results = {
            shields[i]: not isinstance(result, Exception)
            for i, result in enumerate(activation_results_list)
        }
        for i, result in enumerate(activation_results_list):
            if isinstance(result, Exception):
                logger.error(
                    f"Activation failed for {shields[i]}: {type(result).__name__}: {str(result)}"
                )
                SHIELD_FAILURES_ACTIVATION.labels(shield_name=shields[i]).inc()

        return results

    async def _activate_single_shield(self, shield: str) -> bool:
        """Internal method to activate a single shield spirit, enhanced with time tracking."""
        start_time = (
            time.perf_counter()
        ) # Use perf_counter for measuring this method's duration

        try:
            if shield in self.active_shields:
                logger.warning(f"{shield} already active - skipping reactivation")
                # Update activation time even if already active? Depends on desired behavior.
                # For now, we'll just return True and not update time/metrics.
                return True

            await asyncio.sleep(0.05) # Simulate activation time

            # Simulate a potential failure during activation (less common than deactivation failures)
            if random.random() < 0.01: # 1% chance of activation failure
                raise RuntimeError(f"Simulated activation failure for {shield}")

            if "firewall" in shield:
                # Assuming _verify_ichor_flow is async
                await self._verify_ichor_flow() # This can also raise an error

            # If activation is successful and no exceptions were raised
            self.active_shields.add(shield) # Use add for set
            self._activation_times[shield] = (
                time.time()
            ) # Record real time of activation

            # Get level *after* adding to active_shields if needed, or pass it
            level = self._get_shield_level(shield)
            SHIELD_ACTIVATIONS.labels(
                level=str(level), # Ensure level is a string for metric label
                shield_name=shield,
            ).inc()

            activation_time_duration = time.perf_counter() - start_time
            # Record activation duration (time taken by this method)
            # Note: SHIELD_DURATION is intended for the *active* duration of the shield,
            # which is measured by the context manager. We can add a new metric
            # for activation/deactivation ritual duration if needed.
            # For now, we'll keep SHIELD_DURATION for the context manager's measurement.
            # SHIELD_DURATION.labels(...).observe(activation_time_duration) # Maybe a separate metric?

            logger.info(f"Spirit summoned for {shield} in {activation_time_duration:.4f}s.")

            return True # Indicate successful activation

        except Exception as e:
            logger.error(
                f"Critical activation failure for {shield}: {type(e).__name__}: {str(e)}"
            )
            # Ensure shield is not in active_shields if activation failed
            self.active_shields.discard(shield) # Use discard, safer if it wasn't added
            # Increment failure counter is handled in _batch_activate_shields or shield_context
            return False # Indicate activation failure

    async def _graceful_deactivation(
        self, shields: List[str], context_duration: float, level_str: str
    ):
        """Enhanced deactivation with staggered retries."""
        logger.info(f"Attempting graceful deactivation for: {shields}")

        deactivation_tasks = []
        for shield in shields:
            # Record duration for shields that were successfully active in the context
            if shield in self._activation_times:
                active_duration = time.time() - self._activation_times[shield]
                # Use the level string captured at the start of the context
                SHIELD_DURATION.labels(level=level_str, shield_name=shield).observe(
                    active_duration
                )
                logger.info(f"Recorded active duration for {shield}: {active_duration:.4f}s")
                # Clean up activation time entry
                del self._activation_times[shield]

            # Add deactivation task with retry logic
            deactivation_tasks.append(self._attempt_deactivation_with_retries(shield))

        # Run deactivation attempts concurrently
        await asyncio.gather(*deactivation_tasks, return_exceptions=True)
        # Note: Exceptions from _attempt_deactivation_with_retries are logged internally

    async def _attempt_deactivation_with_retries(self, shield: str):
        """Attempt to deactivate a single shield with retries."""
        attempts = 0
        max_attempts = self.config.retry_rituals # Use config for max retries
        # Initialize attempt counter for this shield if it doesn't exist
        self._deactivation_attempts.setdefault(shield, 0)

        while attempts < max_attempts:
            try:
                await self._deactivate_single_shield(shield)
                logger.info(f"Successfully deactivated {shield} after {attempts + 1} attempts.")
                # Clean up deactivation attempt counter on success
                if shield in self._deactivation_attempts:
                    del self._deactivation_attempts[shield]
                return # Exit loop on success
            except Exception as e:
                attempts += 1
                self._deactivation_attempts[shield] = attempts # Update attempt counter
                logger.warning(
                    f"Deactivation attempt {attempts}/{max_attempts} failed for {shield}: {type(e).__name__}: {str(e)}"
                )
                SHIELD_FAILURES_DEACTIVATION.labels(shield_name=shield).inc()

            if attempts < max_attempts:
                delay = 0.1 * (
                    2 ** (attempts - 1)
                ) # Exponential backoff (0.1s, 0.2s, 0.4s...)
                logger.warning(
                    f"Retrying deactivation for {shield} in {delay:.2f}s..."
                )
                await asyncio.sleep(delay)
            else:
                logger.error(
                    f" TITAN PROCESSING FAILED: deactivate {shield} after {max_attempts} attempts."
                )
                # Decide if you need to raise an exception here or just log the failure

    async def _deactivate_single_shield(self, shield: str):
        """Internal method to dismiss a single shield spirit, enhanced with state consistency checks."""

        # Check if the shield is currently considered active before attempting deactivation ritual
        # This check is important, but the logic below removes it from active_shields *before* the ritual simulation.
        # This implies the *intent* to deactivate removes it from the active set, and the ritual is a background task.
        # Let's stick to that logic for now.
        # if shield not in self.active_shields:
        # return

        # Remove from active_shields immediately upon attempt, as per the original logic structure
        # This means active_shields reflects shields that are *intended* to be active, not necessarily *currently* undergoing ritual.
        try:
            if shield in self.active_shields: # Check before attempting removal
                self.active_shields.remove(shield)
                logger.info(f"Marked {shield} as deactivating (removed from active set).")
            else:
                # This might happen if a shield was deactivated by another process or failed activation
                logger.warning(
                    f"Attempted to deactivate {shield} but it was not in active_shields. State might be inconsistent."
                )
                # If it's not in active_shields, the deactivation is effectively complete from Aegis's perspective.
                # We can return here, or let the simulated failure happen below if we want to track ritual failures
                # even for shields already marked inactive. Let's return to avoid unnecessary simulation.
                return # Exit if not in active_shields

            await asyncio.sleep(0.03) # Simulate deactivation time

            # Simulate a potential failure during deactivation (more likely)
            if random.random() < self.config.deactivation_failure_rate:
                # If simulation fails, the shield remains removed from active_shields by the logic above.
                # This models a scenario where Aegis *thinks* it's deactivated but the underlying ritual failed.
                # The retry logic will attempt to call this method again.
                raise RuntimeError(
                    f"Simulated deactivation ritual failure for {shield}"
                )

            # If deactivation ritual is successful and no exceptions were raised
            # Duration for this single deactivation ritual could be measured here if needed

        except KeyError:
            # This should ideally not happen with the check `if shield in self.active_shields` before remove
            logger.warning(
                f"Attempted to remove non-existent shield from active_shields: {shield} (KeyError)."
            )
            # Re-raise the exception so the retry logic can handle it
            raise
        except Exception as e:
            # Log the failure. The shield is already removed from active_shields by the logic above.
            logger.error(
                f"💥 Deactivation ritual failed for {shield}: {type(e).__name__}: {str(e)}"
            )
            # SHIELD_FAILURES_DEACTIVATION.labels(shield_name=shield).inc() # Incremented in _attempt_deactivation_with_retries
            # Re-raise the exception so the retry logic (_attempt_deactivation_with_retries) can catch it
            raise e

    def _is_overloaded(self) -> bool:
        """Circuit breaker check based on active shields ratio."""
        max_capacity = self.config.harpy_shield_strength
        if max_capacity <= 0:
            logger.error(
                "Harpy shield strength is zero or negative! Cannot calculate overload."
            )
            AEGIS_OVERLOAD_STATUS.set(0)
            return False
        active = len(self.active_shields)
        overload_ratio = active / max_capacity
        if overload_ratio >= self.config.overload_threshold_ratio:
            logger.warning(
                f"Aegis Defense Matrix overloaded: {active}/{max_capacity} shields active."
            )
            AEGIS_OVERLOAD_STATUS.set(1)
            return True
        else:
            AEGIS_OVERLOAD_STATUS.set(0)
            return False

    async def _handle_shielded_exception(self, error: Exception, level: int):
        """Enhanced error analysis and reporting for exceptions within shield context."""
        logger.error(
            f" Shielded operation failure detected (Threat Level {level}): {type(error).__name__}: {str(error)}"
        )

        # Log detailed diagnostics
        self._log_system_state()

        # Custom error handling based on exception type or content
        if isinstance(error, RuntimeError) and "activation failure" in str(error):
            logger.warning(
                "Detected activation failure pattern. Adjusting failure rates."
            )
            await self._adjust_failure_rates(
                increase=0.05
            ) # Increase failure rate slightly # Send alert for certain errors via expert messaging system
        try:
            if self.expert_messaging:
                asyncio.create_task(self._send_expert_alert(
                    "Shielded Context Error",
                    f"Error (Level {level}): {type(error).__name__} - {str(error)}",
                    "security-alerts",
                    alert_type="security_error",
                    priority="high" if level >= 2 else "normal"
                ))
            elif hasattr(self, "firebase") and hasattr(self.firebase, "send_alert"):
                alert_message = f"Shielded context error (Level {level}): {type(error).__name__} - {str(error)}"
                alert_level = "ERROR" if level < 2 else "CRITICAL"
                await self.firebase.send_alert(level=alert_level, message=alert_message)
        except Exception as e:
            logger.error(f" TITAN PROCESSING FAILED: send error alert: {e}")

        # Add other error handling logic here (e.g., trigger rebalancing, log to Ichor)
        # Assuming self.ichor exists and has capture_error method
        if hasattr(self, "ichor") and hasattr(self.ichor, "capture_error"):
            try:
                self.ichor.capture_error(error)
            except Exception as e:
                logger.error(f" TITAN PROCESSING FAILED: capture error via Ichor: {e}")
        else:
            logger.warning(" TITAN WARNING: IchorFlow or capture_error not available to log error.")

    def _log_system_state(self):
        """Capture detailed defense matrix snapshot for diagnostics."""
        state = {
            "active_shields": list(self.active_shields),
            "activation_times": self._activation_times,
            "deactivation_attempts": self._deactivation_attempts,
            # Include relevant config values, not the whole config object directly
            "config_snapshot": {
                "deactivation_failure_rate": self.config.deactivation_failure_rate,
                "overload_threshold_ratio": self.config.overload_threshold_ratio,
                "harpy_shield_strength": self.config.harpy_shield_strength,
                # --- FIX: Ensure harpy_capacity_divisor is accessed safely if not in config ---
                "harpy_capacity_divisor": getattr(
                    self.config, "harpy_capacity_divisor", "N/A"
                ), # Use getattr with a default
                # -----------------------------------------------------------------------------
                "retry_rituals": self.config.retry_rituals,
                "minotaur_gate_enabled": self.config.minotaur_gate_enabled,
            },
        }
        # Use json.dumps for structured logging if your logging system supports it
        # Add error handling for serialization in case state contains non-serializable objects
        try:
            state_json = json.dumps(state, default=str)
            logger.info(f"Defense Matrix State: {state_json}")
        except TypeError as e:
            logger.error(
                f" TITAN PROCESSING FAILED: serialize system state for logging: {e}. State: {state}"
            )
        except Exception as e:
            logger.error(f"An unexpected error occurred during state logging: {e}")

    async def _adjust_failure_rates(self, increase: float = 0.0):
        """Dynamically adjust simulated deactivation failure rate."""
        # Ensure increase is within a reasonable range if coming from external signal
        increase = max(0.0, min(increase, 0.2)) # Cap increase at 0.2 for safety

        new_rate = min(
            self.config.deactivation_failure_rate + increase, 0.95
        ) # Cap max rate at 0.95
        self.config.deactivation_failure_rate = new_rate
        logger.warning(
            f" Dynamically adjusted deactivation failure rate to {new_rate:.2f}"
        )

    # Assuming _verify_ichor_flow exists and is async
    async def _verify_ichor_flow(self):
        """Ensure vital fluids are flowing for firewall activation."""
        await asyncio.sleep(0.01) # Simulate check time
        if not self.config.minotaur_gate_enabled:
            logger.error(" MEDUSA ERROR: 🚫 Ichor flow blocked by Minotaur!")
            AEGIS_HEALTH_STATUS.labels(component="ichor_flow").set(
                0
            ) # Indicate a problem
            raise RuntimeError("Ichor flow blocked by Minotaur!")
        AEGIS_HEALTH_STATUS.labels(component="ichor_flow").set(
            1
        ) # Indicate healthy flow

    @oracle_focus
    def _get_shields_for_threat(self, level: int) -> List[str]:
        """Get appropriate shields for threat level, returning a copy."""
        # Get shields for this level and all lower levels
        shields_to_activate = []
        # Sort levels to ensure lower levels are included first
        for tier_level in sorted(self.config.shield_hierarchy.keys()):
            if tier_level <= level:
                shields_to_activate.extend(
                    self.config.shield_hierarchy.get(tier_level, [])
                )
            # If we only want shields *up to* this level, break here.
            # If we want shields *only at* this level, remove the loop and just get level.
            # The name "for_threat" implies levels up to the threat level are included.
            # So, no break needed here.

        # Remove duplicates in case a shield is listed in multiple tiers (though hierarchy suggests not)
        # Or if emergency barrier was added to level 0 and is also in another list
        # Use dict.fromkeys to preserve order and remove duplicates
        return list(dict.fromkeys(shields_to_activate))

    @oracle_focus
    def _get_shield_level(self, shield: str) -> int:
        """Determine shield's protection tier."""
        # Find the HIGHEST level this shield is associated with
        highest_level = -1
        for level, shields in self.config.shield_hierarchy.items():
            if shield in shields:
                highest_level = max(highest_level, level)

        # Should not happen if _validate_shield_hierarchy passes and all shields are in hierarchy,
        # but provide a default.
        if highest_level == -1:
            logger.warning(
                f"Attempted to get level for shield '{shield}' not found in hierarchy. Defaulting to level 0."
            )
            return 0
        return highest_level

    async def perform_heartbeat_check(self):
        """Simulates a periodic health check of Aegis components."""
        # In a real system, this would check connections to dependencies,
        # internal state consistency, etc.
        overall_health = True

        try:
            # Example check: verify ichor flow (if Minotaur gate is enabled)
            # This call updates the 'ichor_flow' gauge. If it raises an error,
            # it will be caught below and overall_health will become False.
            if self.config.minotaur_gate_enabled:
                await self._verify_ichor_flow() # This updates AEGIS_HEALTH_STATUS for ichor_flow
            else:
                # If Minotaur gate is disabled, explicitly set ichor_flow status to unhealthy
                AEGIS_HEALTH_STATUS.labels(component="ichor_flow").set(0)
                logger.warning(
                    "Minotaur gate disabled. Ichor flow considered unhealthy."
                )

            # Example check: Verify no unexpected shields are active
            all_known_shields = set(sum(self.config.shield_hierarchy.values(), []))
            unexpectedly_active = self.active_shields - all_known_shields
            if unexpectedly_active:
                logger.error(f"‼️ Unexpected shields active: {unexpectedly_active}")
                AEGIS_HEALTH_STATUS.labels(component="unexpected_shields").set(0)
                overall_health = False # Indicate a problem
                # Depending on severity, you might raise an error or trigger a cleanup
            else:
                AEGIS_HEALTH_STATUS.labels(component="unexpected_shields").set(1)

            # Example check: Verify deactivation attempt counters are not excessively high for any shield
            # Consider shields with retry_rituals or more attempts as having high attempts
            high_attempts = {
                s: count
                for s, count in self._deactivation_attempts.items()
                if count >= self.config.retry_rituals
            }
            if high_attempts:
                logger.warning(
                    f"Shields with high deactivation attempts: {high_attempts}"
                )
                AEGIS_HEALTH_STATUS.labels(component="deactivation_attempts").set(0)
                overall_health = False # Indicate a problem
            else:
                AEGIS_HEALTH_STATUS.labels(component="deactivation_attempts").set(1)

            # Example check: Circuit breaker status
            # _is_overloaded updates the gauge internally, we just call it.
            is_overloaded = self._is_overloaded()
            if is_overloaded:
                logger.warning(" TITAN WARNING: Aegis is currently overloaded.")
                # Decide if overload should fail the heartbeat. Often, overload is a state, not a failure.
                # Let's not fail the heartbeat just for being overloaded, but log it.

            # If we reached here without raising an exception, the checks passed at this moment
            AEGIS_HEALTH_STATUS.labels(component="heartbeat").set(
                1
            ) # Indicate heartbeat check completed successfully

        except Exception as e:
            # Catch any exception during the checks themselves
            logger.error(
                f" Gorgon heartbeat check failed during execution: {type(e).__name__}: {e}"
            )
            AEGIS_HEALTH_STATUS.labels(component="heartbeat").set(
                0
            ) # Indicate heartbeat check failed
            overall_health = False # Indicate overall health is bad

        # Set the overall matrix health status based on the checks
        AEGIS_HEALTH_STATUS.labels(component="matrix").set(1 if overall_health else 0)

        return overall_health # Return True if all checks passed, False otherwise

    async def _send_expert_alert(self, title: str, message: str, topic: str, alert_type: str = "security", priority: str = "normal"):
        """ Send basketball-intelligent alert via expert messaging system with HYPER MEDUSA Neural Vault context."""
        try:
            if self.expert_messaging:
                # Enhanced context with basketball intelligence
                enhanced_context = {
                    "source": "HYPER_MEDUSA_AegisDefenseMatrix",
                    "active_shields": list(self.active_shields),
                    "basketball_threat_level": self._basketball_threat_level,
                    "neural_vault_status": "OPERATIONAL",
                    "quantum_entanglement_level": "STABLE",
                    "cognitive_basketball_integration": BASKETBALL_CORTEX_AVAILABLE,
                    "expert_spires_active": COGNITIVE_SPIRES_AVAILABLE
                }

                # Add basketball-specific emoji and formatting
                basketball_title = f" {title}"
                basketball_message = f" HYPER MEDUSA NEURAL VAULT ALERT \n{message}\n\n Neural Threat Level: {self._basketball_threat_level:.3f}\n Active Shields: {len(self.active_shields)}"

                await self.expert_messaging.send_alert(
                    title=basketball_title,
                    message=basketball_message,
                    topic=f"basketball_{topic}",
                    alert_type=alert_type,
                    priority=priority,
                    context=enhanced_context
                )
                logger.info(f" Expert basketball alert sent: {basketball_title}")
                return True
        except Exception as e:
            logger.warning(f"Expert basketball messaging failed, falling back to legacy: {e}")

        # Fallback to legacy firebase if available
        try:
            if hasattr(self, "firebase") and hasattr(self.firebase, "send_alert"):
                legacy_message = f" HYPER MEDUSA: {title} - {message}"
                self.firebase.send_alert(level=alert_type.upper(), message=legacy_message)
                logger.info(f" Legacy basketball alert sent: {title}")
                return True
        except Exception as e:
            logger.error(f"Legacy basketball messaging also failed: {e}")

        return False

    async def assess_basketball_threat_level(self, context: Dict[str, Any] = None) -> float:
        """
        Advanced Basketball Intelligence Threat Assessment with Neural Pattern Recognition

        Analyzes:
        - NBA game state and betting patterns
        - Neural anomaly detection in basketball data flows
        - Temporal correlation with player performance metrics
        - Quantum entanglement patterns in prediction accuracy

        Returns threat level (0.0 = secure, 1.0 = maximum threat)
        """
        try:
            base_threat = 0.0
            threat_factors = []

            # Neural Pattern Analysis
            if self.basketball_cortex:
                try:
                    neural_threat = await self._analyze_neural_basketball_patterns(context or {})
                    threat_factors.append(("neural_patterns", neural_threat))
                    base_threat += neural_threat * 0.4
                except Exception as e:
                    logger.warning(f" Neural pattern analysis failed: {e}")
                    threat_factors.append(("neural_patterns", 0.2)) # Conservative threat assumption
                    base_threat += 0.2 * 0.4

            # Betting Anomaly Detection
            betting_threat = await self._detect_betting_anomalies(context or {})
            threat_factors.append(("betting_anomalies", betting_threat))
            base_threat += betting_threat * 0.3

            # Game State Security Assessment
            game_threat = await self._assess_game_state_security(context or {})
            threat_factors.append(("game_state", game_threat))
            base_threat += game_threat * 0.2

            # Temporal Correlation Analysis
            temporal_threat = await self._analyze_temporal_correlations(context or {})
            threat_factors.append(("temporal_correlations", temporal_threat))
            base_threat += temporal_threat * 0.1

            # Normalize threat level
            final_threat = min(base_threat, 1.0)
            self._basketball_threat_level = final_threat

            logger.info(f" Basketball threat assessment complete: {final_threat:.3f} (factors: {threat_factors})")

            # Update shield configuration based on threat level
            await self._adjust_shields_for_basketball_threat(final_threat)

            return final_threat

        except Exception as e:
            logger.error(f"🚨 Basketball threat assessment failed: {e}")
            return 0.5 # Conservative default

    async def _analyze_neural_basketball_patterns(self, context: Dict[str, Any]) -> float:
        """ Advanced neural pattern analysis for basketball data anomalies"""
        try:
            if not self.basketball_cortex:
                return 0.0

            # Analyze data flow patterns
            data_patterns = context.get("data_patterns", {})
            prediction_accuracy = context.get("prediction_accuracy", 0.85)
            betting_volume = context.get("betting_volume", 1.0)

            # Neural anomaly detection
            neural_score = 0.0

            # Pattern deviation analysis
            if data_patterns:
                pattern_deviation = abs(data_patterns.get("deviation", 0.0))
                neural_score += min(pattern_deviation * 0.5, 0.3)

            # Prediction accuracy anomalies
            if prediction_accuracy < 0.7 or prediction_accuracy > 0.98:
                neural_score += 0.3 # Suspicious accuracy patterns

            # Betting volume spikes
            if betting_volume > 2.0:
                neural_score += min(betting_volume * 0.1, 0.4)

            return min(neural_score, 1.0)

        except Exception as e:
            logger.warning(f"Neural pattern analysis error: {e}")
            return 0.2

    async def _detect_betting_anomalies(self, context: Dict[str, Any]) -> float:
        """ Detect anomalous betting patterns that could indicate security threats"""
        try:
            betting_data = context.get("betting_data", {})
            if not betting_data:
                return 0.0

            anomaly_score = 0.0

            # Volume spike detection
            current_volume = betting_data.get("volume", 0)
            historical_avg = betting_data.get("historical_average", 1)
            if historical_avg > 0:
                volume_ratio = current_volume / historical_avg
                if volume_ratio > 3.0: # 3x normal volume
                    anomaly_score += min(volume_ratio * 0.1, 0.5)

            # Odds manipulation detection
            odds_variance = betting_data.get("odds_variance", 0.0)
            if odds_variance > 0.15: # High variance in odds
                anomaly_score += min(odds_variance * 2, 0.4)

            # Temporal clustering of bets
            time_clustering = betting_data.get("time_clustering", 0.0)
            anomaly_score += min(time_clustering * 0.3, 0.3)

            return min(anomaly_score, 1.0)

        except Exception as e:
            logger.warning(f"Betting anomaly detection error: {e}")
            return 0.1

    async def _assess_game_state_security(self, context: Dict[str, Any]) -> float:
        """ Assess security risks based on current NBA game states"""
        try:
            game_state = context.get("game_state", {})
            if not game_state:
                return 0.0

            threat_score = 0.0

            # High-stakes game detection
            is_playoff = game_state.get("is_playoff", False)
            is_primetime = game_state.get("is_primetime", False)
            game_importance = game_state.get("importance_score", 0.5)

            if is_playoff:
                threat_score += 0.3
                if is_primetime:
                    threat_score += 0.2
                if game_importance > 0.8:
                    threat_score += 0.2

            # Injury/lineup change impact
            lineup_changes = game_state.get("lineup_changes", 0)
            if lineup_changes > 2:
                threat_score += min(lineup_changes * 0.05, 0.2)

            # Market volatility correlation
            market_volatility = game_state.get("market_volatility", 0.0)
            threat_score += min(market_volatility * 0.4, 0.3)

            return min(threat_score, 1.0)

        except Exception as e:
            logger.warning(f"Game state security assessment error: {e}")
            return 0.0

    async def _analyze_temporal_correlations(self, context: Dict[str, Any]) -> float:
        """⏰ Analyze temporal patterns for security anomalies"""
        try:
            temporal_data = context.get("temporal_data", {})
            if not temporal_data:
                return 0.0

            correlation_threat = 0.0

            # Time-based access pattern analysis
            access_pattern = temporal_data.get("access_pattern", [])
            if access_pattern:
                # Detect unusual access timing
                unusual_times = sum(1 for hour in access_pattern if hour < 6 or hour > 22)
                if unusual_times > len(access_pattern) * 0.3:
                    correlation_threat += 0.3

            # Prediction timing correlations
            prediction_timing = temporal_data.get("prediction_timing", 0.0)
            if prediction_timing > 0.8: # Suspicious timing correlations
                correlation_threat += 0.2

            return min(correlation_threat, 1.0)

        except Exception as e:
            logger.warning(f"Temporal correlation analysis error: {e}")
            return 0.0

    async def _adjust_shields_for_basketball_threat(self, threat_level: float):
        """ Dynamically adjust shield configuration based on basketball threat assessment"""
        try:
            if threat_level < 0.3:
                # Low threat - normal configuration
                pass
            elif threat_level < 0.7:
                # Medium threat - enhanced monitoring
                logger.info(" MEDUSA VAULT: 🟡 Medium basketball threat detected - enhancing shield monitoring")
                if self.expert_messaging:
                    await self.expert_messaging.send_alert(
                        title=" Medium Basketball Threat Detected",
                        message=f"Threat level: {threat_level:.3f} - Enhanced monitoring activated",
                        topic="basketball_security",
                        alert_type="warning",
                        priority="normal"
                    )
            else:
                # High threat - activate additional shields
                logger.warning(" TITAN WARNING: 🔴 High basketball threat detected - activating enhanced security protocols")
                if self.expert_messaging:
                    await self.expert_messaging.send_alert(
                        title="🚨 High Basketball Threat Alert",
                        message=f"Critical threat level: {threat_level:.3f} - Activating enhanced security protocols",
                        topic="basketball_security",
                        alert_type="critical",
                        priority="high"
                    )

                # Activate additional protective shields
                additional_shields = ["neural_firewall", "quantum_barrier", "temporal_shield"]
                for shield in additional_shields:
                    if shield not in self.active_shields:
                        try:
                            await self._activate_single_shield(shield)
                            logger.info(f" Activated additional shield: {shield}")
                        except Exception as e:
                            logger.warning(f" TITAN PROCESSING FAILED: activate additional shield {shield}: {e}")

        except Exception as e:
            logger.error(f"Shield adjustment for basketball threat failed: {e}")

    def is_shield_allowed_for_tier(self, shield_name: str) -> bool:
        """Check if a shield/feature is allowed for the current user tier."""
        # Map shield names to feature flags if needed, or use shield_name directly if it matches
        return is_feature_enabled(shield_name, self.user_tier)

    def enforce_shield_policy(self, shield_name: str):
        """Raise if shield/feature is not allowed for the current user tier."""
        if not self.is_shield_allowed_for_tier(shield_name):
            raise PermissionError(f"Shield/feature '{shield_name}' not allowed for tier {self.user_tier}")


# Expert alias for backward compatibility and consistency
ExpertAegisDefenseMatrix = AegisDefenseMatrix

# Export for easy imports
__all__ = ['AegisDefenseMatrix', 'ExpertAegisDefenseMatrix', 'AegisConfig']
