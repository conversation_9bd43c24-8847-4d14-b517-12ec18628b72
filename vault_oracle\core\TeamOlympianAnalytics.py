from datetime import datetime, timed<PERSON><PERSON>
from typing import Any, Dict, Optional, List, Union
import logging
import asyncio
import numpy as np
from contextlib import asynccontextmanager


# DIGITAL FINGERPRINT: UUID=0b0b1b0b-9b0b-4b0b-8b0b-0b0b0b0b0b0b | DATE=2025-06-26
"""
Expert TeamOlympianAnalytics Implementation
The single source of truth for team analysis across the HYPER MEDUSA NEURAL VAULT Oracle system with expert-level
basketball intelligence, quantum-aware analytics, and comprehensive error handling.
"""
from vault_oracle.shared.elemental_utils import (
    calculate_all_elemental_decays,
    get_default_elemental_profile,
    ELEMENTAL_MAP,
    ELEMENTAL_HALF_LIFE
)

logger = logging.getLogger(__name__)

# Expert Messaging Integration
class ExpertMessenger:
    """Expert-level messaging system for critical analytics alerts"""
    
    @staticmethod
    async def send_critical_alert(message: str, context: Dict = None, alert_type: str = "TEAM_ANALYTICS"):
        """Send critical alert with basketball context"""
        context = context or {}
        alert_data = {
            "timestamp": datetime.now().isoformat(),
            "alert_type": alert_type,
            "message": message,
            "basketball_context": context,
            "severity": "CRITICAL"
        }
        logger.critical(f"🚨 EXPERT ALERT [{alert_type}]: {message} | Context: {context}")
    
    @staticmethod
    async def send_warning_alert(message: str, context: Dict = None, alert_type: str = "TEAM_ANALYTICS"):
        """Send warning alert with basketball context"""
        context = context or {}
        alert_data = {
            "timestamp": datetime.now().isoformat(),
            "alert_type": alert_type,
            "message": message,
            "basketball_context": context,
            "severity": "WARNING"
        }
        logger.warning(f" EXPERT WARNING [{alert_type}]: {message} | Context: {context}")
    
    @staticmethod
    async def send_info_alert(message: str, context: Dict = None, alert_type: str = "TEAM_ANALYTICS"):
        """Send informational alert with basketball context"""
        context = context or {}
        alert_data = {
            "timestamp": datetime.now().isoformat(),
            "alert_type": alert_type,
            "message": message,
            "basketball_context": context,
            "severity": "INFO"
        }
        logger.info(f"ℹ️ EXPERT INFO [{alert_type}]: {message} | Context: {context}")

# Quantum Analytics Integration
class QuantumTeamAnalyzer:
    """Quantum-aware team analytics with basketball intelligence"""
    
    @staticmethod
    def analyze_quantum_team_coherence(team_data: Dict) -> Dict:
        """Analyze team coherence using quantum-inspired basketball metrics"""
        try:
            # Quantum coherence based on basketball performance consistency
            performance_variance = team_data.get('performance_variance', 0.5)
            chemistry_stability = team_data.get('chemistry_stability', 0.7)
            
            # Quantum coherence score (0-1, higher is better)
            coherence_score = max(0.0, min(1.0, (2.0 - performance_variance) * chemistry_stability))
            
            # Quantum entanglement between offensive and defensive systems
            offensive_rating = team_data.get('offensive_rating', 110.0)
            defensive_rating = team_data.get('defensive_rating', 110.0)
            entanglement_coefficient = 1.0 - abs(offensive_rating - defensive_rating) / 50.0
            
            return {
                "quantum_coherence_score": coherence_score,
                "entanglement_coefficient": max(0.0, min(1.0, entanglement_coefficient)),
                "quantum_stability": coherence_score * entanglement_coefficient,
                "basketball_context": {
                    "coherence_interpretation": "high" if coherence_score > 0.75 else "moderate" if coherence_score > 0.5 else "low",
                    "entanglement_strength": "strong" if entanglement_coefficient > 0.8 else "moderate" if entanglement_coefficient > 0.6 else "weak"
                }
            }
        except Exception as e:
            logger.error(f"Quantum team coherence analysis failed: {e}")
            return {
                "quantum_coherence_score": 0.5,
                "entanglement_coefficient": 0.5,
                "quantum_stability": 0.25,
                "error": str(e)
            }

# Expert Basketball Intelligence Integration
class BasketballIntelligenceEngine:
    """Advanced basketball analytics with contextual intelligence"""
    
    @staticmethod
    def analyze_team_basketball_profile(team_data: Dict) -> Dict:
        """Comprehensive basketball intelligence analysis"""
        try:
            # Advanced basketball metrics analysis
            pace = team_data.get('pace', 100.0)
            offensive_rating = team_data.get('offensive_rating', 110.0)
            defensive_rating = team_data.get('defensive_rating', 110.0)
            net_rating = offensive_rating - defensive_rating
            
            # Basketball style classification
            style_indicators = {
                "pace_style": "fast" if pace > 102 else "moderate" if pace > 98 else "slow",
                "offensive_style": "elite" if offensive_rating > 115 else "good" if offensive_rating > 110 else "average",
                "defensive_style": "elite" if defensive_rating < 105 else "good" if defensive_rating < 110 else "average",
                "net_efficiency": "dominant" if net_rating > 8 else "strong" if net_rating > 3 else "competitive" if net_rating > -3 else "struggling"
            }
            
            # Advanced basketball insights
            basketball_insights = {
                "primary_strength": BasketballIntelligenceEngine._identify_primary_strength(team_data),
                "critical_weakness": BasketballIntelligenceEngine._identify_critical_weakness(team_data),
                "matchup_advantages": BasketballIntelligenceEngine._analyze_matchup_advantages(team_data),
                "clutch_performance": BasketballIntelligenceEngine._analyze_clutch_factors(team_data)
            }
            
            return {
                "basketball_profile": style_indicators,
                "advanced_insights": basketball_insights,
                "performance_metrics": {
                    "pace": pace,
                    "offensive_rating": offensive_rating,
                    "defensive_rating": defensive_rating,
                    "net_rating": net_rating
                },
                "basketball_intelligence_score": BasketballIntelligenceEngine._calculate_intelligence_score(style_indicators, basketball_insights)
            }
        except Exception as e:
            logger.error(f"Basketball intelligence analysis failed: {e}")
            return {
                "basketball_profile": {"error": "analysis_failed"},
                "advanced_insights": {"error": str(e)},
                "basketball_intelligence_score": 0.5
            }
    
    @staticmethod
    def _identify_primary_strength(team_data: Dict) -> str:
        """Identify team's primary basketball strength"""
        strengths = []
        
        if team_data.get('offensive_rating', 0) > 115:
            strengths.append("elite_offense")
        if team_data.get('defensive_rating', 120) < 105:
            strengths.append("elite_defense")
        if team_data.get('pace', 0) > 105:
            strengths.append("fast_pace")
        if team_data.get('rebounding_rate', 0) > 0.55:
            strengths.append("rebounding_dominance")
        
        return strengths[0] if strengths else "balanced_approach"
    
    @staticmethod
    def _identify_critical_weakness(team_data: Dict) -> str:
        """Identify team's most critical weakness"""
        weaknesses = []
        
        if team_data.get('turnover_rate', 0) > 0.16:
            weaknesses.append("ball_security")
        if team_data.get('defensive_rating', 0) > 115:
            weaknesses.append("defensive_consistency")
        if team_data.get('three_point_percentage', 0) < 0.32:
            weaknesses.append("perimeter_shooting")
        if team_data.get('free_throw_rate', 0) < 0.20:
            weaknesses.append("drawing_fouls")
        
        return weaknesses[0] if weaknesses else "minor_inconsistencies"
    
    @staticmethod
    def _analyze_matchup_advantages(team_data: Dict) -> List[str]:
        """Analyze potential matchup advantages"""
        advantages = []
        
        if team_data.get('pace', 0) > 105:
            advantages.append("transition_offense")
        if team_data.get('defensive_rating', 120) < 108:
            advantages.append("defensive_pressure")
        if team_data.get('three_point_rate', 0) > 0.40:
            advantages.append("perimeter_volume")
        if team_data.get('offensive_rebounding_rate', 0) > 0.30:
            advantages.append("second_chance_points")
        
        return advantages
    
    @staticmethod
    def _analyze_clutch_factors(team_data: Dict) -> Dict:
        """Analyze clutch performance factors"""
        return {
            "clutch_offense": team_data.get('clutch_offensive_rating', 110.0),
            "clutch_defense": team_data.get('clutch_defensive_rating', 110.0),
            "fourth_quarter_performance": team_data.get('fourth_quarter_net_rating', 0.0),
            "close_game_record": team_data.get('close_game_win_percentage', 0.5)
        }
    
    @staticmethod
    def _calculate_intelligence_score(style_indicators: Dict, basketball_insights: Dict) -> float:
        """Calculate overall basketball intelligence score"""
        # Weighted scoring based on basketball performance categories
        style_weights = {
            "elite": 1.0, "good": 0.8, "fast": 0.9, "dominant": 1.0,
            "strong": 0.85, "competitive": 0.7, "moderate": 0.6,
            "average": 0.5, "slow": 0.4, "struggling": 0.3
        }
        
        total_score = 0.0
        total_weight = 0.0
        
        for indicator, value in style_indicators.items():
            weight = style_weights.get(value, 0.5)
            total_score += weight
            total_weight += 1.0
        
        return min(1.0, max(0.0, total_score / total_weight if total_weight > 0 else 0.5))

# Mock MnemosyneArchiveKeeper with enhanced capabilities
class ExpertMnemosyneArchiveKeeper:
    """Expert-level data connector with enhanced basketball intelligence and error handling"""
    
    def __init__(self, config=None, quantum_entangler=None, temporal_stabilizer=None):
        self.config = config or {}
        self.quantum_entangler = quantum_entangler
        self.temporal_stabilizer = temporal_stabilizer
        self.expert_messenger = ExpertMessenger()

    async def _get_team_abbr_from_id(self, mythic_roster_id: str) -> Optional[str]:
        """Get team abbreviation with expert error handling"""
        try:
            if not mythic_roster_id:
                await self.expert_messenger.send_warning_alert(
                    "Empty team ID provided for abbreviation lookup",
                    {"mythic_roster_id": mythic_roster_id},
                    "TEAM_ID_VALIDATION"
                )
                return None
            
            # Enhanced team ID processing with basketball context
            abbr = mythic_roster_id[-3:].upper() if len(mythic_roster_id) >= 3 else mythic_roster_id.upper()
            
            # Validate against known NBA team abbreviations
            valid_abbrs = {
                'ATL', 'BOS', 'BRK', 'CHA', 'CHI', 'CLE', 'DAL', 'DEN', 'DET', 'GSW',
                'HOU', 'IND', 'LAC', 'LAL', 'MEM', 'MIA', 'MIL', 'MIN', 'NOP', 'NYK',
                'OKC', 'ORL', 'PHI', 'PHX', 'POR', 'SAC', 'SAS', 'TOR', 'UTA', 'WAS'
            }
            
            if abbr not in valid_abbrs:
                await self.expert_messenger.send_warning_alert(
                    f"Unknown team abbreviation derived: {abbr}",
                    {"original_team_id": mythic_roster_id, "derived_abbr": abbr, "valid_abbrs": list(valid_abbrs)},
                    "TEAM_VALIDATION"
                )
                return abbr # Return anyway for fallback processing
            
            await self.expert_messenger.send_info_alert(
                f"Successfully validated team abbreviation: {abbr}",
                {"mythic_roster_id": mythic_roster_id, "abbreviation": abbr},
                "TEAM_VALIDATION"
            )
            return abbr
        
        except Exception as e:
            await self.expert_messenger.send_critical_alert(
                f"Critical MEDUSA ERROR: team abbreviation lookup: {str(e)}",
                {"mythic_roster_id": mythic_roster_id, "error": str(e)},
                "TEAM_ID_ERROR"
            )
            return None

    async def get_team_playing_rotation(
        self, team_abbr: str, season="2024-25", min_minutes_threshold=200
    ) -> List[Dict]:
        """Get team rotation with enhanced basketball intelligence"""
        try:
            if not team_abbr:
                await self.expert_messenger.send_warning_alert(
                    "Empty team abbreviation for rotation lookup",
                    {"team_abbr": team_abbr, "season": season},
                    "ROTATION_LOOKUP"
                )
                return []
            
            # Enhanced mock rotation with basketball context
            rotation_players = [
                {
                    "hero_id": f"mock_player_{team_abbr}_1",
                    "minutes_per_game": 35.2,
                    "position": "PG",
                    "role": "primary_ball_handler",
                    "usage_rate": 0.28
                },
                {
                    "hero_id": f"mock_player_{team_abbr}_2", 
                    "minutes_per_game": 32.8,
                    "position": "SG",
                    "role": "secondary_scorer",
                    "usage_rate": 0.22
                },
                {
                    "hero_id": f"mock_player_{team_abbr}_3",
                    "minutes_per_game": 31.5,
                    "position": "SF", 
                    "role": "versatile_wing",
                    "usage_rate": 0.20
                },
                {
                    "hero_id": f"mock_player_{team_abbr}_4",
                    "minutes_per_game": 28.7,
                    "position": "PF",
                    "role": "frontcourt_anchor",
                    "usage_rate": 0.18
                },
                {
                    "hero_id": f"mock_player_{team_abbr}_5",
                    "minutes_per_game": 26.3,
                    "position": "C",
                    "role": "rim_protector",
                    "usage_rate": 0.16
                }
            ]
            
            await self.expert_messenger.send_info_alert(
                f"Retrieved rotation for {team_abbr} with {len(rotation_players)} players",
                {
                    "team_abbr": team_abbr,
                    "season": season,
                    "rotation_size": len(rotation_players),
                    "min_minutes_threshold": min_minutes_threshold
                },
                "ROTATION_SUCCESS"
            )
            
            return rotation_players
        
        except Exception as e:
            await self.expert_messenger.send_critical_alert(
                f"Critical error retrieving team rotation: {str(e)}",
                {"team_abbr": team_abbr, "season": season, "error": str(e)},
                "ROTATION_ERROR"
            )
            return []

    async def query_player_affinities(self, team_abbr: str) -> List[Dict]:
        """Query player affinities with enhanced basketball intelligence"""
        try:
            if not team_abbr:
                await self.expert_messenger.send_warning_alert(
                    "Empty team abbreviation for affinity lookup",
                    {"team_abbr": team_abbr},
                    "AFFINITY_LOOKUP"
                )
                return []
            
            # Enhanced mock affinities with basketball context
            player_affinities = [
                {
                    "hero_id": f"mock_player_{team_abbr}_1",
                    "fire": 0.35, # Offensive aggression
                    "water": 0.20, # Defensive flow
                    "earth": 0.25, # Rebounding/physicality
                    "air": 0.20, # Court vision/passing
                    "basketball_context": {
                        "primary_affinity": "fire",
                        "playstyle": "aggressive_scorer",
                        "strengths": ["driving", "three_point_shooting"],
                        "role_fit": "primary_option"
                    }
                },
                {
                    "hero_id": f"mock_player_{team_abbr}_2",
                    "fire": 0.25,
                    "water": 0.30, # Strong defensive presence
                    "earth": 0.20,
                    "air": 0.25,
                    "basketball_context": {
                        "primary_affinity": "water",
                        "playstyle": "two_way_player",
                        "strengths": ["perimeter_defense", "spot_shooting"],
                        "role_fit": "secondary_option"
                    }
                },
                {
                    "hero_id": f"mock_player_{team_abbr}_3",
                    "fire": 0.20,
                    "water": 0.25,
                    "earth": 0.30, # Strong rebounding/interior presence
                    "air": 0.25,
                    "basketball_context": {
                        "primary_affinity": "earth",
                        "playstyle": "versatile_forward",
                        "strengths": ["rebounding", "post_moves", "screening"],
                        "role_fit": "complementary_piece"
                    }
                },
                {
                    "hero_id": f"mock_player_{team_abbr}_4",
                    "fire": 0.15,
                    "water": 0.25,
                    "earth": 0.25,
                    "air": 0.35, # Excellent court vision
                    "basketball_context": {
                        "primary_affinity": "air",
                        "playstyle": "facilitator",
                        "strengths": ["passing", "basketball_iq", "leadership"],
                        "role_fit": "floor_general"
                    }
                },
                {
                    "hero_id": f"mock_player_{team_abbr}_5",
                    "fire": 0.30,
                    "water": 0.20,
                    "earth": 0.35, # Dominant interior presence
                    "air": 0.15,
                    "basketball_context": {
                        "primary_affinity": "earth",
                        "playstyle": "interior_anchor",
                        "strengths": ["rim_protection", "post_scoring", "rebounding"],
                        "role_fit": "defensive_anchor"
                    }
                }
            ]
            
            await self.expert_messenger.send_info_alert(
                f"Retrieved player affinities for {team_abbr} with {len(player_affinities)} players",
                {
                    "team_abbr": team_abbr,
                    "players_analyzed": len(player_affinities),
                    "affinity_categories": ["fire", "water", "earth", "air"]
                },
                "AFFINITY_SUCCESS"
            )
            
            return player_affinities
        
        except Exception as e:
            await self.expert_messenger.send_critical_alert(
                f"Critical error querying player affinities: {str(e)}",
                {"team_abbr": team_abbr, "error": str(e)},
                "AFFINITY_ERROR"
            )
            return []

    async def get_last_game_date(self, mythic_roster_id: str) -> Optional[datetime]:
        """Get last game date with expert error handling"""
        try:
            if not mythic_roster_id:
                await self.expert_messenger.send_warning_alert(
                    "Empty team ID for last game date lookup",
                    {"mythic_roster_id": mythic_roster_id},
                    "GAME_DATE_LOOKUP"
                )
                return None
            
            # Enhanced mock implementation with basketball context
            last_game = datetime.now() - timedelta(days=2, hours=8, minutes=30)
            
            await self.expert_messenger.send_info_alert(
                f"Retrieved last game date for team {mythic_roster_id}",
                {
                    "mythic_roster_id": mythic_roster_id,
                    "last_game_date": last_game.isoformat(),
                    "days_since_last_game": (datetime.now() - last_game).days
                },
                "GAME_DATE_SUCCESS"
            )
            
            return last_game
        
        except Exception as e:
            await self.expert_messenger.send_critical_alert(
                f"Critical error retrieving last game date: {str(e)}",
                {"mythic_roster_id": mythic_roster_id, "error": str(e)},
                "GAME_DATE_ERROR"
            )
            return None

    async def get_team_performance_metrics(self, team_abbr: str) -> Dict:
        """Get comprehensive team performance metrics with basketball intelligence"""
        try:
            if not team_abbr:
                await self.expert_messenger.send_warning_alert(
                    "Empty team abbreviation for performance metrics",
                    {"team_abbr": team_abbr},
                    "PERFORMANCE_LOOKUP"
                )
                return {}
            
            # Enhanced performance metrics with basketball context
            performance_metrics = {
                "offensive_rating": 112.5,
                "defensive_rating": 108.2,
                "net_rating": 4.3,
                "pace": 101.8,
                "true_shooting_percentage": 0.567,
                "effective_field_goal_percentage": 0.542,
                "turnover_rate": 0.142,
                "offensive_rebounding_rate": 0.285,
                "defensive_rebounding_rate": 0.785,
                "free_throw_rate": 0.235,
                "three_point_rate": 0.412,
                "assist_rate": 0.645,
                "steal_rate": 0.082,
                "block_rate": 0.055,
                "performance_variance": 0.12,
                "chemistry_stability": 0.78,
                "clutch_offensive_rating": 108.9,
                "clutch_defensive_rating": 106.1,
                "fourth_quarter_net_rating": 2.8,
                "close_game_win_percentage": 0.63,
                "basketball_context": {
                    "team_identity": "balanced_two_way_team",
                    "primary_strength": "defensive_consistency", 
                    "secondary_strength": "offensive_efficiency",
                    "main_weakness": "rebounding_consistency",
                    "playoff_readiness": "high"
                }
            }
            
            await self.expert_messenger.send_info_alert(
                f"Retrieved comprehensive performance metrics for {team_abbr}",
                {
                    "team_abbr": team_abbr,
                    "metrics_categories": len(performance_metrics),
                    "net_rating": performance_metrics["net_rating"],
                    "team_identity": performance_metrics["basketball_context"]["team_identity"]
                },
                "PERFORMANCE_SUCCESS"
            )
            
            return performance_metrics
        
        except Exception as e:
            await self.expert_messenger.send_critical_alert(
                f"Critical error retrieving performance metrics: {str(e)}",
                {"team_abbr": team_abbr, "error": str(e)},
                "PERFORMANCE_ERROR"
            )
            return {}

class MnemosyneArchiveKeeper(ExpertMnemosyneArchiveKeeper):
    """Backward compatibility alias"""
    pass


# Expert oracle_focus decorator with enhanced capabilities
def oracle_focus(performance_tracking=True, quantum_coherence=True, basketball_intelligence=True):
    """Expert-level oracle focus decorator with comprehensive analytics tracking"""
    def decorator(func):
        async def async_wrapper(*args, **kwargs):
            start_time = datetime.now()
            function_name = func.__name__
            
            try:
                if performance_tracking:
                    logger.info(f" EXPERT ORACLE FOCUS: Starting {function_name} with basketball intelligence")
                
                # Execute the function
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                if performance_tracking:
                    execution_time = (datetime.now() - start_time).total_seconds()
                    await ExpertMessenger.send_info_alert(
                        f"Oracle focus completed successfully: {function_name}",
                        {
                            "function": function_name,
                            "execution_time_seconds": execution_time,
                            "quantum_coherence": quantum_coherence,
                            "basketball_intelligence": basketball_intelligence
                        },
                        "ORACLE_PERFORMANCE"
                    )
                
                return result
            
            except Exception as e:
                execution_time = (datetime.now() - start_time).total_seconds()
                await ExpertMessenger.send_critical_alert(
                    f"Oracle focus failed in {function_name}: {str(e)}",
                    {
                        "function": function_name,
                        "execution_time_seconds": execution_time,
                        "error": str(e),
                        "args": str(args)[:200], # Truncate for logging
                        "kwargs": str(kwargs)[:200]
                    },
                    "ORACLE_ERROR"
                )
                raise
        
        def sync_wrapper(*args, **kwargs):
            start_time = datetime.now()
            function_name = func.__name__
            
            try:
                if performance_tracking:
                    logger.info(f" EXPERT ORACLE FOCUS: Starting {function_name} with basketball intelligence")
                
                result = func(*args, **kwargs)
                
                if performance_tracking:
                    execution_time = (datetime.now() - start_time).total_seconds()
                    # For sync functions, we'll log directly instead of using async messenger
                    logger.info(f"Oracle focus completed: {function_name} in {execution_time:.3f}s")
                
                return result
            
            except Exception as e:
                execution_time = (datetime.now() - start_time).total_seconds()
                logger.error(f"Oracle focus failed in {function_name}: {str(e)} (execution_time: {execution_time:.3f}s)")
                raise
        
        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


class ExpertTeamOlympianAnalytics:
    """
    Expert-level Team Olympian Analytics with comprehensive basketball intelligence,
    quantum-aware analytics, and robust error handling.
    
    This is the authoritative implementation for team-level analysis across the HYPER MEDUSA NEURAL VAULT Oracle system,
    providing deep basketball insights, elemental analysis, and advanced performance metrics.
    """
    
    ELEMENTAL_MAP = ELEMENTAL_MAP
    ELEMENTAL_HALF_LIFE = ELEMENTAL_HALF_LIFE

    def __init__(
        self, 
        db_connector: Any = None, 
        config: Optional[Dict[str, Any]] = None,
        enable_quantum_analytics: bool = True,
        enable_basketball_intelligence: bool = True
    ):
        """
        Initialize Expert Team Olympian Analytics with advanced capabilities.
        
        Args:
        db_connector: Database connector for team/player data
        config: Configuration dictionary for customization
        enable_quantum_analytics: Enable quantum-aware analytics
        enable_basketball_intelligence: Enable advanced basketball intelligence
        """
        self.synergy_cache = {}
        self.config = config or {}
        self.enable_quantum_analytics = enable_quantum_analytics
        self.enable_basketball_intelligence = enable_basketball_intelligence
        
        # Initialize expert components
        self.db_connector = db_connector if db_connector else ExpertMnemosyneArchiveKeeper(config=config)
        self.expert_messenger = ExpertMessenger()
        self.quantum_analyzer = QuantumTeamAnalyzer() if enable_quantum_analytics else None
        self.basketball_engine = BasketballIntelligenceEngine() if enable_basketball_intelligence else None
        
        logger.info(f" Expert Team Olympian Analytics initialized with quantum={enable_quantum_analytics}, basketball_ai={enable_basketball_intelligence}")

    @oracle_focus(performance_tracking=True, quantum_coherence=True, basketball_intelligence=True)
    async def get_team_essence(self, mythic_roster_id: str) -> Dict:
        """
        Get comprehensive team essence with expert-level analysis.
        
        This method provides the most complete team analysis available, incorporating
        elemental profiles, war council reports, quantum analytics, and basketball intelligence.
        
        Args:
        mythic_roster_id: Unique team identifier
        
        Returns:
        Dict containing complete team essence with expert insights
        """
        try:
            if not mythic_roster_id:
                await self.expert_messenger.send_critical_alert(
                    "Empty team ID provided for team essence analysis",
                    {"mythic_roster_id": mythic_roster_id},
                    "TEAM_ESSENCE_ERROR"
                )
                return self._get_fallback_essence("invalid_team_id")
            
            # Get core team analysis components
            elemental_profile = await self.elemental_analysis(mythic_roster_id)
            war_council_report = await self.generate_war_report(mythic_roster_id)
            synergy_nexus = await self.calculate_olympian_bonds(mythic_roster_id)
            
            # Enhanced expert analysis
            expert_insights = {}
            
            # Add quantum analytics if enabled
            if self.enable_quantum_analytics and self.quantum_analyzer:
                team_performance_data = await self.db_connector.get_team_performance_metrics(
                    await self.db_connector._get_team_abbr_from_id(mythic_roster_id)
                )
                quantum_analysis = self.quantum_analyzer.analyze_quantum_team_coherence(team_performance_data)
                expert_insights["quantum_analytics"] = quantum_analysis
            
            # Add basketball intelligence if enabled
            if self.enable_basketball_intelligence and self.basketball_engine:
                team_performance_data = await self.db_connector.get_team_performance_metrics(
                    await self.db_connector._get_team_abbr_from_id(mythic_roster_id)
                )
                basketball_profile = self.basketball_engine.analyze_team_basketball_profile(team_performance_data)
                expert_insights["basketball_intelligence"] = basketball_profile
            
            # Comprehensive team essence
            team_essence = {
                "elemental_profile": elemental_profile,
                "war_council_report": war_council_report,
                "synergy_nexus": synergy_nexus,
                "expert_insights": expert_insights,
                "analysis_metadata": {
                    "analysis_timestamp": datetime.now().isoformat(),
                    "analyzer_version": "expert_v1.0",
                    "quantum_enabled": self.enable_quantum_analytics,
                    "basketball_ai_enabled": self.enable_basketball_intelligence,
                    "mythic_roster_id": mythic_roster_id
                }
            }
            
            await self.expert_messenger.send_info_alert(
                f"Successfully generated comprehensive team essence for {mythic_roster_id}",
                {
                    "mythic_roster_id": mythic_roster_id,
                    "analysis_components": list(team_essence.keys()),
                    "expert_insights_included": len(expert_insights),
                    "primary_element": elemental_profile.get("primary_element", "unknown")
                },
                "TEAM_ESSENCE_SUCCESS"
            )
            
            return team_essence
        
        except Exception as e:
            await self.expert_messenger.send_critical_alert(
                f"Critical MEDUSA ERROR: team essence analysis: {str(e)}",
                {"mythic_roster_id": mythic_roster_id, "error": str(e)},
                "TEAM_ESSENCE_CRITICAL_ERROR"
            )
            return self._get_fallback_essence("analysis_error", str(e))
    
    @oracle_focus(performance_tracking=True, quantum_coherence=True, basketball_intelligence=True)
    async def elemental_analysis(self, mythic_roster_id: str) -> Dict:
        """
        Quantify elemental playstyle composition with decay rates and expert basketball intelligence.
        
        This method provides comprehensive elemental analysis incorporating basketball context,
        quantum-aware decay calculations, and advanced performance insights.
        
        Args:
        mythic_roster_id: Unique team identifier
        
        Returns:
        Dict containing detailed elemental analysis with basketball context
        """
        try:
            if not mythic_roster_id:
                await self.expert_messenger.send_critical_alert(
                    "Empty team ID provided for elemental analysis",
                    {"mythic_roster_id": mythic_roster_id},
                    "ELEMENTAL_ANALYSIS_ERROR"
                )
                return get_default_elemental_profile()
            
            team_abbr = await self.db_connector._get_team_abbr_from_id(mythic_roster_id)
            if not team_abbr:
                await self.expert_messenger.send_warning_alert(
                    f"Could not get abbreviation for team ID {mythic_roster_id}. Using balanced default.",
                    {"mythic_roster_id": mythic_roster_id},
                    "TEAM_ABBR_LOOKUP_FAILED"
                )
                return get_default_elemental_profile()

            # Get rotation players and their affinities with enhanced error handling
            try:
                rotation_players = await self.db_connector.get_team_playing_rotation(
                    team_abbr, season="2024-25", min_minutes_threshold=200
                )
                rotation_players = rotation_players[:10] # Limit to top 10 rotation players

                if not rotation_players:
                    await self.expert_messenger.send_warning_alert(
                        f"No rotation found for team {team_abbr}. Using balanced profile.",
                        {"mythic_roster_id": mythic_roster_id, "team_abbr": team_abbr},
                        "ROTATION_NOT_FOUND"
                    )
                    return get_default_elemental_profile()

            except Exception as e:
                await self.expert_messenger.send_critical_alert(
                    f"Error retrieving rotation for {team_abbr}: {str(e)}",
                    {"mythic_roster_id": mythic_roster_id, "team_abbr": team_abbr, "error": str(e)},
                    "ROTATION_RETRIEVAL_ERROR"
                )
                return get_default_elemental_profile()

            # Calculate team elemental balance from player affinities
            try:
                all_team_player_affinities = await self.db_connector.query_player_affinities(team_abbr)
                
                if not all_team_player_affinities:
                    await self.expert_messenger.send_warning_alert(
                        f"No player affinities found for team {team_abbr}",
                        {"mythic_roster_id": mythic_roster_id, "team_abbr": team_abbr},
                        "AFFINITY_DATA_MISSING"
                    )
                    return get_default_elemental_profile()
                
                # Enhanced elemental aggregation with basketball context
                elemental_totals = {"fire": 0.0, "water": 0.0, "earth": 0.0, "air": 0.0}
                player_count = len(all_team_player_affinities)
                basketball_context = {"player_roles": [], "elemental_leaders": {}}
                
                for player in all_team_player_affinities:
                    # Add elemental values
                    for element in elemental_totals:
                        elemental_totals[element] += player.get(element, 0)
                    
                    # Capture basketball context if available
                    if "basketball_context" in player:
                        basketball_context["player_roles"].append({
                            "hero_id": player.get("hero_id"),
                            "primary_affinity": player["basketball_context"].get("primary_affinity"),
                            "playstyle": player["basketball_context"].get("playstyle"),
                            "role_fit": player["basketball_context"].get("role_fit")
                        })
                
                if player_count == 0:
                    await self.expert_messenger.send_warning_alert(
                        "No players found in affinity data, using default profile",
                        {"mythic_roster_id": mythic_roster_id, "team_abbr": team_abbr},
                        "EMPTY_AFFINITY_DATA"
                    )
                    return get_default_elemental_profile()
                
                # Normalize to percentages
                total_elemental = sum(elemental_totals.values())
                if total_elemental == 0:
                    await self.expert_messenger.send_warning_alert(
                        "Zero total elemental values, using default profile",
                        {"mythic_roster_id": mythic_roster_id, "team_abbr": team_abbr, "elemental_totals": elemental_totals},
                        "ZERO_ELEMENTAL_TOTAL"
                    )
                    return get_default_elemental_profile()
                
                balance = {
                    "Fire": elemental_totals["fire"] / total_elemental,
                    "Water": elemental_totals["water"] / total_elemental,
                    "Earth": elemental_totals["earth"] / total_elemental,
                    "Air": elemental_totals["air"] / total_elemental,
                }
                
                # Identify elemental leaders by position
                for element in ["fire", "water", "earth", "air"]:
                    element_leader = max(
                        all_team_player_affinities,
                        key=lambda p: p.get(element, 0)
                    )
                    basketball_context["elemental_leaders"][element] = {
                        "hero_id": element_leader.get("hero_id"),
                        "affinity_strength": element_leader.get(element, 0),
                        "basketball_role": element_leader.get("basketball_context", {}).get("role_fit", "unknown")
                    }
                
            except Exception as e:
                await self.expert_messenger.send_critical_alert(
                    f"Error processing player affinities for {team_abbr}: {str(e)}",
                    {"mythic_roster_id": mythic_roster_id, "team_abbr": team_abbr, "error": str(e)},
                    "AFFINITY_PROCESSING_ERROR"
                )
                return get_default_elemental_profile()
            
            # Determine primary element with basketball interpretation
            primary_element = max(balance, key=balance.get)
            primary_strength = balance[primary_element]
            
            # Basketball interpretation of primary element
            basketball_interpretation = {
                "Fire": "aggressive_offensive_identity",
                "Water": "defensive_flow_and_adaptability", 
                "Earth": "physical_rebounding_presence",
                "Air": "ball_movement_and_pace"
            }
            
            # Get last game date and calculate quantum-aware decay
            try:
                last_game_date = await self.db_connector.get_last_game_date(mythic_roster_id)
                elemental_decay = calculate_all_elemental_decays(mythic_roster_id, last_game_date)
            except Exception as e:
                await self.expert_messenger.send_warning_alert(
                    f"Error calculating elemental decay: {str(e)}",
                    {"mythic_roster_id": mythic_roster_id, "error": str(e)},
                    "DECAY_CALCULATION_ERROR"
                )
                elemental_decay = {"fire": 1.0, "water": 1.0, "earth": 1.0, "air": 1.0}
            
            # Expert-level elemental analysis result
            elemental_analysis_result = {
                "primary_element": primary_element,
                "elemental_balance": balance,
                "elemental_decay": elemental_decay,
                "elemental_weakness": self._find_elemental_vulnerability(balance),
                "basketball_interpretation": {
                    "team_identity": basketball_interpretation[primary_element],
                    "primary_strength_percentage": primary_strength,
                    "elemental_diversity": 1.0 - max(balance.values()), # Higher = more balanced
                    "dominant_threshold": primary_strength > 0.4,
                    "basketball_context": basketball_context
                },
                "expert_insights": {
                    "elemental_dominance_level": "high" if primary_strength > 0.4 else "moderate" if primary_strength > 0.3 else "balanced",
                    "team_chemistry_indicator": sum(balance.values()) / len(balance), # Should be ~0.25 for balanced
                    "specialization_score": max(balance.values()) - min(balance.values()),
                    "recommended_strategy": self._get_elemental_strategy_recommendation(primary_element, balance)
                }
            }
            
            await self.expert_messenger.send_info_alert(
                f"Completed expert elemental analysis for {mythic_roster_id}",
                {
                    "mythic_roster_id": mythic_roster_id,
                    "team_abbr": team_abbr,
                    "primary_element": primary_element,
                    "primary_strength": primary_strength,
                    "players_analyzed": player_count,
                    "basketball_identity": basketball_interpretation[primary_element]
                },
                "ELEMENTAL_ANALYSIS_SUCCESS"
            )
            
            return elemental_analysis_result
        
        except Exception as e:
            await self.expert_messenger.send_critical_alert(
                f"Critical MEDUSA ERROR: elemental analysis: {str(e)}",
                {"mythic_roster_id": mythic_roster_id, "error": str(e)},
                "ELEMENTAL_ANALYSIS_CRITICAL_ERROR"
            )
            return get_default_elemental_profile()

    def _find_elemental_vulnerability(self, balance: Dict) -> str:
        """
        Find the team's weakest elemental aspect with expert basketball interpretation.
        
        Args:
        balance: Dictionary of elemental balance percentages
        
        Returns:
        String describing the elemental weakness with basketball context
        """
        try:
            if not balance:
                return "None"
            
            weakest_element = min(balance, key=balance.get)
            weakest_percentage = balance[weakest_element]
            
            # Expert threshold analysis
            if weakest_percentage < 0.15:
                basketball_weakness = {
                    "Fire": "lacks_offensive_aggression",
                    "Water": "defensive_inconsistency", 
                    "Earth": "rebounding_deficiency",
                    "Air": "ball_movement_struggles"
                }
                return f"{weakest_element}_deficiency_{basketball_weakness[weakest_element]}"
            elif weakest_percentage < 0.20:
                return f"{weakest_element}_minor_weakness"
            else:
                return "well_balanced_no_major_weakness"
            
        except Exception as e:
            logger.error(f"Error finding elemental vulnerability: {e}")
            return "analysis_error"

    def _get_elemental_strategy_recommendation(self, primary_element: str, balance: Dict) -> str:
        """
        Get strategic recommendations based on elemental analysis.
        
        Args:
        primary_element: Team's dominant element
        balance: Elemental balance distribution
        
        Returns:
        Strategic recommendation string with basketball context
        """
        try:
            primary_strength = balance.get(primary_element, 0)
            
            strategy_recommendations = {
                "Fire": {
                    "high": "leverage_aggressive_pace_push_transition",
                    "moderate": "develop_offensive_consistency_maintain_aggression",
                    "balanced": "integrate_fire_elements_into_balanced_attack"
                },
                "Water": {
                    "high": "emphasize_defensive_versatility_and_flow",
                    "moderate": "strengthen_defensive_schemes_improve_adaptability",
                    "balanced": "build_defensive_identity_around_water_principles"
                },
                "Earth": {
                    "high": "dominate_rebounding_establish_interior_presence",
                    "moderate": "improve_physicality_increase_rebounding_focus",
                    "balanced": "develop_interior_game_around_earth_strength"
                },
                "Air": {
                    "high": "maximize_ball_movement_increase_pace_and_spacing",
                    "moderate": "improve_passing_develop_transition_game",
                    "balanced": "enhance_court_vision_and_team_chemistry"
                }
            }
            
            dominance_level = "high" if primary_strength > 0.4 else "moderate" if primary_strength > 0.3 else "balanced"
            return strategy_recommendations.get(primary_element, {}).get(dominance_level, "develop_balanced_approach")
            
        except Exception as e:
            logger.error(f"Error generating strategy recommendation: {e}")
            return "analysis_error_consult_expert"
    
    @oracle_focus(performance_tracking=True, quantum_coherence=True, basketball_intelligence=True)
    async def generate_war_report(self, mythic_roster_id: str) -> Dict:
        """
        Generate comprehensive war council report with expert basketball intelligence.
        
        This method provides detailed strategic analysis including team strengths,
        weaknesses, tactical recommendations, and matchup assessments.
        
        Args:
        mythic_roster_id: Unique team identifier
        
        Returns:
        Dict containing comprehensive war council report
        """
        try:
            if not mythic_roster_id:
                await self.expert_messenger.send_critical_alert(
                    "Empty team ID provided for war report generation",
                    {"mythic_roster_id": mythic_roster_id},
                    "WAR_REPORT_ERROR"
                )
                return self._get_fallback_war_report("invalid_team_id")
            
            team_abbr = await self.db_connector._get_team_abbr_from_id(mythic_roster_id)
            if not team_abbr:
                await self.expert_messenger.send_warning_alert(
                    f"Could not get abbreviation for team ID {mythic_roster_id}",
                    {"mythic_roster_id": mythic_roster_id},
                    "WAR_REPORT_TEAM_LOOKUP"
                )
                return self._get_fallback_war_report("team_lookup_failed")
            
            # Get comprehensive team performance data
            try:
                performance_metrics = await self.db_connector.get_team_performance_metrics(team_abbr)
                rotation_data = await self.db_connector.get_team_playing_rotation(team_abbr)
                player_affinities = await self.db_connector.query_player_affinities(team_abbr)
                
            except Exception as e:
                await self.expert_messenger.send_critical_alert(
                    f"Error retrieving data for war report: {str(e)}",
                    {"mythic_roster_id": mythic_roster_id, "team_abbr": team_abbr, "error": str(e)},
                    "WAR_REPORT_DATA_ERROR"
                )
                return self._get_fallback_war_report("data_retrieval_error", str(e))
            
            # Generate expert-level war council analysis
            war_report = {
                "strategic_assessment": {
                    "team_identity": performance_metrics.get("basketball_context", {}).get("team_identity", "unknown"),
                    "primary_strength": performance_metrics.get("basketball_context", {}).get("primary_strength", "unknown"),
                    "secondary_strength": performance_metrics.get("basketball_context", {}).get("secondary_strength", "unknown"),
                    "main_weakness": performance_metrics.get("basketball_context", {}).get("main_weakness", "unknown"),
                    "playoff_readiness": performance_metrics.get("basketball_context", {}).get("playoff_readiness", "unknown")
                },
                "tactical_analysis": {
                    "offensive_strategy": self._analyze_offensive_strategy(performance_metrics),
                    "defensive_approach": self._analyze_defensive_approach(performance_metrics),
                    "pace_control": self._analyze_pace_strategy(performance_metrics),
                    "clutch_performance": self._analyze_clutch_capabilities(performance_metrics)
                },
                "roster_evaluation": {
                    "rotation_depth": len(rotation_data),
                    "elemental_distribution": self._analyze_roster_balance(player_affinities),
                    "key_players": self._identify_key_players(rotation_data, player_affinities),
                    "role_balance": self._assess_role_balance(rotation_data)
                },
                "matchup_intelligence": {
                    "favorable_matchups": self._identify_favorable_matchups(performance_metrics),
                    "challenging_matchups": self._identify_challenging_matchups(performance_metrics),
                    "exploitable_advantages": self._find_exploitable_advantages(performance_metrics),
                    "defensive_concerns": self._identify_defensive_concerns(performance_metrics)
                },
                "war_council_recommendations": {
                    "immediate_focus": self._get_immediate_focus_areas(performance_metrics),
                    "strategic_adjustments": self._recommend_strategic_adjustments(performance_metrics),
                    "lineup_optimizations": self._suggest_lineup_optimizations(rotation_data, player_affinities),
                    "opponent_preparation": self._generate_opponent_prep_strategy(performance_metrics)
                },
                "report_metadata": {
                    "analysis_timestamp": datetime.now().isoformat(),
                    "mythic_roster_id": mythic_roster_id,
                    "team_abbreviation": team_abbr,
                    "report_version": "expert_war_council_v1.0",
                    "confidence_level": "high" if performance_metrics else "low"
                }
            }
            
            await self.expert_messenger.send_info_alert(
                f"Generated comprehensive war report for {team_abbr}",
                {
                    "mythic_roster_id": mythic_roster_id,
                    "team_abbr": team_abbr,
                    "report_sections": len(war_report),
                    "team_identity": war_report["strategic_assessment"]["team_identity"],
                    "primary_strength": war_report["strategic_assessment"]["primary_strength"]
                },
                "WAR_REPORT_SUCCESS"
            )
            
            return war_report
        
        except Exception as e:
            await self.expert_messenger.send_critical_alert(
                f"Critical error generating war report: {str(e)}",
                {"mythic_roster_id": mythic_roster_id, "error": str(e)},
                "WAR_REPORT_CRITICAL_ERROR"
            )
            return self._get_fallback_war_report("critical_error", str(e))

    @oracle_focus(performance_tracking=True, quantum_coherence=True, basketball_intelligence=True)
    async def calculate_olympian_bonds(self, mythic_roster_id: str) -> Dict:
        """
        Team chemistry analysis using recent rotations with expert basketball intelligence.
        
        This method provides comprehensive team chemistry analysis incorporating
        synergy scores, assist networks, defensive coordination, and temporal cohesion.
        
        Args:
        mythic_roster_id: Unique team identifier
        
        Returns:
        Dict containing detailed Olympian bonds analysis
        """
        try:
            if not mythic_roster_id:
                await self.expert_messenger.send_critical_alert(
                    "Empty team ID provided for Olympian bonds calculation",
                    {"mythic_roster_id": mythic_roster_id},
                    "OLYMPIAN_BONDS_ERROR"
                )
                return self._get_fallback_bonds("invalid_team_id")
            
            team_abbr = await self.db_connector._get_team_abbr_from_id(mythic_roster_id)
            if not team_abbr:
                await self.expert_messenger.send_warning_alert(
                    f"Could not get abbreviation for team ID {mythic_roster_id}",
                    {"mythic_roster_id": mythic_roster_id},
                    "BONDS_TEAM_LOOKUP"
                )
                return self._get_fallback_bonds("team_lookup_failed")
            
            # Check cache first for performance optimization
            cache_key = f"{mythic_roster_id}_{team_abbr}"
            if cache_key in self.synergy_cache:
                cached_result = self.synergy_cache[cache_key]
                await self.expert_messenger.send_info_alert(
                    f"Retrieved cached Olympian bonds for {team_abbr}",
                    {"mythic_roster_id": mythic_roster_id, "team_abbr": team_abbr, "cache_hit": True},
                    "BONDS_CACHE_HIT"
                )
                return cached_result
            
            try:
                # Get comprehensive team data
                affinities = await self.db_connector.query_player_affinities(team_abbr)
                rotation_data = await self.db_connector.get_team_playing_rotation(team_abbr)
                performance_metrics = await self.db_connector.get_team_performance_metrics(team_abbr)
                
                if not affinities:
                    await self.expert_messenger.send_warning_alert(
                        f"No player affinities found for {team_abbr}",
                        {"mythic_roster_id": mythic_roster_id, "team_abbr": team_abbr},
                        "BONDS_NO_AFFINITIES"
                    )
                    return self._get_fallback_bonds("no_affinities")
                
            except Exception as e:
                await self.expert_messenger.send_critical_alert(
                    f"Error retrieving data for Olympian bonds: {str(e)}",
                    {"mythic_roster_id": mythic_roster_id, "team_abbr": team_abbr, "error": str(e)},
                    "BONDS_DATA_ERROR"
                )
                return self._get_fallback_bonds("data_error", str(e))
            
            # Calculate expert-level bond metrics
            bonds = {
                "synergy_score": await self._calculate_enhanced_synergy_score(affinities, rotation_data),
                "assist_web": await self._calculate_assist_nexus(team_abbr, rotation_data),
                "defensive_harmony": await self._measure_defensive_synergy(team_abbr, affinities, performance_metrics),
                "chronosync": await self._temporal_cohesion_index(mythic_roster_id, performance_metrics),
                "basketball_chemistry": {
                    "offensive_cohesion": self._calculate_offensive_chemistry(affinities, performance_metrics),
                    "defensive_coordination": self._calculate_defensive_coordination(affinities, performance_metrics),
                    "transition_synergy": self._calculate_transition_synergy(affinities, performance_metrics),
                    "clutch_unity": self._calculate_clutch_chemistry(affinities, performance_metrics)
                },
                "elemental_harmony": {
                    "balance_score": self._calculate_elemental_balance_score(affinities),
                    "complementarity": self._assess_elemental_complementarity(affinities),
                    "synergistic_pairs": self._identify_synergistic_player_pairs(affinities),
                    "chemistry_network": self._map_chemistry_network(affinities)
                },
                "expert_insights": {
                    "chemistry_grade": self._grade_team_chemistry(affinities, performance_metrics),
                    "improvement_areas": self._identify_chemistry_improvement_areas(affinities, performance_metrics),
                    "lineup_recommendations": self._recommend_chemistry_optimized_lineups(affinities, rotation_data),
                    "long_term_potential": self._assess_chemistry_potential(affinities, rotation_data)
                },
                "bonds_metadata": {
                    "analysis_timestamp": datetime.now().isoformat(),
                    "mythic_roster_id": mythic_roster_id,
                    "team_abbreviation": team_abbr,
                    "players_analyzed": len(affinities),
                    "analysis_version": "expert_olympian_bonds_v1.0"
                }
            }
            
            # Cache the result for performance
            self.synergy_cache[cache_key] = bonds
            
            await self.expert_messenger.send_info_alert(
                f"Completed expert Olympian bonds analysis for {team_abbr}",
                {
                    "mythic_roster_id": mythic_roster_id,
                    "team_abbr": team_abbr,
                    "synergy_score": bonds["synergy_score"],
                    "players_analyzed": len(affinities),
                    "chemistry_grade": bonds["expert_insights"]["chemistry_grade"]
                },
                "OLYMPIAN_BONDS_SUCCESS"
            )
            
            return bonds
        
        except Exception as e:
            await self.expert_messenger.send_critical_alert(
                f"Critical MEDUSA ERROR: Olympian bonds calculation: {str(e)}",
                {"mythic_roster_id": mythic_roster_id, "error": str(e)},
                "OLYMPIAN_BONDS_CRITICAL_ERROR"
            )
            return self._get_fallback_bonds("critical_error", str(e))

    # Expert helper methods for comprehensive analysis
    async def _calculate_enhanced_synergy_score(self, affinities: List[Dict], rotation_data: List[Dict]) -> float:
        """Calculate enhanced synergy score with basketball intelligence"""
        try:
            if not affinities:
                return 0.0
            
            # Base synergy from elemental affinities
            base_synergy = sum(
                a["fire"] + a["water"] + a["earth"] + a["air"] for a in affinities
            ) / len(affinities) if affinities else 0.0
            
            # Basketball role synergy multiplier
            role_multiplier = 1.0
            if rotation_data:
                roles = [player.get("role", "unknown") for player in rotation_data]
                role_diversity = len(set(roles)) / len(roles) if roles else 0
                role_multiplier = 1.0 + (role_diversity * 0.2) # Bonus for role diversity
            
            # Minutes distribution balance
            minutes_balance = 1.0
            if rotation_data:
                minutes = [player.get("minutes_per_game", 0) for player in rotation_data]
                if minutes and max(minutes) > 0:
                    minutes_variance = np.var(minutes) / np.mean(minutes) if np.mean(minutes) > 0 else 1.0
                    minutes_balance = max(0.5, 1.0 - minutes_variance) # Penalty for unbalanced minutes
            
            enhanced_synergy = base_synergy * role_multiplier * minutes_balance
            return min(1.0, max(0.0, enhanced_synergy))
            
        except Exception as e:
            logger.error(f"Error calculating enhanced synergy score: {e}")
            return 0.5

    async def _calculate_assist_nexus(self, team_abbr: str, rotation_data: List[Dict]) -> Dict:
        """Enhanced assist network calculation with basketball intelligence"""
        try:
            # Mock advanced assist network analysis
            assist_metrics = {
                "primary_facilitator": rotation_data[0].get("hero_id") if rotation_data else "unknown",
                "assist_distribution": {
                    "point_guard": 0.45,
                    "secondary_playmaker": 0.25,
                    "role_players": 0.30
                },
                "ball_movement_rating": 0.78,
                "creation_hierarchy": ["primary", "secondary", "tertiary"],
                "network_connectivity": 0.82,
                "basketball_context": {
                    "passing_style": "ball_movement_offense",
                    "assist_rate": 0.645,
                    "turnover_ratio": 2.1
                }
            }
            
            return assist_metrics
            
        except Exception as e:
            logger.error(f"Error calculating assist nexus: {e}")
            return {"error": str(e), "network_connectivity": 0.5}

    async def _measure_defensive_synergy(self, team_abbr: str, affinities: List[Dict], performance_metrics: Dict) -> Dict:
        """Enhanced defensive coordination calculation with basketball intelligence"""
        try:
            # Extract defensive metrics from performance data
            defensive_rating = performance_metrics.get("defensive_rating", 110.0)
            steal_rate = performance_metrics.get("steal_rate", 0.08)
            block_rate = performance_metrics.get("block_rate", 0.05)
            defensive_rebounding = performance_metrics.get("defensive_rebounding_rate", 0.75)
            
            # Calculate water (defensive) affinity strength
            water_strength = sum(a.get("water", 0) for a in affinities) / len(affinities) if affinities else 0.5
            
            # Comprehensive defensive synergy
            defensive_synergy = {
                "coordination_score": water_strength * 1.2, # Boost water element for defense
                "rim_protection": min(1.0, block_rate * 20), # Normalize block rate
                "perimeter_pressure": min(1.0, steal_rate * 12.5), # Normalize steal rate
                "rebounding_synergy": defensive_rebounding,
                "overall_defensive_rating": max(0.0, (120 - defensive_rating) / 15), # Normalize def rating
                "basketball_context": {
                    "defensive_identity": "switch_heavy" if water_strength > 0.3 else "traditional",
                    "scheme_flexibility": water_strength,
                    "communication_level": "high" if water_strength > 0.25 else "moderate"
                }
            }
            
            return defensive_synergy
            
        except Exception as e:
            logger.error(f"Error measuring defensive synergy: {e}")
            return {"coordination_score": 0.5, "error": str(e)}

    async def _temporal_cohesion_index(self, mythic_roster_id: str, performance_metrics: Dict) -> Dict:
        """Enhanced temporal cohesion calculation with basketball intelligence"""
        try:
            # Get performance consistency metrics
            performance_variance = performance_metrics.get("performance_variance", 0.15)
            chemistry_stability = performance_metrics.get("chemistry_stability", 0.75)
            
            # Calculate temporal cohesion components
            consistency_score = max(0.0, 1.0 - performance_variance * 2) # Lower variance = better
            stability_score = chemistry_stability
            
            # Advanced temporal metrics
            temporal_cohesion = {
                "consistency_index": consistency_score,
                "stability_rating": stability_score,
                "temporal_sync": (consistency_score + stability_score) / 2,
                "momentum_factor": self._calculate_momentum_factor(performance_metrics),
                "adaptation_rate": self._calculate_adaptation_rate(performance_metrics),
                "basketball_context": {
                    "game_flow_management": "excellent" if stability_score > 0.8 else "good" if stability_score > 0.6 else "developing",
                    "clutch_consistency": performance_metrics.get("close_game_win_percentage", 0.5),
                    "rhythm_maintenance": consistency_score
                }
            }
            
            return temporal_cohesion
            
        except Exception as e:
            logger.error(f"Error calculating temporal cohesion: {e}")
            return {"temporal_sync": 0.5, "error": str(e)}

    # Additional expert helper methods for comprehensive basketball intelligence
    def _calculate_offensive_chemistry(self, affinities: List[Dict], performance_metrics: Dict) -> float:
        """Calculate offensive chemistry score with basketball intelligence"""
        try:
            # Fire (offensive) elemental strength
            fire_strength = sum(a.get("fire", 0) for a in affinities) / len(affinities) if affinities else 0.5
            
            # Basketball performance modifiers
            offensive_rating = performance_metrics.get("offensive_rating", 110.0)
            true_shooting = performance_metrics.get("true_shooting_percentage", 0.56)
            assist_rate = performance_metrics.get("assist_rate", 0.60)
            
            # Combine elemental and performance factors
            performance_factor = ((offensive_rating - 100) / 20) * true_shooting * assist_rate
            chemistry_score = (fire_strength * 0.6) + (performance_factor * 0.4)
            
            return min(1.0, max(0.0, chemistry_score))
        except Exception as e:
            logger.error(f"Error calculating offensive chemistry: {e}")
            return 0.5

    def _calculate_defensive_coordination(self, affinities: List[Dict], performance_metrics: Dict) -> float:
        """Calculate defensive coordination score with basketball intelligence"""
        try:
            # Water (defensive) elemental strength
            water_strength = sum(a.get("water", 0) for a in affinities) / len(affinities) if affinities else 0.5
            
            # Basketball performance modifiers
            defensive_rating = performance_metrics.get("defensive_rating", 110.0)
            steal_rate = performance_metrics.get("steal_rate", 0.08)
            block_rate = performance_metrics.get("block_rate", 0.05)
            
            # Combine elemental and performance factors
            performance_factor = max(0.0, (120 - defensive_rating) / 20) + (steal_rate * 5) + (block_rate * 10)
            coordination_score = (water_strength * 0.6) + (performance_factor * 0.4)
            
            return min(1.0, max(0.0, coordination_score))
        except Exception as e:
            logger.error(f"Error calculating defensive coordination: {e}")
            return 0.5

    def _calculate_transition_synergy(self, affinities: List[Dict], performance_metrics: Dict) -> float:
        """Calculate transition game synergy with basketball intelligence"""
        try:
            # Air (transition/pace) elemental strength
            air_strength = sum(a.get("air", 0) for a in affinities) / len(affinities) if affinities else 0.5
            
            # Basketball performance modifiers
            pace = performance_metrics.get("pace", 100.0)
            turnover_rate = performance_metrics.get("turnover_rate", 0.14)
            
            # Combine factors (higher pace is good, lower turnovers are good)
            performance_factor = ((pace - 95) / 15) * max(0.0, (0.20 - turnover_rate) / 0.06)
            synergy_score = (air_strength * 0.7) + (performance_factor * 0.3)
            
            return min(1.0, max(0.0, synergy_score))
        except Exception as e:
            logger.error(f"Error calculating transition synergy: {e}")
            return 0.5

    def _calculate_clutch_chemistry(self, affinities: List[Dict], performance_metrics: Dict) -> float:
        """Calculate clutch performance chemistry with basketball intelligence"""
        try:
            # Overall elemental balance for clutch situations
            total_affinity = sum(
                a.get("fire", 0) + a.get("water", 0) + a.get("earth", 0) + a.get("air", 0)
                for a in affinities
            ) / len(affinities) if affinities else 2.0
            
            # Clutch performance metrics
            clutch_win_pct = performance_metrics.get("close_game_win_percentage", 0.5)
            fourth_quarter_rating = performance_metrics.get("fourth_quarter_net_rating", 0.0)
            
            # Combine elemental balance with clutch performance
            balance_factor = min(1.0, total_affinity / 1.0) # Normalize to 1.0
            performance_factor = (clutch_win_pct + max(0.0, (fourth_quarter_rating + 10) / 20)) / 2
            
            clutch_score = (balance_factor * 0.4) + (performance_factor * 0.6)
            return min(1.0, max(0.0, clutch_score))
        except Exception as e:
            logger.error(f"Error calculating clutch chemistry: {e}")
            return 0.5

    def _calculate_elemental_balance_score(self, affinities: List[Dict]) -> float:
        """Calculate how balanced the team's elemental distribution is"""
        try:
            if not affinities:
                return 0.5
            
            # Calculate team elemental totals
            totals = {"fire": 0, "water": 0, "earth": 0, "air": 0}
            for affinity in affinities:
                for element in totals:
                    totals[element] += affinity.get(element, 0)
            
            # Calculate balance (lower variance = more balanced)
            values = list(totals.values())
            if not values or sum(values) == 0:
                return 0.5
            
            mean_val = sum(values) / len(values)
            variance = sum((v - mean_val) ** 2 for v in values) / len(values)
            balance_score = max(0.0, 1.0 - (variance / (mean_val ** 2)) if mean_val > 0 else 0.5)
            
            return min(1.0, balance_score)
        except Exception as e:
            logger.error(f"Error calculating elemental balance score: {e}")
            return 0.5

    def _assess_elemental_complementarity(self, affinities: List[Dict]) -> Dict:
        """Assess how well players' elements complement each other"""
        try:
            if len(affinities) < 2:
                return {"complementarity_score": 0.5, "synergistic_elements": []}
            
            # Find elemental pairings and their synergy potential
            synergistic_pairs = [
                ("fire", "air"), # Offense + Pace
                ("water", "earth"), # Defense + Rebounding
                ("fire", "earth"), # Scoring + Physical presence
                ("water", "air") # Defense + Ball movement
            ]
            
            complementarity_scores = []
            synergistic_elements = []
            
            for elem1, elem2 in synergistic_pairs:
                elem1_strength = sum(a.get(elem1, 0) for a in affinities) / len(affinities)
                elem2_strength = sum(a.get(elem2, 0) for a in affinities) / len(affinities)
                
                # Higher score when both elements are reasonably strong
                pair_score = min(elem1_strength, elem2_strength) * 2
                complementarity_scores.append(pair_score)
                
                if pair_score > 0.4:
                    synergistic_elements.append(f"{elem1}_{elem2}")
            
            overall_complementarity = sum(complementarity_scores) / len(complementarity_scores) if complementarity_scores else 0.5
            
            return {
                "complementarity_score": min(1.0, overall_complementarity),
                "synergistic_elements": synergistic_elements,
                "pair_scores": dict(zip([f"{p[0]}_{p[1]}" for p in synergistic_pairs], complementarity_scores))
            }
        except Exception as e:
            logger.error(f"Error assessing elemental complementarity: {e}")
            return {"complementarity_score": 0.5, "error": str(e)}

    def _identify_synergistic_player_pairs(self, affinities: List[Dict]) -> List[Dict]:
        """Identify player pairs with high synergistic potential"""
        try:
            if len(affinities) < 2:
                return []
            
            synergistic_pairs = []
            
            for i, player1 in enumerate(affinities):
                for j, player2 in enumerate(affinities[i+1:], i+1):
                    # Calculate synergy score between two players
                    synergy_score = self._calculate_player_pair_synergy(player1, player2)
                    
                    if synergy_score > 0.6: # Threshold for synergistic pair
                        synergistic_pairs.append({
                            "player1_id": player1.get("hero_id", f"player_{i}"),
                            "player2_id": player2.get("hero_id", f"player_{j}"),
                            "synergy_score": synergy_score,
                            "synergy_type": self._determine_synergy_type(player1, player2)
                        })
            
            # Sort by synergy score
            synergistic_pairs.sort(key=lambda x: x["synergy_score"], reverse=True)
            return synergistic_pairs[:5] # Return top 5 pairs
            
        except Exception as e:
            logger.error(f"Error identifying synergistic player pairs: {e}")
            return []

    def _calculate_player_pair_synergy(self, player1: Dict, player2: Dict) -> float:
        """Calculate synergy score between two players"""
        try:
            # Complementary elements have higher synergy
            complementary_bonus = 0.0
            
            # Check for complementary elemental strengths
            p1_strongest = max(player1, key=lambda k: player1.get(k, 0) if k in ["fire", "water", "earth", "air"] else 0)
            p2_strongest = max(player2, key=lambda k: player2.get(k, 0) if k in ["fire", "water", "earth", "air"] else 0)
            
            complementary_pairs = [
                ("fire", "air"), ("air", "fire"),
                ("water", "earth"), ("earth", "water"),
                ("fire", "earth"), ("earth", "fire"),
                ("water", "air"), ("air", "water")
            ]
            
            if (p1_strongest, p2_strongest) in complementary_pairs:
                complementary_bonus = 0.3
            
            # Calculate overall elemental harmony
            total_harmony = 0.0
            for element in ["fire", "water", "earth", "air"]:
                p1_val = player1.get(element, 0)
                p2_val = player2.get(element, 0)
                # Higher synergy when elements are balanced between players
                total_harmony += min(p1_val, p2_val) + (abs(p1_val - p2_val) * 0.3)
            
            base_synergy = total_harmony / 4.0 # Normalize
            return min(1.0, base_synergy + complementary_bonus)
            
        except Exception as e:
            logger.error(f"Error calculating player pair synergy: {e}")
            return 0.5

    def _determine_synergy_type(self, player1: Dict, player2: Dict) -> str:
        """Determine the type of synergy between two players"""
        try:
            p1_strongest = max(["fire", "water", "earth", "air"], key=lambda k: player1.get(k, 0))
            p2_strongest = max(["fire", "water", "earth", "air"], key=lambda k: player2.get(k, 0))
            
            synergy_types = {
                ("fire", "air"): "offensive_pace_synergy",
                ("water", "earth"): "defensive_rebounding_synergy", 
                ("fire", "earth"): "scoring_presence_synergy",
                ("water", "air"): "defensive_transition_synergy",
                ("fire", "fire"): "dual_scoring_threat",
                ("water", "water"): "defensive_wall",
                ("earth", "earth"): "frontcourt_dominance",
                ("air", "air"): "pace_and_space"
            }
            
            return synergy_types.get((p1_strongest, p2_strongest), "general_compatibility")
            
        except Exception as e:
            logger.error(f"Error determining synergy type: {e}")
            return "unknown_synergy"

    def _map_chemistry_network(self, affinities: List[Dict]) -> Dict:
        """Map the team's chemistry network connections"""
        try:
            if len(affinities) < 2:
                return {"network_density": 0.0, "key_connectors": []}
            
            # Calculate network connections based on elemental compatibility
            connections = {}
            total_possible_connections = len(affinities) * (len(affinities) - 1) / 2
            actual_connections = 0
            
            for i, player1 in enumerate(affinities):
                player1_id = player1.get("hero_id", f"player_{i}")
                connections[player1_id] = []
                
                for j, player2 in enumerate(affinities):
                    if i != j:
                        synergy = self._calculate_player_pair_synergy(player1, player2)
                        if synergy > 0.5: # Connection threshold
                            player2_id = player2.get("hero_id", f"player_{j}")
                            connections[player1_id].append({
                                "connected_player": player2_id,
                                "connection_strength": synergy
                            })
                            if i < j: # Count each connection only once
                                actual_connections += 1
            
            # Identify key connectors (players with most connections)
            key_connectors = sorted(
                [(hero_id, len(conns)) for hero_id, conns in connections.items()],
               
                key=lambda x: x[1],
                reverse=True
            )[:3]
            
            network_density = actual_connections / total_possible_connections if total_possible_connections > 0 else 0.0
            
            return {
                "network_density": network_density,
                "key_connectors": [{"hero_id": kc[0], "connections": kc[1]} for kc in key_connectors],
                "total_connections": actual_connections,
                "connection_map": connections
            }
            
        except Exception as e:
            logger.error(f"Error mapping chemistry network: {e}")
            return {"network_density": 0.5, "error": str(e)}

    # Expert analysis helper methods
    def _analyze_offensive_strategy(self, performance_metrics: Dict) -> Dict:
        """Analyze team's offensive strategy and approach"""
        try:
            offensive_rating = performance_metrics.get("offensive_rating", 110.0)
            pace = performance_metrics.get("pace", 100.0)
            three_point_rate = performance_metrics.get("three_point_rate", 0.35)
            true_shooting = performance_metrics.get("true_shooting_percentage", 0.56)
            
            strategy_profile = {
                "pace_category": "fast" if pace > 102 else "moderate" if pace > 98 else "slow",
                "shooting_philosophy": "three_point_heavy" if three_point_rate > 0.40 else "balanced" if three_point_rate > 0.32 else "interior_focused",
                "efficiency_level": "elite" if true_shooting > 0.58 else "good" if true_shooting > 0.55 else "average",
                "offensive_rating_tier": "elite" if offensive_rating > 115 else "good" if offensive_rating > 110 else "average",
                "strategic_recommendation": self._get_offensive_recommendation(pace, three_point_rate, true_shooting)
            }
            
            return strategy_profile
        except Exception as e:
            logger.error(f"Error analyzing offensive strategy: {e}")
            return {"error": str(e)}

    def _analyze_defensive_approach(self, performance_metrics: Dict) -> Dict:
        """Analyze team's defensive approach and philosophy"""
        try:
            defensive_rating = performance_metrics.get("defensive_rating", 110.0)
            steal_rate = performance_metrics.get("steal_rate", 0.08)
            block_rate = performance_metrics.get("block_rate", 0.05)
            defensive_rebounding = performance_metrics.get("defensive_rebounding_rate", 0.75)
            
            approach_profile = {
                "defensive_tier": "elite" if defensive_rating < 105 else "good" if defensive_rating < 110 else "average",
                "pressure_style": "aggressive" if steal_rate > 0.09 else "moderate" if steal_rate > 0.07 else "conservative",
                "rim_protection": "strong" if block_rate > 0.06 else "moderate" if block_rate > 0.04 else "weak",
                "rebounding_focus": "excellent" if defensive_rebounding > 0.78 else "good" if defensive_rebounding > 0.74 else "needs_improvement",
                "defensive_philosophy": self._determine_defensive_philosophy(steal_rate, block_rate, defensive_rebounding)
            }
            
            return approach_profile
        except Exception as e:
            logger.error(f"Error analyzing defensive approach: {e}")
            return {"error": str(e)}

    def _analyze_pace_strategy(self, performance_metrics: Dict) -> Dict:
        """Analyze team's pace and tempo strategy"""
        try:
            pace = performance_metrics.get("pace", 100.0)
            offensive_rating = performance_metrics.get("offensive_rating", 110.0)
            defensive_rating = performance_metrics.get("defensive_rating", 110.0)
            turnover_rate = performance_metrics.get("turnover_rate", 0.14)
            
            pace_profile = {
                "pace_preference": "fast" if pace > 102 else "moderate" if pace > 98 else "slow",
                "pace_effectiveness": self._evaluate_pace_effectiveness(pace, offensive_rating, defensive_rating),
                "turnover_control": "excellent" if turnover_rate < 0.12 else "good" if turnover_rate < 0.15 else "needs_improvement",
                "optimal_pace_range": self._calculate_optimal_pace_range(offensive_rating, defensive_rating, turnover_rate),
                "pace_strategy_recommendation": self._recommend_pace_strategy(pace, offensive_rating, defensive_rating)
            }
            
            return pace_profile
        except Exception as e:
            logger.error(f"Error analyzing pace strategy: {e}")
            return {"error": str(e)}

    def _analyze_clutch_capabilities(self, performance_metrics: Dict) -> Dict:
        """Analyze team's clutch performance capabilities"""
        try:
            clutch_win_pct = performance_metrics.get("close_game_win_percentage", 0.5)
            fourth_quarter_rating = performance_metrics.get("fourth_quarter_net_rating", 0.0)
            clutch_offensive = performance_metrics.get("clutch_offensive_rating", 110.0)
            clutch_defensive = performance_metrics.get("clutch_defensive_rating", 110.0)
            
            clutch_profile = {
                "clutch_record_grade": "excellent" if clutch_win_pct > 0.65 else "good" if clutch_win_pct > 0.55 else "needs_improvement",
                "fourth_quarter_performance": "dominant" if fourth_quarter_rating > 5 else "good" if fourth_quarter_rating > 0 else "struggling",
                "clutch_offense_rating": "elite" if clutch_offensive > 115 else "good" if clutch_offensive > 110 else "average",
                "clutch_defense_rating": "elite" if clutch_defensive < 105 else "good" if clutch_defensive < 110 else "average",
                "clutch_factor": self._calculate_clutch_factor(clutch_win_pct, fourth_quarter_rating, clutch_offensive, clutch_defensive)
            }
            
            return clutch_profile
        except Exception as e:
            logger.error(f"Error analyzing clutch capabilities: {e}")
            return {"error": str(e)}

    # Fallback methods for expert error handling
    def _get_fallback_essence(self, error_type: str, error_details: str = "") -> Dict:
        """Provide fallback team essence in case of errors"""
        return {
            "elemental_profile": get_default_elemental_profile(),
            "war_council_report": self._get_fallback_war_report(error_type, error_details),
            "synergy_nexus": self._get_fallback_bonds(error_type, error_details),
            "expert_insights": {"error": f"Analysis failed: {error_type}"},
            "analysis_metadata": {
                "analysis_timestamp": datetime.now().isoformat(),
                "analyzer_version": "expert_v1.0_fallback",
                "error_type": error_type,
                "error_details": error_details
            }
        }

    def _get_fallback_war_report(self, error_type: str, error_details: str = "") -> Dict:
        """Provide fallback war report in case of errors"""
        return {
            "strategic_assessment": {
                "team_identity": "unknown_analysis_failed",
                "primary_strength": "analysis_unavailable",
                "main_weakness": "analysis_unavailable",
                "playoff_readiness": "unknown"
            },
            "tactical_analysis": {"error": f"Analysis failed: {error_type}"},
            "roster_evaluation": {"error": f"Evaluation failed: {error_type}"},
            "matchup_intelligence": {"error": f"Intelligence unavailable: {error_type}"},
            "war_council_recommendations": {
                "immediate_focus": "resolve_data_issues",
                "strategic_adjustments": "ensure_data_availability"
            },
            "report_metadata": {
                "analysis_timestamp": datetime.now().isoformat(),
                "error_type": error_type,
                "error_details": error_details,
                "report_version": "fallback_v1.0"
            }
        }

    def _get_fallback_bonds(self, error_type: str, error_details: str = "") -> Dict:
        """Provide fallback Olympian bonds in case of errors"""
        return {
            "synergy_score": 0.5,
            "assist_web": {"error": f"Analysis failed: {error_type}"},
            "defensive_harmony": {"coordination_score": 0.5, "error": error_type},
            "chronosync": {"temporal_sync": 0.5, "error": error_type},
            "basketball_chemistry": {"error": f"Chemistry analysis failed: {error_type}"},
            "elemental_harmony": {"balance_score": 0.5, "error": error_type},
            "expert_insights": {
                "chemistry_grade": "unknown",
                "improvement_areas": ["resolve_data_issues"],
                "error_type": error_type
            },
            "bonds_metadata": {
                "analysis_timestamp": datetime.now().isoformat(),
                "error_type": error_type,
                "error_details": error_details,
                "analysis_version": "fallback_v1.0"
            }
        }

    # Additional utility methods for comprehensive analysis
    def _calculate_momentum_factor(self, performance_metrics: Dict) -> float:
        """Calculate team momentum factor"""
        try:
            # Use recent performance indicators to determine momentum
            fourth_quarter_rating = performance_metrics.get("fourth_quarter_net_rating", 0.0)
            close_game_win_pct = performance_metrics.get("close_game_win_percentage", 0.5)
            
            # Positive momentum indicators
            momentum = (fourth_quarter_rating / 10) + (close_game_win_pct - 0.5)
            return min(1.0, max(-1.0, momentum))
        except Exception as e:
            logger.error(f"Error calculating momentum factor: {e}")
            return 0.0

    def _calculate_adaptation_rate(self, performance_metrics: Dict) -> float:
        """Calculate team's adaptation rate to different situations"""
        try:
            # Use performance variance as inverse indicator of adaptation
            performance_variance = performance_metrics.get("performance_variance", 0.15)
            chemistry_stability = performance_metrics.get("chemistry_stability", 0.75)
            
            # Lower variance and higher stability indicate better adaptation
            adaptation_rate = chemistry_stability * (1.0 - performance_variance)
            return min(1.0, max(0.0, adaptation_rate))
        except Exception as e:
            logger.error(f"Error calculating adaptation rate: {e}")
            return 0.5

    # Backward compatibility and factory functions

def create_expert_team_analytics(
    quantum_enabled: bool = True,
    basketball_intelligence: bool = True,
    config: Optional[Dict[str, Any]] = None
) -> ExpertTeamOlympianAnalytics:
    """Factory function to create expert team analytics with specified capabilities"""
    return ExpertTeamOlympianAnalytics(
        config=config,
        enable_quantum_analytics=quantum_enabled,
        enable_basketball_intelligence=basketball_intelligence
    )

def create_standard_team_analytics(config: Optional[Dict[str, Any]] = None) -> ExpertTeamOlympianAnalytics:
    """Factory function to create standard team analytics"""
    return ExpertTeamOlympianAnalytics(
        config=config,
        enable_quantum_analytics=False,
        enable_basketball_intelligence=True
    )

# Backward compatibility alias - make ExpertTeamOlympianAnalytics available as TeamOlympianAnalytics
TeamOlympianAnalytics = ExpertTeamOlympianAnalytics

# Example usage and testing
if __name__ == "__main__":
    
    async def test_expert_analytics():
        analytics = create_expert_team_analytics()
        team_essence = await analytics.get_team_essence("LAL")
        # Example: print out the keys of expert_insights if present
        if 'quantum_analytics' in team_essence['expert_insights']:
            print("Quantum analytics present.")
        if 'basketball_intelligence' in team_essence['expert_insights']:
            print("Basketball intelligence present.")
        print("Team Essence:", team_essence)
    
    # Run the test
    asyncio.run(test_expert_analytics())
