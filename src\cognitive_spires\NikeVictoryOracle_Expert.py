import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any, Optional, Union
import asyncio
from dataclasses import dataclass, field
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import warnings
from src.features.feature_feedback import FeatureFeedback

# Optional import to avoid circular dependency
try:
    from src.features.feature_alchemist import SelfLearningFeatureAlchemist
except ImportError:
    # Fallback for circular import
    SelfLearningFeatureAlchemist = None

#!/usr/bin/env python3
"""
NikeVictoryOracle_Expert.py
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
Advanced Expert-Level Nike Victory Oracle for NBA Predictions
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

This module provides expert-level victory prediction and momentum analysis capabilities,
significantly enhanced beyond the basic NikeOracle with production-grade features.
"""

warnings.filterwarnings('ignore')

# Configure logger
logger = logging.getLogger(__name__)


@dataclass
class VictoryConfig:
    """Configuration for NikeVictoryOracle_Expert"""
    momentum_threshold: float = 0.6
    clutch_time_threshold: int = 300 # seconds
    home_advantage_factor: float = 0.12
    recent_games_window: int = 10
    confidence_threshold: float = 0.65
    upset_sensitivity: float = 0.15
    comeback_threshold: float = 15.0 # points
    streak_importance: float = 0.25
    head_to_head_weight: float = 0.3


@dataclass
class VictoryMetrics:
    """Comprehensive victory prediction metrics"""
    win_probability: float = 0.5
    momentum_score: float = 0.0
    clutch_factor: float = 0.0
    upset_potential: float = 0.0
    comeback_capability: float = 0.0
    closing_strength: float = 0.0
    psychological_edge: float = 0.0
    confidence_score: float = 0.0
    key_factors: List[str] = field(default_factory=list)


@dataclass
class MomentumContext:
    """Context for momentum analysis"""
    recent_win_streak: int = 0
    recent_loss_streak: int = 0
    last_5_record: Tuple[int, int] = (0, 0)
    scoring_trend: float = 0.0
    defensive_trend: float = 0.0
    injury_impact: float = 0.0
    rest_advantage: int = 0
    revenge_game: bool = False
    playoff_implications: bool = False


class NikeVictoryOracle_Expert:
    """
    👟 Expert-Level Nike Victory Oracle
    Advanced victory prediction engine that provides sophisticated win probability,
    momentum analysis, and clutch performance modeling for NBA games.
    """
    
    def __init__(self, config: Optional[VictoryConfig] = None, enable_momentum_tracking: bool = True, **kwargs):
        """Initialize the Expert Nike Oracle"""
        self.enable_momentum_tracking = enable_momentum_tracking
        self.config = config or VictoryConfig()
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Advanced models for victory prediction
        self.win_probability_model = RandomForestClassifier(
            n_estimators=250,
            max_depth=20,
            random_state=42,
            n_jobs=-1,
            class_weight='balanced'
        )
        
        self.momentum_model = GradientBoostingClassifier(
            n_estimators=150,
            learning_rate=0.1,
            max_depth=8,
            random_state=42
        )
        
        self.clutch_model = LogisticRegression(
            random_state=42,
            max_iter=1000,
            class_weight='balanced'
        )
        
        # Victory pattern recognition
        self.victory_patterns = {}
        self.momentum_history = {}
        self.clutch_performance = {}
        self.upset_patterns = {}
        
        # Expert-level feature engineering
        self.feature_extractors = {
            'offensive': self._extract_offensive_features,
            'defensive': self._extract_defensive_features,
            'momentum': self._extract_momentum_features,
            'situational': self._extract_situational_features,
            'psychological': self._extract_psychological_features
        }
        # Model performance metrics
        self.model_metrics = {
            'accuracy': 0.0,
            'precision': 0.0,
            'recall': 0.0,
            'f1_score': 0.0,
            'log_loss': float('inf')
        }

        # Enhanced basketball intelligence capabilities
        self.temporal_momentum_windows = [5, 10, 15, 20]  # minutes
        self.clutch_time_threshold = 300  # 5 minutes in seconds
        self.rest_fatigue_analysis = True
        self.injury_impact_analysis = True

        # Advanced basketball models
        self.clutch_performance_model = None
        self.injury_impact_model = None
        self.rest_fatigue_model = None
        self.momentum_tracker = {}
        self.live_accuracy_tracker = []

        self.logger.info("🏀 MEDUSA VAULT: 👟 Enhanced NikeVictoryOracle_Expert initialized with advanced basketball intelligence")
        self.feature_alchemist = SelfLearningFeatureAlchemist(model_trainer=None, data_source=None)

    async def analyze_clutch_performance(self, team_id: str, game_context: Dict[str, Any]) -> Dict[str, float]:
        """Analyze clutch performance capabilities"""
        try:
            # Extract clutch-relevant features
            clutch_features = {
                'fourth_quarter_performance': game_context.get('fourth_quarter_stats', {}).get('efficiency', 0.5),
                'close_game_record': game_context.get('close_game_record', 0.5),
                'pressure_situations': game_context.get('pressure_performance', 0.5),
                'star_player_clutch': game_context.get('star_clutch_rating', 0.5),
                'coaching_clutch': game_context.get('coach_clutch_rating', 0.5)
            }

            # Calculate clutch factor
            clutch_weights = [0.3, 0.25, 0.2, 0.15, 0.1]
            clutch_score = sum(score * weight for score, weight in zip(clutch_features.values(), clutch_weights))

            # Adjust for game situation
            game_time_remaining = game_context.get('time_remaining', 2880)  # seconds
            if game_time_remaining <= self.clutch_time_threshold:
                clutch_score *= 1.2  # Boost clutch importance in actual clutch time

            return {
                'clutch_score': clutch_score,
                'clutch_confidence': min(0.95, max(0.1, clutch_score)),
                'clutch_factors': clutch_features
            }

        except Exception as e:
            self.logger.warning(f"Clutch analysis failed: {e}")
            return {'clutch_score': 0.5, 'clutch_confidence': 0.5, 'clutch_factors': {}}

    async def analyze_temporal_momentum(self, team_id: str, game_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze momentum across different time windows"""
        try:
            momentum_analysis = {}

            for window in self.temporal_momentum_windows:
                window_key = f"momentum_{window}min"

                # Extract momentum features for this time window
                recent_performance = game_data.get(f'last_{window}_minutes', {})

                momentum_factors = {
                    'scoring_run': recent_performance.get('scoring_differential', 0),
                    'defensive_stops': recent_performance.get('defensive_stops', 0),
                    'turnovers_forced': recent_performance.get('turnovers_forced', 0),
                    'shooting_percentage': recent_performance.get('fg_percentage', 0.45),
                    'energy_level': recent_performance.get('energy_rating', 0.5)
                }

                # Calculate momentum score for this window
                momentum_score = self._calculate_momentum_score(momentum_factors)

                momentum_analysis[window_key] = {
                    'score': momentum_score,
                    'factors': momentum_factors,
                    'trend': 'positive' if momentum_score > 0.6 else 'negative' if momentum_score < 0.4 else 'neutral'
                }

            # Calculate overall momentum
            overall_momentum = np.mean([analysis['score'] for analysis in momentum_analysis.values()])
            momentum_analysis['overall'] = {
                'score': overall_momentum,
                'confidence': min(0.95, max(0.1, 1.0 - abs(overall_momentum - 0.5) * 2))
            }

            return momentum_analysis

        except Exception as e:
            self.logger.warning(f"Temporal momentum analysis failed: {e}")
            return {'overall': {'score': 0.5, 'confidence': 0.5}}

    def _calculate_momentum_score(self, factors: Dict[str, float]) -> float:
        """Calculate momentum score from factors"""
        weights = {
            'scoring_run': 0.3,
            'defensive_stops': 0.25,
            'turnovers_forced': 0.2,
            'shooting_percentage': 0.15,
            'energy_level': 0.1
        }

        # Normalize factors to 0-1 range
        normalized_factors = {}
        for factor, value in factors.items():
            if factor == 'scoring_run':
                # Scoring differential: normalize around -20 to +20
                normalized_factors[factor] = max(0, min(1, (value + 20) / 40))
            elif factor == 'shooting_percentage':
                # Already in 0-1 range
                normalized_factors[factor] = max(0, min(1, value))
            else:
                # Other factors: assume 0-10 scale
                normalized_factors[factor] = max(0, min(1, value / 10))

        # Calculate weighted score
        momentum_score = sum(normalized_factors.get(factor, 0.5) * weight
                           for factor, weight in weights.items())

        return momentum_score

    async def predict_victory(
            self,
            home_team_id: str,
            away_team_id: str,
            game_date: datetime,
            home_stats: Dict[str, Any],
            away_stats: Dict[str, Any],
            context: Optional[MomentumContext] = None
            ) -> VictoryMetrics:
        """
            Predict game victory with expert-level precision
        
        Args:
            home_team_id: Home team identifier
            away_team_id: Away team identifier
            game_date: Date of the game
            home_stats: Home team statistics and context
            away_stats: Away team statistics and context
            context: Additional momentum context
        Returns:
            VictoryMetrics: Comprehensive victory prediction
        """
        try:
            # Extract comprehensive features
            features = await self._extract_all_features(
                home_team_id, away_team_id, game_date, home_stats, away_stats, context
            )
            
            # Multi-model victory prediction
            win_prob = await self._calculate_win_probability(features)
            momentum_score = await self._calculate_momentum_score(features)
            clutch_factor = await self._calculate_clutch_factor(features)
            
            # Advanced analytics
            upset_potential = await self._assess_upset_potential(features, win_prob)
            comeback_capability = await self._assess_comeback_capability(features)
            closing_strength = await self._assess_closing_strength(features)
            psychological_edge = await self._assess_psychological_edge(features)
            
            # Identify key factors
            key_factors = await self._identify_key_factors(features, win_prob)
            
            # Confidence scoring
            confidence_score = await self._calculate_confidence_score(
                [win_prob, momentum_score, clutch_factor]
            )
            
            metrics = VictoryMetrics(
                win_probability=win_prob,
                momentum_score=momentum_score,
                clutch_factor=clutch_factor,
                upset_potential=upset_potential,
                comeback_capability=comeback_capability,
                closing_strength=closing_strength,
                psychological_edge=psychological_edge,
                confidence_score=confidence_score,
                key_factors=key_factors
            )
            
            # Store for pattern learning
            await self._update_victory_memory(home_team_id, away_team_id, game_date, metrics)
            
            self.logger.info(f"🏀 Expert victory prediction complete: {home_team_id} vs {away_team_id}")
            return metrics
            
        except Exception as e:
            self.logger.error(f"🚨 Victory prediction failed: {e}")
            return VictoryMetrics(confidence_score=0.0)
    
    async def analyze_live_momentum(
            self,
            home_team_id: str,
            away_team_id: str,
            current_score: Tuple[int, int],
            time_remaining: int,
            recent_plays: List[Dict[str, Any]]
            ) -> Dict[str, float]:
        """
            Analyze live momentum during a game
    
        Args:
            home_team_id: Home team identifier
            away_team_id: Away team identifier
            current_score: (home_score, away_score)
            time_remaining: Seconds remaining in game
            recent_plays: Recent plays for momentum analysis
    
        Returns:
            Dict containing live momentum metrics
        """
        try:
            momentum_metrics = {
                'current_momentum': 0.0,
                'momentum_shift': 0.0,
                'clutch_time_factor': 0.0,
                'comeback_probability': 0.0,
                'lead_sustainability': 0.0
            }
    
            # Calculate current momentum from recent plays
            current_momentum = await self._calculate_live_momentum(recent_plays)
    
            # Detect momentum shifts
            momentum_shift = await self._detect_momentum_shift(recent_plays)
    
            # Clutch time analysis
            clutch_factor = 1.0 if time_remaining <= self.config.clutch_time_threshold else 0.0
    
            # Comeback probability based on score differential and time
            home_score, away_score = current_score
            score_diff = abs(home_score - away_score)
            comeback_prob = await self._calculate_comeback_probability(score_diff, time_remaining)
    
            # Lead sustainability
            leading_team = "home" if home_score > away_score else "away"
            lead_sustainability = await self._calculate_lead_sustainability(
                score_diff, time_remaining, current_momentum, leading_team
            )
    
            momentum_metrics.update({
                'current_momentum': current_momentum,
                'momentum_shift': momentum_shift,
                'clutch_time_factor': clutch_factor,
                'comeback_probability': comeback_prob,
                'lead_sustainability': lead_sustainability
            })
    
            self.logger.info(f" Live momentum analyzed: {home_team_id} vs {away_team_id}")
            return momentum_metrics
    
        except Exception as e:
            self.logger.error(f" Live momentum analysis failed: {e}")
            return {'current_momentum': 0.0, 'momentum_shift': 0.0, 'clutch_time_factor': 0.0,
                    'comeback_probability': 0.0, 'lead_sustainability': 0.0}
    
    async def predict_upset_potential(
            self,
            favorite_team_id: str,
            underdog_team_id: str,
            betting_line: float,
            game_context: Dict[str, Any]
            ) -> Dict[str, Any]:
        """
            Predict upset potential using advanced analytics
    
        Args:
            favorite_team_id: Favored team identifier
            underdog_team_id: Underdog team identifier
            betting_line: Point spread
            game_context: Additional game context
    
        Returns:
            Upset analysis with probability and key factors
        """
        try:
            upset_analysis = {
                'upset_probability': 0.0,
                'confidence': 0.0,
                'key_upset_factors': [],
                'historical_precedent': 0.0,
                'value_bet_rating': 0.0
            }
    
            # Calculate base upset probability
            base_upset_prob = await self._calculate_base_upset_probability(
                favorite_team_id, underdog_team_id, betting_line
            )
    
            # Analyze upset factors
            upset_factors = await self._analyze_upset_factors(
                favorite_team_id, underdog_team_id, game_context
            )
    
            # Historical precedent analysis
            historical_precedent = await self._analyze_historical_upsets(
                favorite_team_id, underdog_team_id, betting_line
            )
    
            # Adjust probability based on factors
            adjusted_upset_prob = base_upset_prob
            for factor, impact in upset_factors.items():
                adjusted_upset_prob += impact * self.config.upset_sensitivity
    
            adjusted_upset_prob = max(0.0, min(1.0, adjusted_upset_prob))
    
            # Value bet rating
            value_rating = await self._calculate_value_bet_rating(
                adjusted_upset_prob, betting_line
            )
    
            upset_analysis.update({
                'upset_probability': adjusted_upset_prob,
                'confidence': min(0.9, base_upset_prob + 0.3),
                'key_upset_factors': list(upset_factors.keys()),
                'historical_precedent': historical_precedent,
                'value_bet_rating': value_rating
            })
    
            self.logger.info(f" Upset analysis complete: {underdog_team_id} over {favorite_team_id}")
            return upset_analysis
    
        except Exception as e:
            self.logger.error(f" Upset prediction failed: {e}")
            return {
                'upset_probability': 0.0,
                'confidence': 0.0,
                'key_upset_factors': [],
                'historical_precedent': 0.0,
                'value_bet_rating': 0.0
            }
    
            # Private helper methods for expert-level analysis
    
    async def _extract_all_features(
            self,
            home_team_id: str,
            away_team_id: str,
            game_date: datetime,
            home_stats: Dict[str, Any],
            away_stats: Dict[str, Any],
            context: Optional[MomentumContext]
            ) -> Dict[str, float]:
        """Extract comprehensive features for prediction"""
        features = {}
    
        # Extract features from all categories
        for category, extractor in self.feature_extractors.items():
            try:
                if category in ['offensive', 'defensive']:
                    home_features = extractor(home_stats, 'home')
                    away_features = extractor(away_stats, 'away')
                    features.update(home_features)
                    features.update(away_features)
                elif category == 'momentum':
                    momentum_features = extractor(context or MomentumContext())
                    features.update(momentum_features)
                elif category == 'situational':
                    situational_features = extractor(game_date, home_stats, away_stats)
                    features.update(situational_features)
                elif category == 'psychological':
                    psych_features = extractor(home_team_id, away_team_id, context)
                    features.update(psych_features)
            except Exception as e:
                self.logger.warning(f" TITAN PROCESSING FAILED: extract {category} features: {e}")
    
        return features
    
    async def _calculate_win_probability(self, features: Dict[str, float]) -> float:
        """Calculate win probability using ensemble models"""
        try:
            # Convert features to array
            feature_array = np.array(list(features.values())).reshape(1, -1)
    
            # Handle case where model isn't trained yet
            if not hasattr(self.win_probability_model, 'classes_'):
                # Use simplified calculation
                offensive_diff = features.get('home_offensive_rating', 0.5) - features.get('away_offensive_rating', 0.5)
                defensive_diff = features.get('away_defensive_rating', 0.5) - features.get('home_defensive_rating', 0.5)
                momentum_factor = features.get('momentum_score', 0.0)
    
                win_prob = 0.5 + (offensive_diff + defensive_diff + momentum_factor) * 0.2
                win_prob += self.config.home_advantage_factor # Home advantage
    
                return max(0.0, min(1.0, win_prob))
            else: # Added else for clarity
                # Use trained model
                win_prob = self.win_probability_model.predict_proba(feature_array)[0][1]
                return win_prob
    
        except Exception as e:
            self.logger.warning(f"Win probability calculation error: {e}")
            return 0.5
    
    async def _calculate_momentum_score(self, features: Dict[str, float]) -> float:
        """Calculate momentum score"""
        momentum_components = [
            features.get('recent_win_streak', 0.0) * 0.1,
            features.get('scoring_trend', 0.0) * 0.15,
            features.get('defensive_trend', 0.0) * 0.15,
            features.get('last_5_win_rate', 0.5) - 0.5, # Center around 0
        ]
    
        momentum_score = sum(momentum_components)
        return max(-1.0, min(1.0, momentum_score))
    
    async def _calculate_clutch_factor(self, features: Dict[str, float]) -> float:
        """Calculate clutch performance factor"""
        clutch_components = [
            features.get('home_clutch_rating', 0.5),
            features.get('away_clutch_rating', 0.5),
            features.get('home_fourth_quarter_rating', 0.5),
            features.get('away_fourth_quarter_rating', 0.5)
        ]
    
        home_clutch = (clutch_components[0] + clutch_components[2]) / 2
        away_clutch = (clutch_components[1] + clutch_components[3]) / 2
    
        return home_clutch - away_clutch # Positive favors home team
    
    async def _assess_upset_potential(self, features: Dict[str, float], win_prob: float) -> float:
        """Assess upset potential"""
        # High upset potential when underdog has specific advantages
        underdog_advantages = [
            features.get('momentum_score', 0.0) > 0.3, # Strong momentum
            features.get('rest_advantage', 0) > 1, # Rest advantage
            features.get('revenge_factor', 0.0) > 0.5, # Revenge game
            features.get('injury_impact_differential', 0.0) > 0.2 # Opponent injuries
        ]
    
        upset_potential = sum(underdog_advantages) / len(underdog_advantages)
    
        # Adjust based on win probability
        if win_prob < 0.3: # Significant underdog
            upset_potential *= 1.5
    
        return min(1.0, upset_potential)
    
    async def _assess_comeback_capability(self, features: Dict[str, float]) -> float:
        """Assess comeback capability"""
        comeback_factors = [
            features.get('home_fourth_quarter_rating', 0.5),
            features.get('home_clutch_rating', 0.5),
            features.get('home_three_point_rate', 0.3), # Three-point shooting for quick scoring
            features.get('home_pace', 0.5) # Fast pace helps comebacks
        ]
    
        return sum(comeback_factors) / len(comeback_factors)
    
    async def _assess_closing_strength(self, features: Dict[str, float]) -> float:
        """Assess ability to close out games"""
        closing_factors = [
            features.get('home_fourth_quarter_defensive_rating', 0.5),
            features.get('home_free_throw_rate', 0.5),
            features.get('home_turnover_rate', 0.5), # Lower is better
            features.get('home_clutch_rating', 0.5)
        ]
    
        # Invert turnover rate (lower is better)
        closing_factors[2] = 1.0 - closing_factors[2]
    
        return sum(closing_factors) / len(closing_factors)
    
    async def _assess_psychological_edge(self, features: Dict[str, float]) -> float:
        """Assess psychological advantages"""
        psych_factors = [
            features.get('home_advantage', 0.0),
            features.get('revenge_factor', 0.0),
            features.get('playoff_implications', 0.0),
            features.get('head_to_head_advantage', 0.0),
            features.get('confidence_momentum', 0.0)
        ]
    
        return sum(psych_factors) / len(psych_factors)
    
    async def _identify_key_factors(self, features: Dict[str, float], win_prob: float) -> List[str]:
        """Identify key factors influencing the prediction"""
        key_factors = []
    
        # Significant feature thresholds
        if abs(features.get('offensive_rating_diff', 0.0)) > 0.15:
            key_factors.append("Significant offensive advantage")
    
        if abs(features.get('defensive_rating_diff', 0.0)) > 0.15:
            key_factors.append("Significant defensive advantage")
    
        if features.get('momentum_score', 0.0) > 0.3:
            key_factors.append("Strong recent momentum")
        elif features.get('momentum_score', 0.0) < -0.3:
            key_factors.append("Poor recent momentum")
    
        if features.get('rest_advantage', 0) > 1:
            key_factors.append("Rest advantage")
    
        if features.get('revenge_factor', 0.0) > 0.5:
            key_factors.append("Revenge game motivation")
    
        if features.get('home_advantage', 0.0) > 0.1:
            key_factors.append("Home court advantage")
    
        if features.get('injury_impact_differential', 0.0) > 0.2:
            key_factors.append("Opponent injury concerns")
    
        return key_factors
    
    async def _calculate_confidence_score(self, prediction_components: List[float]) -> float:
        """Calculate confidence in predictions"""
        # Base confidence on consistency of components
        base_confidence = 0.7
    
        # Reduce confidence for extreme outliers
        for component in prediction_components:
            if abs(component - 0.5) > 0.4: # Very extreme prediction
                base_confidence *= 0.9
    
        # Variance penalty
        if len(prediction_components) > 1:
            variance = np.var(prediction_components)
            if variance > 0.3:
                base_confidence *= 0.85
    
        return max(0.3, base_confidence)
    
        # Feature extraction methods
    
    def _extract_offensive_features(self, stats: Dict[str, Any], prefix: str) -> Dict[str, float]:
        """Extract offensive performance features"""
        return {
            f'{prefix}_offensive_rating': stats.get('offensive_rating', 110.0) / 120.0,
            f'{prefix}_pace': stats.get('pace', 100.0) / 110.0,
            f'{prefix}_three_point_rate': stats.get('three_point_attempt_rate', 0.35),
            f'{prefix}_free_throw_rate': stats.get('free_throw_rate', 0.25),
            f'{prefix}_turnover_rate': stats.get('turnover_rate', 0.15),
            f'{prefix}_assist_rate': stats.get('assist_rate', 0.6)
        }
    
    def _extract_defensive_features(self, stats: Dict[str, Any], prefix: str) -> Dict[str, float]:
        """Extract defensive performance features"""
        return {
            f'{prefix}_defensive_rating': stats.get('defensive_rating', 110.0) / 120.0,
            f'{prefix}_steal_rate': stats.get('steal_rate', 0.08),
            f'{prefix}_block_rate': stats.get('block_rate', 0.05),
            f'{prefix}_defensive_rebound_rate': stats.get('defensive_rebound_rate', 0.75),
            f'{prefix}_opponent_turnover_rate': stats.get('opponent_turnover_rate', 0.15)
        }
    
    def _extract_momentum_features(self, context: MomentumContext) -> Dict[str, float]:
        """Extract momentum-based features"""
        return {
            'recent_win_streak': min(context.recent_win_streak / 5.0, 1.0),
            'recent_loss_streak': min(context.recent_loss_streak / 5.0, 1.0),
            'last_5_win_rate': context.last_5_record[0] / 5.0,
            'scoring_trend': max(-0.5, min(0.5, context.scoring_trend / 20.0)),
            'defensive_trend': max(-0.5, min(0.5, context.defensive_trend / 20.0)),
            'injury_impact': min(context.injury_impact, 1.0),
            'rest_advantage': max(-3, min(3, context.rest_advantage)) / 3.0
        }
    
    def _extract_situational_features(
            self,
            game_date: datetime,
            home_stats: Dict[str, Any],
            away_stats: Dict[str, Any]
            ) -> Dict[str, float]:
        """Extract situational features"""
        # Determine if it's a playoff/important game
        is_playoff_season = game_date.month >= 4
        is_weekend = game_date.weekday() >= 5
    
        return {
            'home_advantage': self.config.home_advantage_factor,
            'playoff_season': 1.0 if is_playoff_season else 0.0,
            'weekend_game': 1.0 if is_weekend else 0.0,
            'season_month': game_date.month / 12.0,
            'days_rest_differential': max(-3, min(3,
                                                 home_stats.get('days_rest', 1) - away_stats.get('days_rest', 1))) / 3.0
        }
    
    def _extract_psychological_features(
            self,
            home_team_id: str,
            away_team_id: str,
            context: Optional[MomentumContext]
            ) -> Dict[str, float]:
        """Extract psychological factors"""
        features = {
            'revenge_factor': 0.0,
            'head_to_head_advantage': 0.0,
            'confidence_momentum': 0.0,
            'playoff_implications': 0.0
        }
    
        if context:
            features['revenge_factor'] = 1.0 if context.revenge_game else 0.0
            features['playoff_implications'] = 1.0 if context.playoff_implications else 0.0
    
            # Confidence based on recent performance
            if context.last_5_record[0] >= 4: # 4+ wins in last 5
                features['confidence_momentum'] = 0.8
            elif context.last_5_record[0] <= 1: # 1 or fewer wins in last 5
                features['confidence_momentum'] = 0.2
            else:
                features['confidence_momentum'] = 0.5
    
        return features
    
        # Live game analysis methods
    
    async def _calculate_live_momentum(self, recent_plays: List[Dict[str, Any]]) -> float:
        """Calculate current momentum from recent plays"""
        if not recent_plays:
            return 0.0
    
        momentum = 0.0
        total_weight = 0.0
    
        # Weight recent plays more heavily
        for i, play in enumerate(recent_plays[-10:]): # Last 10 plays
            weight = (i + 1) / 10.0 # More recent plays have higher weight
    
            play_type = play.get('type', '')
            team = play.get('team', '')
    
            play_momentum = 0.0
            if play_type in ['made_shot', 'three_pointer']:
                play_momentum = 0.2
            elif play_type in ['steal', 'block']:
                play_momentum = 0.3
            elif play_type in ['turnover', 'missed_shot']:
                play_momentum = -0.2
            elif play_type == 'and_one':
                play_momentum = 0.4
    
            # Adjust for team (assume home team is positive momentum)
            if team != 'home':
                play_momentum *= -1
    
            momentum += play_momentum * weight
            total_weight += weight
    
        return momentum / total_weight if total_weight > 0 else 0.0
    
    async def _detect_momentum_shift(self, recent_plays: List[Dict[str, Any]]) -> float:
        """Detect momentum shifts in recent plays"""
        if len(recent_plays) < 6:
            return 0.0
    
        # Compare momentum in last 3 plays vs previous 3 plays
        recent_momentum = await self._calculate_live_momentum(recent_plays[-3:])
        previous_momentum = await self._calculate_live_momentum(recent_plays[-6:-3])
    
        return recent_momentum - previous_momentum
    
    async def _calculate_comeback_probability(self, score_diff: int, time_remaining: int) -> float:
        """Calculate probability of comeback based on deficit and time"""
        if score_diff == 0:
            return 0.5
    
        # Base probability decreases with deficit and time
        time_factor = time_remaining / 2880.0 # Total game time in seconds
        deficit_factor = max(0.0, 1.0 - (score_diff / 25.0)) # 25-point deficit = 0% base probability
    
        comeback_prob = time_factor * deficit_factor * 0.8
    
        # Adjust for game situation
        if time_remaining <= 300: # Last 5 minutes
            comeback_prob *= 1.5 # Increased urgency can lead to faster scoring
    
        return min(1.0, comeback_prob)
    
    async def _calculate_lead_sustainability(
            self,
            score_diff: int,
            time_remaining: int,
            current_momentum: float,
            leading_team: str
            ) -> float:
        """Calculate how sustainable the current lead is"""
        if score_diff == 0:
            return 0.5
    
        # Base sustainability increases with lead size and decreases with time
        lead_factor = min(1.0, score_diff / 20.0) # 20-point lead = 100% base sustainability
        time_factor = 1.0 - (time_remaining / 2880.0)
    
        sustainability = (lead_factor * 0.7) + (time_factor * 0.3)
    
        # Adjust for current momentum
        if leading_team == 'home':
            sustainability += current_momentum * 0.2
        else:
            sustainability -= current_momentum * 0.2
    
        return max(0.0, min(1.0, sustainability))
    
        # Upset analysis methods
    
    async def _calculate_base_upset_probability(
            self,
            favorite_id: str,
            underdog_id: str,
            betting_line: float
            ) -> float:
        """Calculate base upset probability from betting line"""
        # Convert point spread to implied probability
        # Rough conversion: each point ≈ 2-3% win probability
        favorite_win_prob = 0.5 + (abs(betting_line) * 0.025)
        favorite_win_prob = min(0.95, favorite_win_prob) # Cap at 95%
    
        upset_prob = 1.0 - favorite_win_prob
        return upset_prob
    
    async def _analyze_upset_factors(
            self,
            favorite_id: str,
            underdog_id: str,
            game_context: Dict[str, Any]
            ) -> Dict[str, float]:
        """Analyze factors that contribute to upset potential"""
        upset_factors = {}
    
        # Rest advantage for underdog
        rest_diff = game_context.get('underdog_rest_advantage', 0)
        if rest_diff > 0:
            upset_factors['rest_advantage'] = min(0.15, rest_diff * 0.05)
    
        # Injuries to favorite
        favorite_injuries = game_context.get('favorite_key_injuries', 0)
        if favorite_injuries > 0:
            upset_factors['favorite_injuries'] = min(0.2, favorite_injuries * 0.1)
    
        # Underdog momentum
        underdog_momentum = game_context.get('underdog_recent_form', 0.5)
        if underdog_momentum > 0.7:
            upset_factors['underdog_hot_streak'] = (underdog_momentum - 0.5) * 0.3
    
        # Revenge game
        if game_context.get('revenge_game', False):
            upset_factors['revenge_motivation'] = 0.1
    
        # Playoff implications for underdog
        if game_context.get('underdog_playoff_implications', False):
            upset_factors['playoff_desperation'] = 0.08
    
        # Favorite overconfidence (back-to-back, trap game)
        if game_context.get('trap_game_potential', False):
            upset_factors['trap_game'] = 0.12
    
        return upset_factors
    
    async def _analyze_historical_upsets(
            self,
            favorite_id: str,
            underdog_id: str,
            betting_line: float
            ) -> float:
        """Analyze historical upset patterns"""
        # This would query historical database in production
        # For now, return a baseline based on betting line
    
        line_category = "small" if abs(betting_line) <= 3 else "medium" if abs(betting_line) <= 7 else "large"
    
        historical_rates = {
            "small": 0.42, # Close games upset ~42% of time
            "medium": 0.25, # Medium spreads upset ~25% of time
            "large": 0.12 # Large spreads upset ~12% of time
        }
    
        return historical_rates.get(line_category, 0.2)
    
    async def _calculate_value_bet_rating(self, predicted_prob: float, betting_line: float) -> float:
        """Calculate value bet rating"""
        # Convert betting line to implied probability
        implied_prob = 0.5 + (abs(betting_line) * 0.025)
        implied_prob = min(0.95, implied_prob)
    
        underdog_implied_prob = 1.0 - implied_prob
    
        # Value exists when our predicted probability > implied probability
        value = predicted_prob - underdog_implied_prob
    
        # Scale to 0-1 rating
        value_rating = max(0.0, min(1.0, value * 3.0)) # 33% edge = 100% value rating
    
        return value_rating
    
    async def _update_victory_memory(
            self,
            home_team_id: str,
            away_team_id: str,
            game_date: datetime,
            metrics: VictoryMetrics
            ) -> None:
        """Update victory memory for learning"""
        game_key = f"{home_team_id}_vs_{away_team_id}_{game_date.strftime('%Y%m%d')}"
    
        if home_team_id not in self.victory_patterns:
            self.victory_patterns[home_team_id] = []
    
        self.victory_patterns[home_team_id].append({
            'date': game_date.isoformat(),
            'opponent': away_team_id,
            'metrics': metrics
        })
    
        # Keep only recent patterns (last 30 games)
        self.victory_patterns[home_team_id] = self.victory_patterns[home_team_id][-30:]
    
    def get_model_performance(self) -> Dict[str, float]:
        """Get current model performance metrics"""
        return self.model_metrics.copy()
    
    async def retrain_models(self, training_data: List[Dict[str, Any]]) -> bool:
        """Retrain victory prediction models with new data"""
        try:
            if len(training_data) < 20:
                self.logger.warning(" TITAN WARNING: Insufficient training data for model retraining")
                return False
    
            # Prepare training data
            X = []
            y_win = []
            y_momentum = []
    
            for data_point in training_data:
                features = data_point.get('features', {})
                X.append(list(features.values()))
                y_win.append(1 if data_point.get('home_won', False) else 0)
                y_momentum.append(data_point.get('momentum_score', 0.0))
    
            X = np.array(X)
            y_win = np.array(y_win)
            y_momentum = np.array(y_momentum)
    
            # Retrain models
            self.win_probability_model.fit(X, y_win)
            self.momentum_model.fit(X, y_momentum)
    
            # Update performance metrics
            win_pred = self.win_probability_model.predict(X)
    
            self.model_metrics.update({
                'accuracy': accuracy_score(y_win, win_pred),
                'precision': precision_score(y_win, win_pred, average='weighted'),
                'recall': recall_score(y_win, win_pred, average='weighted'),
                'f1_score': f1_score(y_win, win_pred, average='weighted')
            })
    
            self.logger.info(f" Models retrained with {len(training_data)} samples")
            return True
    
        except Exception as e:
            self.logger.error(f" Model retraining failed: {e}")
            return False
    
    def _calculate_victory_probability(self, features: Dict[str, float]) -> float:
        """
            Calculate victory probability based on extracted features
        """
        try:
            # Convert features to numpy array for model prediction
            feature_values = np.array(list(features.values())).reshape(1, -1)
    
            # Use multiple models for ensemble prediction
            try:
                # Primary win probability from random forest
                win_prob = self.win_probability_model.predict_proba(feature_values)[0][1]
            except Exception: # Broad except to catch NotFittedError or similar
                # Fallback calculation if model not trained or fails
                win_prob = self._calculate_fallback_victory_probability(features)
    
            # Apply momentum boost
            momentum_factor = features.get('momentum', 0.0)
            momentum_boost = momentum_factor * 0.1 # Max 10% boost/penalty
    
            # Apply home advantage
            home_advantage = features.get('home_advantage', 0.0) * self.config.home_advantage_factor
    
            # Calculate final probability
            final_prob = win_prob + momentum_boost + home_advantage
    
            # Ensure probability is within valid range
            return max(0.05, min(0.95, final_prob))
    
        except Exception as e:
            self.logger.error(f" Victory probability calculation error: {e}")
            return 0.5
    
    def _calculate_fallback_victory_probability(self, features: Dict[str, float]) -> float:
        """
            Fallback victory probability calculation when model is not available
        """
        try:
            # Basic calculation based on key features
            win_rate = features.get('win_rate', 0.5)
            recent_form = features.get('recent_form', 0.5)
            offensive_rating = features.get('offensive_rating', 0.5)
            defensive_rating = 1.0 - features.get('defensive_rating', 0.5) # Lower is better for defense
    
            # Weighted average
            probability = (
                win_rate * 0.3 +
                recent_form * 0.25 +
                offensive_rating * 0.225 +
                defensive_rating * 0.225
            )
    
            return max(0.1, min(0.9, probability))
    
        except Exception as e:
            self.logger.error(f" Fallback calculation error: {e}")
            return 0.5

    def _extract_simplified_features(self, data: Dict[str, Any]) -> Dict[str, float]:
        """Simplified synchronous feature extraction for predict method"""
        try:
            features = {
                # Basic game features
                'home_win_pct': data.get('home_win_pct', 0.5),
                'away_win_pct': data.get('away_win_pct', 0.5),
                'home_offensive_rating': data.get('home_offensive_rating', 100.0),
                'away_offensive_rating': data.get('away_offensive_rating', 100.0),
                'home_defensive_rating': data.get('home_defensive_rating', 100.0),
                'away_defensive_rating': data.get('away_defensive_rating', 100.0),
                'pace': data.get('pace', 100.0),
                'total_score': data.get('total_score', 200.0),
                'spread': data.get('spread', 0.0),
                'home_advantage': 1.0, # Default home advantage
                'rest_differential': data.get('rest_differential', 0),
                'back_to_back': data.get('back_to_back', 0),
                'injuries': data.get('injuries', 0),
                'weather': data.get('weather', 0.5),
                'crowd_factor': data.get('crowd_factor', 0.5)
            }
            # Add some computed features
            features['offensive_differential'] = (
                features['home_offensive_rating'] - features['away_offensive_rating']
            ) / 100.0
            features['defensive_differential'] = (
                features['away_defensive_rating'] - features['home_defensive_rating']
            ) / 100.0
            features['win_pct_differential'] = features['home_win_pct'] - features['away_win_pct']
    
            return features
    
        except Exception as e:
            self.logger.error(f" MEDUSA ERROR: simplified feature extraction: {e}")
            return {
                'home_win_pct': 0.5, 'away_win_pct': 0.5, 'offensive_differential': 0.0,
                'defensive_differential': 0.0, 'win_pct_differential': 0.0
            }
    
    def predict(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
            Standard prediction method for compatibility with test frameworks
        """
        try:
            # Extract required parameters from data, providing defaults if missing
            home_team_id = data.get('home_team_id', 'unknown_home')
            away_team_id = data.get('away_team_id', 'unknown_away')
            game_date = data.get('game_date', datetime.now())
            home_stats = data.get('home_stats', {})
            away_stats = data.get('away_stats', {})
            context = data.get('context', None)
    
            # Extract victory-related features using proper arguments
            # Use asyncio.run for async compatibility
            try:
                features = asyncio.run(self._extract_all_features(
                    home_team_id, away_team_id, game_date, home_stats, away_stats, context
                ))
            except RuntimeError:
                # If already in async context, use simplified extraction
                features = self._extract_simplified_features(data)
    
            # Generate victory probability prediction
            victory_probability = self._calculate_victory_probability(features)
            momentum_factor = self._calculate_momentum_factor(features)
    
            prediction = {
                'prediction': victory_probability,
                'momentum': momentum_factor,
                'confidence': min(0.95, max(0.1, 1.0 - abs(victory_probability - 0.5))),
                'spire_type': 'nike_victory',
                'features_used': len(features)
            }
            confidence = prediction.get('confidence', 1.0)
            # --- Feedback wiring: send feedback if confidence is low ---
            if confidence < 0.3:
                feedback = FeatureFeedback(self.__class__.__name__, features, confidence, message="Low confidence. Requesting feature improvement.")
                self.feature_alchemist.receive_feedback(feedback)
            return prediction
        except Exception as e:
            self.logger.error(f" Prediction error: {e}")
            return {
                'prediction': 0.5,
                'momentum': 0.0,
                'confidence': 0.1,
                'spire_type': 'nike_victory',
                'error': str(e)
            }

    def _calculate_momentum_factor(self, features: Dict[str, float]) -> float:
        """
            Calculate the momentum factor based on extracted features
        """
        try:
            # Weight different momentum components
            momentum_weights = {
                'win_pct_differential': 0.20,
                'recent_form': 0.18,
                'offensive_differential': 0.15,
                'defensive_differential': 0.15,
                'home_win_pct': 0.10,
                'away_win_pct': -0.10, # Away team momentum (negative for home team)
                'rest_differential': 0.08,
                'injuries': -0.04, # Negative impact
                'back_to_back': -0.02 # Negative impact
            }
    
            # Calculate weighted momentum score
            momentum_score = 0.0
            total_weight = 0.0
    
            for feature, weight in momentum_weights.items():
                if feature in features:
                    momentum_score += features[feature] * weight
                    total_weight += abs(weight)
    
            # Normalize if we have features
            if total_weight > 0:
                momentum_score = momentum_score / total_weight
            else:
                momentum_score = 0.5 # Neutral momentum if no features
    
            # Apply momentum modifiers
            if 'crowd_factor' in features:
                momentum_score += features['crowd_factor'] * 0.05
    
            if 'weather' in features:
                momentum_score += (features['weather'] - 0.5) * 0.03
    
            # Recent performance boost
            if features.get('home_win_pct', 0.5) > 0.6:
                momentum_score += 0.02            # Ensure bounds [0, 1]
            momentum_score = max(0.0, min(1.0, momentum_score))
    
            return momentum_score
    
        except Exception as e:
            self.logger.error(f" Error calculating momentum factor: {e}")
            return 0.5 # Return neutral momentum on error

    def self_learn(self, feedback: Optional[Dict[str, Any]] = None):
        """
        Self-learning and feedback-driven adaptation for NikeVictoryOracle_Expert.
        Adjusts internal parameters or logic based on feedback from the War Council, Feature Alchemist, or other spires.
        """
        if feedback:
            logger.info(f"[NikeVictoryOracle_Expert] Received feedback: {feedback}")
            # Example: Adjust momentum threshold or weights based on feedback
            if 'momentum_threshold' in feedback:
                self.config.momentum_threshold = feedback['momentum_threshold']
            if 'confidence_threshold' in feedback:
                self.config.confidence_threshold = feedback['confidence_threshold']
            # Log feedback for meta-learning
            if not hasattr(self, 'feedback_log'):
                self.feedback_log = []
            self.feedback_log.append(feedback)
# ...existing code...

# Factory function for easy instantiation
def create_nike_expert(config: Optional[VictoryConfig] = None) -> 'NikeVictoryOracle_Expert':
    """Create an instance of NikeVictoryOracle_Expert"""
    return NikeVictoryOracle_Expert(config)


if __name__ == "__main__":
        # Example usage and testing
    
        async def test_nike_expert():
            """Test the NikeVictoryOracle_Expert"""
            oracle = NikeVictoryOracle_Expert()
        
            # Test data
            home_stats = {
                'offensive_rating': 115.2,
                'defensive_rating': 108.5,
                'pace': 101.3,
                'three_point_attempt_rate': 0.42,
                'days_rest': 2
            }
        
            away_stats = {
                'offensive_rating': 110.8,
                'defensive_rating': 112.1,
                'pace': 98.7,
                'three_point_attempt_rate': 0.38,
                'days_rest': 1
            }
        
            context = MomentumContext(
                recent_win_streak=3,
                last_5_record=(4, 1),
                scoring_trend=5.2,
                revenge_game=True
            )
        
            # Test victory prediction
            metrics = await oracle.predict_victory(
                home_team_id="1610612744", # GSW
                away_team_id="1610612747", # LAL
                game_date=datetime(2024, 1, 17),
                home_stats=home_stats,
                away_stats=away_stats,
                context=context
            )
        
        
            # Test live momentum
            recent_plays = [
                {'type': 'made_shot', 'team': 'home', 'points': 3},
                {'type': 'steal', 'team': 'home'},
                {'type': 'made_shot', 'team': 'home', 'points': 2},
                {'type': 'turnover', 'team': 'away'},
                {'type': 'made_shot', 'team': 'home', 'points': 2}
            ]
        
            live_momentum = await oracle.analyze_live_momentum(
                home_team_id="1610612744",
                away_team_id="1610612747",
                current_score=(95, 88),
                time_remaining=420,
                recent_plays=recent_plays
            )
        
            for metric, value in live_momentum.items():
                print(f"{metric}: {value}")
    
        # Run the test
        asyncio.run(test_nike_expert())
