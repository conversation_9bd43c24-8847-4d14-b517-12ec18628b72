import logging
from sqlalchemy import create_engine, MetaData
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
from vault_oracle.core.config.config_loader import config_loader
from .models import Base
# from .crud import crud_operations  # Commented out for now

"""
Database Package
===============

Database models and CRUD operations for NBA data.
"""


logger = logging.getLogger("🏀 DATABASE")

# Global database variables
engine = None
SessionLocal = None


async def init_db():
    """Initialize database connection and create tables."""
    global engine, SessionLocal
    
    try:
        config = config_loader.load_config()
        # Use SQLite by default, can be extended for other DBs if TOML is updated
        database_url = f"sqlite:///{config['vault_paths']['ORACLE_MEMORY']}"
        logger.info(f"🗃️ Initializing database: {database_url}")
        
        # Create engine
        if database_url.startswith("sqlite"):
            engine = create_engine(
                database_url,
                connect_args={"check_same_thread": False},
                poolclass=StaticPool,
                echo=config['system_config'].get('debug', False)
            )
        else:
            # If you add a [database] section to TOML, update these keys accordingly
            engine = create_engine(
                database_url,
                pool_size=config['system_config'].get('database_pool_size', 5),
                max_overflow=config['system_config'].get('database_max_overflow', 10),
                echo=config['system_config'].get('debug', False)
            )
        
        # Create session maker
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        # Import and create tables
        Base.metadata.create_all(bind=engine)
        
        logger.info("✅ Database initialized successfully")
        
    except Exception as e:
        logger.error(f"❌ Database initialization failed: {e}")
        raise


def get_db():
    """Get database session."""
    if SessionLocal is None:
        raise RuntimeError("Database not initialized. Call init_db() first.")
    
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


# Removed orphaned import fragments - these should be imported properly elsewhere

__all__ = [
 # CRUD classes
 "GameCRUD",
 "PlayerCRUD",
 "PredictionCRUD",
 "ExpertCRUDManager",
 
 # Pydantic models
 "Game",
 "Player",
 "Prediction",
 
 # Core managers
 "AsyncDatabaseManager",
 "CacheManager", 
 "ExpertDatabaseService",
 
 # Dependencies
 "get_database_dependency",
 "get_authenticated_database_service",
 "get_expert_database_service",
 "get_vault_database_service",
 "get_db_service",
 
 # Database operations
 "initialize_database",
 "cleanup_database",
 
 # Authentication-aware functions
 "get_today_games_authenticated",
 "get_user_recent_predictions_authenticated",
 "get_game_predictions_summary_authenticated",
 
 # Legacy functions
 "get_today_games",
 "get_user_recent_predictions"
]
