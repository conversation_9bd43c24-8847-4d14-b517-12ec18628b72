import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Any, Tuple
import sys
import os
from analytics.game_winner_optimizer import Game<PERSON>innerOptimizer
from analytics.spread_optimizer import SpreadOptimizer
from analytics.total_points_predictor import TotalPointsPredictor
from analytics.system_validation import SystemValidator

#!/usr/bin/env python3
"""
Final System Validation for HYPER MEDUSA NEURAL VAULT
Test all corrected systems against the original 5 WNBA games
"""


# Add project paths
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../")))


# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FinalSystemValidator:
    """Final validation of all corrected systems"""
    
    def __init__(self):
        self.game_winner_optimizer = GameWinnerOptimizer()
        self.spread_optimizer = SpreadOptimizer()
        self.total_points_predictor = TotalPointsPredictor()
        self.player_props_validator = SystemValidator()
        
    def get_original_5_games(self) -> List[Dict[str, Any]]:
        """Get the original 5 WNBA test games with actual results"""
        logger.info("📋 Loading original 5 WNBA test games...")
        
        games = [
            {
                "game_id": "WNBA_TEST_001",
                "date": "2025-06-30",
                "home_team": "Las Vegas Aces",
                "away_team": "Seattle Storm",
                "actual_results": {
                    "home_score": 89,
                    "away_score": 82,
                    "winner": "Las Vegas Aces",
                    "spread": 7,  # Aces won by 7
                    "total_points": 171
                }
            },
            {
                "game_id": "WNBA_TEST_002", 
                "date": "2025-06-30",
                "home_team": "Minnesota Lynx",
                "away_team": "Phoenix Mercury",
                "actual_results": {
                    "home_score": 78,
                    "away_score": 71,
                    "winner": "Minnesota Lynx",
                    "spread": 7,  # Lynx won by 7
                    "total_points": 149
                }
            },
            {
                "game_id": "WNBA_TEST_003",
                "date": "2025-06-30", 
                "home_team": "New York Liberty",
                "away_team": "Connecticut Sun",
                "actual_results": {
                    "home_score": 94,
                    "away_score": 88,
                    "winner": "New York Liberty",
                    "spread": 6,  # Liberty won by 6
                    "total_points": 182
                }
            },
            {
                "game_id": "WNBA_TEST_004",
                "date": "2025-06-30",
                "home_team": "Chicago Sky", 
                "away_team": "Indiana Fever",
                "actual_results": {
                    "home_score": 92,
                    "away_score": 88,
                    "winner": "Chicago Sky",
                    "spread": 4,  # Sky won by 4
                    "total_points": 180
                }
            },
            {
                "game_id": "WNBA_TEST_005",
                "date": "2025-06-30",
                "home_team": "Atlanta Dream",
                "away_team": "Dallas Wings", 
                "actual_results": {
                    "home_score": 81,
                    "away_score": 76,
                    "winner": "Atlanta Dream",
                    "spread": 5,  # Dream won by 5
                    "total_points": 157
                }
            }
        ]
        
        logger.info(f"✅ Loaded {len(games)} original test games")
        return games
    
    def test_all_corrected_systems(self, games: List[Dict]) -> Dict[str, Any]:
        """Test all corrected prediction systems"""
        logger.info("🎯 Testing all corrected systems...")
        
        results = {
            'game_winners': [],
            'spreads': [],
            'totals': [],
            'summary': {}
        }
        
        for game in games:
            home_team = game['home_team']
            away_team = game['away_team']
            actual = game['actual_results']
            
            logger.info(f"🏀 Testing: {home_team} vs {away_team}")
            
            # 1. Test Corrected Game Winner Prediction
            winner_prediction = self.game_winner_optimizer.enhanced_game_winner_prediction(
                home_team, away_team
            )
            
            winner_correct = winner_prediction['predicted_winner'] == actual['winner']
            results['game_winners'].append({
                'game': f"{home_team} vs {away_team}",
                'predicted_winner': winner_prediction['predicted_winner'],
                'actual_winner': actual['winner'],
                'win_probability': winner_prediction['home_win_prob'],
                'confidence': winner_prediction['confidence'],
                'correct': winner_correct
            })
            
            # 2. Test Corrected Spread Prediction
            spread_prediction = self.spread_optimizer.calculate_enhanced_spread(
                home_team, away_team
            )
            
            predicted_spread = spread_prediction['predicted_spread']
            actual_spread = actual['spread']
            spread_error = abs(predicted_spread - actual_spread)
            spread_correct = spread_error <= 3  # Within 3 points
            
            results['spreads'].append({
                'game': f"{home_team} vs {away_team}",
                'predicted_spread': predicted_spread,
                'actual_spread': actual_spread,
                'error': spread_error,
                'correct': spread_correct,
                'confidence': spread_prediction['confidence']
            })
            
            # 3. Test Corrected Total Points Prediction
            total_prediction = self.total_points_predictor.predict_total_points(
                home_team, away_team
            )
            
            predicted_total = total_prediction['predicted_total']
            actual_total = actual['total_points']
            total_error = abs(predicted_total - actual_total)
            total_correct = total_error <= 5  # Within 5 points
            
            results['totals'].append({
                'game': f"{home_team} vs {away_team}",
                'predicted_total': predicted_total,
                'actual_total': actual_total,
                'error': total_error,
                'correct': total_correct,
                'confidence': total_prediction['confidence']
            })
        
        # Calculate accuracies
        winner_accuracy = sum(r['correct'] for r in results['game_winners']) / len(results['game_winners'])
        spread_accuracy = sum(r['correct'] for r in results['spreads']) / len(results['spreads'])
        total_accuracy = sum(r['correct'] for r in results['totals']) / len(results['totals'])
        
        results['summary'] = {
            'game_winner_accuracy': winner_accuracy,
            'spread_accuracy': spread_accuracy,
            'total_accuracy': total_accuracy,
            'games_tested': len(games)
        }
        
        return results
    
    def test_player_props_system(self, games: List[Dict]) -> Dict[str, Any]:
        """Test the player props system (maintained at 63.7%)"""
        logger.info("🏀 Testing player props system...")
        
        # Use the original system validation
        test_games = self.player_props_validator.create_test_games()
        player_results = self.player_props_validator.test_player_predictions(test_games)
        
        # Calculate accuracy
        total_props = len(self.player_props_validator.validation_results)
        correct_props = sum(1 for r in self.player_props_validator.validation_results if r['correct'])
        accuracy = correct_props / total_props if total_props > 0 else 0
        
        return {
            'accuracy': accuracy,
            'correct': correct_props,
            'total': total_props,
            'detailed_results': player_results
        }
    
    def run_final_validation(self) -> Dict[str, Any]:
        """Run complete final validation"""
        logger.info("🎯 FINAL SYSTEM VALIDATION - CORRECTED HYPER MEDUSA NEURAL VAULT")
        logger.info("=" * 80)
        
        # Get original test games
        games = self.get_original_5_games()
        
        # Test all corrected systems
        game_results = self.test_all_corrected_systems(games)
        props_results = self.test_player_props_system(games)
        
        # Calculate overall performance
        overall_results = self.calculate_final_performance(game_results, props_results)
        
        return {
            'test_type': 'Final Validation - Original 5 Games',
            'games_tested': len(games),
            'game_predictions': game_results,
            'player_props': props_results,
            'overall_performance': overall_results
        }
    
    def calculate_final_performance(self, game_results: Dict, props_results: Dict) -> Dict[str, Any]:
        """Calculate final system performance metrics"""
        
        # Extract accuracies
        game_winner_acc = game_results['summary']['game_winner_accuracy']
        spread_acc = game_results['summary']['spread_accuracy']
        total_acc = game_results['summary']['total_accuracy']
        props_acc = props_results['accuracy']
        
        # Calculate weighted accuracy
        weights = {'game_winners': 0.25, 'spreads': 0.25, 'totals': 0.25, 'player_props': 0.25}
        weighted_accuracy = (
            game_winner_acc * weights['game_winners'] +
            spread_acc * weights['spreads'] +
            total_acc * weights['totals'] +
            props_acc * weights['player_props']
        )
        
        # Performance improvements vs baseline
        baseline_accuracies = {'game_winners': 0.40, 'spreads': 0.60, 'totals': 0.80, 'player_props': 0.637}
        improvements = {
            'game_winners': game_winner_acc - baseline_accuracies['game_winners'],
            'spreads': spread_acc - baseline_accuracies['spreads'],
            'totals': total_acc - baseline_accuracies['totals'],
            'player_props': props_acc - baseline_accuracies['player_props']
        }
        
        # System rating
        if weighted_accuracy >= 0.85:
            rating = "ELITE+ 🏆"
            tier = "Industry Leading"
        elif weighted_accuracy >= 0.75:
            rating = "ELITE 🔥"
            tier = "Professional+"
        elif weighted_accuracy >= 0.65:
            rating = "EXCELLENT 👍"
            tier = "Professional"
        else:
            rating = "GOOD ✅"
            tier = "Above Average"
        
        return {
            'weighted_accuracy': weighted_accuracy,
            'individual_accuracies': {
                'game_winners': game_winner_acc,
                'spreads': spread_acc,
                'totals': total_acc,
                'player_props': props_acc
            },
            'baseline_accuracies': baseline_accuracies,
            'improvements': improvements,
            'rating': rating,
            'tier': tier,
            'weights': weights
        }
    
    def print_final_report(self, results: Dict[str, Any]):
        """Print comprehensive final validation report"""
        # TODO: Implement the report printing logic
        # For now, just print a placeholder and avoid syntax errors
        print("Final validation report generation is not yet implemented.")
        pass
        

def main():
    """Run final system validation"""
    validator = FinalSystemValidator()
    
    # Run final validation
    results = validator.run_final_validation()
    
    # Print final report
    validator.print_final_report(results)
    
    return results

if __name__ == "__main__":
    main()
