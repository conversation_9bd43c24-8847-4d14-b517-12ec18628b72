# src/database/hyper_medusa_db_bootstrap.py

#!/usr/bin/env python3
"""
HYPER_MEDUSA_DB_BOOTSTRAP.py
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
Enhanced database bootstrap system for the NBA/WNBA Betting & Prediction App.

Manages the initialization and setup of the database, including applying
the schema, seeding reference data, running migrations, and verifying integrity.

Features:
- Atomic transactions with rollback capabilities
- Bulk insert operations
- Data validation
- Retry mechanisms
- Progress tracking
- Dry-run mode
- Backup/restore functionality (placeholder)
- Migration application (placeholder)
"""


import sys # Standard library for system-specific parameters and functions
import os # Standard library for interacting with the operating system
import json # Standard library for working with JSON data
import logging # Standard library for logging
import argparse # Standard library for parsing command-line arguments
from pathlib import Path # Standard library for working with file paths
from typing import Dict, List, Optional, Any # Standard type hints
from functools import wraps # Standard library for decorators
import time
from src.mnemosyne_archive.mnemosyne_archive_keeper import MnemosyneArchive<PERSON>eeper
from vault_oracle.core.ichor_vitality import ExpertQuantumEntanglementManager
from vault_oracle.core.temporal_flux import TemporalFluxStabilizer
import toml
from vault_oracle.core.vault_manifest import VaultIdentity
from vault_oracle.core.oracle_focus import oracle_focus
from sqlalchemy import text, create_engine
import tqdm
import logging


# Note: Redundant sys.path manipulation removed as it's handled by project structure/entry point


logger = logging.getLogger(__name__)

# --- Enforce Real Production Dependencies ---
try:

    logger.info(
        "Successfully imported all production dependencies for DB bootstrap."
    )
except ImportError as e:
    logger.critical(
        f"TITAN PROCESSING FAILED: import a required production dependency: {e}. Production build requires all real classes and libraries."
    )
    raise


# Configure logger for this module
if not logger.handlers:

    handler = logging.StreamHandler(sys.stdout)
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )
    handler.setFormatter(handler)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)

# --- Create dummy placeholder files for testing if they don't exist ---
# This is just for the standalone __main__ example to run without errors
# It uses the MockVaultIdentity for path info
if __name__ == "__main__":
    dummy_schema_content = """
-- Dummy Schema for Bootstrapper Testing
PRAGMA foreign_keys = ON;
CREATE TABLE IF NOT EXISTS olympian_squads (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL,
    abbreviation TEXT NOT NULL UNIQUE,
    league TEXT NOT NULL
);
CREATE TABLE IF NOT EXISTS mortal_heroes (
    id INTEGER PRIMARY KEY,
    full_name TEXT NOT NULL,
    olympian_squad_id INTEGER,
    FOREIGN KEY (olympian_squad_id) REFERENCES olympian_squads(id)
);
"""
    dummy_ref_data = {
        "OlympianPantheon": [ # Note: Schema uses olympian_squads, ref data uses OlympianPantheon - need to map or reconcile
            {"mythic_roster_id": 1, "name": "Team A", "abbreviation": "TA", "league": "NBA"},
            {"mythic_roster_id": 2, "name": "Team B", "abbreviation": "TB", "league": "NBA"},
        ],
        "players": [ # Note: Schema uses mortal_heroes, ref data uses players - need to map or reconcile
            {"hero_id": 101, "full_name": "Player One", "mythic_roster_id": 1},
            {"hero_id": 102, "full_name": "Player Two", "mythic_roster_id": 2},
        ],
    }

    # Use the mock VaultIdentity to get paths for dummy files
    schema_path_str = getattr(VaultIdentity, "schema_path", "./schema.sql")
    ref_data_path_str = getattr(
        VaultIdentity, "reference_data_path", "./reference_data.json"
    )

    schema_path = Path(schema_path_str)
    ref_data_path = Path(ref_data_path_str)

    # Ensure parent directories exist for the dummy files
    schema_path.parent.mkdir(parents=True, exist_ok=True)
    ref_data_path.parent.mkdir(parents=True, exist_ok=True)

    if not schema_path.exists():
        try:
            schema_path.write_text(dummy_schema_content)
        except Exception as e:
            logger.error(f"Error creating dummy schema file {schema_path}: {e}")

    if not ref_data_path.exists():
        try:
            ref_data_path.write_text(json.dumps(dummy_ref_data, indent=2))
        except Exception as e:
            logger.error(
                f"Error creating dummy reference data file {ref_data_path}: {e}"
            )

# --------------------------------------------------------------------


# === Constants ===
# Can override these with config values from VaultIdentity if needed
DEFAULT_CHUNK_SIZE = 500
MAX_RETRIES = 3
RETRY_DELAY = 1 # Seconds


# === Decorators ===
def database_retry(func):
    """Decorator to retry database operations."""

    @wraps(func)
    def wrapper(*args, **kwargs):
        # Use retry settings from config if available, otherwise defaults
        # Assuming VaultIdentity has chronos_patience.db_retry_delay etc.
        # Example: MAX_RETRIES_CFG = getattr(VaultIdentity, 'chronos_patience.db_max_retries', MAX_RETRIES)
        # RETRY_DELAY_CFG = getattr(VaultIdentity, 'chronos_patience.db_retry_delay', RETRY_DELAY)
        MAX_RETRIES_CFG = MAX_RETRIES # Using constants for now
        RETRY_DELAY_CFG = RETRY_DELAY # Using constants for now

        for attempt in range(MAX_RETRIES_CFG):
            try:
                logger.info(
                    f"Attempt {attempt + 1}/{MAX_RETRIES_CFG} for {func.__name__}."
                )
                return func(*args, **kwargs)
            except Exception as e:
                logger.warning(
                    f"Attempt {attempt + 1}/{MAX_RETRIES_CFG} failed for {func.__name__}: {str(e)}"
                )
                if attempt == MAX_RETRIES_CFG - 1:
                    logger.error(
                        f"All {MAX_RETRIES_CFG} attempts failed for {func.__name__}. Giving up.",
                        exc_info=True,
                    )
                    raise # Re-raise the exception after exhausting retries
                else:
                    logger.info(
                        f"Retrying {func.__name__} after {RETRY_DELAY_CFG} seconds..."
                    )
                time.sleep(RETRY_DELAY_CFG)
        # This part should ideally not be reached
        logger.critical(
            f"Wrapper finished without returning or raising for {func.__name__}. This indicates a logic error."
        )
        raise RuntimeError(
            f"Retry decorator failed for {func.__name__}."
        ) # Ensure something is returned or raised

    return wrapper


# === Core Functions ===
class DatabaseBootstrapper:
    """
    Manages the database bootstrap process for the Hyper Medusa Neural Vault,
    including schema application, data seeding, and integrity verification.
    """

    @oracle_focus # Apply decorator if available
    def __init__(
        self, db, dry_run: bool = False
    ): # Removed type hint to avoid compile error
        """
        Initializes the DatabaseBootstrapper.

        Args:
        db: An instance of the MnemosyneArchiveKeeper (or a compatible class).
        dry_run: If True, simulate database operations without making actual database changes.
        """
        # Ensure the provided db object is an instance of the expected keeper or its mock
        if not isinstance(db, (MnemosyneArchiveKeeper,)):
            logger.error(
                f"Invalid database keeper instance provided. Expected MnemosyneArchiveKeeper, got {type(db)}."
            )
            raise TypeError("Invalid database keeper instance provided.")

        self.db = db # Store the database keeper instance
        self.dry_run = dry_run
        logger.info(f"DatabaseBootstrapper initialized. Dry run: {self.dry_run}")

        # Note: _setup_initial_state might need to happen outside the transaction
        # depending on the specific database and PRAGMA behavior.
        # Calling it here ensures it's done early.
        self._setup_initial_state()

    @oracle_focus # Apply decorator if available
    def _setup_initial_state(self):
        """
        Initialize database settings like foreign keys.

        Note: Some settings (like PRAGMA in SQLite) may need to be executed
        outside of a transaction or at the connection/engine level.
        """
        logger.info("MEDUSA VAULT: Setting up initial database state...")
        if self.dry_run:
            logger.info("MEDUSA VAULT: [Dry Run] Would execute initial database setup commands.")
            return

        try:
            # Example: Ensure foreign keys are off initially (SQLite specific PRAGMA)
            # This might need to be executed on the connection directly, not in a transaction.
            # Assuming self.db.execute is appropriate for this.
            if self.db.connection and hasattr(
                self.db.connection, "execute"
            ): # Check if connection exists and supports execute
                # Note: SQLAlchemy's text() is recommended for raw SQL
                self.db.connection.execute(
                    text("PRAGMA foreign_keys = OFF;")
                ) # Use connection's execute if available and suitable
            else:
                logger.warning(
                    "Database keeper connection does not support direct execute for PRAGMA. Skipping initial setup."
                )

            # Other initial setup might go here (e.g., setting search_path for PostgreSQL)

        except Exception as e:
            # Log the error but don't necessarily fail bootstrap here, as some PRAGMAs are backend-specific
            logger.warning(f"TITAN PROCESSING FAILED: set initial database state: {e}")

    # This method is intended to wrap a series of queries in a transaction.
    # The MnemosyneArchiveKeeper's inscribe_scroll already handles transactions and retries
    # for individual queries. This method might be redundant or need to orchestrate
    # multiple inscribe_scroll calls within a larger external transaction context
    # provided by the keeper if needed.
    # However, the apply_schema method calls this with multiple queries split by ';'.
    # Let's keep it as is, assuming it manages a batch of schema statements
    # before potentially calling MnemosyneArchiveKeeper's execute or inscribe_scroll.
    # The current implementation uses self.db.execute inside the transaction,
    # which might not be the intended pattern if self.db.inscribe_scroll should be used.
    # Let's adjust it to use self.db.execute as defined in the keeper's mock/actual code.
    @database_retry # Apply retry decorator
    @oracle_focus # Apply decorator if available
    def _execute_in_transaction(self, queries: List[str]):
        """
        Execute multiple raw SQL queries in a single transaction with retry.

        Suitable for schema application statements. Uses the database keeper's
        execute_raw_sql method for each query.
        """
        if self.dry_run:
            logger.info(
                f"[Dry Run] Would execute {len(queries)} queries in a transaction."
            )
            return
        # Patch: Use execute_raw_sql for each query
        for i, query in enumerate(queries):
            try:
                logger.info(
                    f"Applying schema query {i+1}/{len(queries)}: {query.strip()[:100]}..."
                )
                self.db.execute_raw_sql(query, commit=True)
            except Exception as e:
                logger.error(f"TITAN PROCESSING FAILED: execute schema query {i+1}: {e}")
                raise

    @oracle_focus # Apply decorator if available
    def _backup_existing_data(self):
        """
        Create temporary backup of critical tables before destructive operations.

        This is a placeholder method. Actual implementation would involve
        reading data from the database using the keeper and storing it
        temporarily (e.g., to files or memory).
        """
        # Check if backups are enabled via VaultIdentity config
        # Access nested config using .get() or direct attribute access if VaultIdentity is a Pydantic model
        # Assuming VaultIdentity is a dictionary-like object or Pydantic model where mnemosyne_core.enable_backups exists
        enable_backups_cfg = getattr(
            VaultIdentity, "mnemosyne_core.enable_backups", True
        )
        if not enable_backups_cfg:
            logger.info("MEDUSA VAULT: Backup functionality disabled by configuration.")
            return

        backup_tables = [
            "olympian_squads",
            "mortal_heroes",
            "heroic_odysseys",
        ] # Use schema table names
        logger.info("MEDUSA VAULT: 🔰 Creating temporary backup (placeholder)...")

        if self.dry_run:
            logger.info(f"[Dry Run] Would backup tables: {backup_tables}")
            return

        # --- Placeholder Implementation ---
        # In a real implementation:
        # - Iterate over backup_tables.
        # - For each table, use self.db to query all data (e.g., self.db.consult_oracle(f"SELECT * FROM {table}")).
        # - Save the retrieved data (e.g., as JSON, CSV, or pickle) to a temporary location.
        # - Ensure rollback/cleanup mechanisms for the backup files in case of bootstrap failure.
        logger.warning("TITAN WARNING: Backup implementation is a placeholder.")
        for table in backup_tables:
            logger.info(
                f"Attempting to backup data from table '{table}' (placeholder)..."
            )
            # Example: data = self.db.consult_oracle(f"SELECT * FROM {table}")
            # Example: Save data to a file...

        # -----------------------------------
        logger.info("MEDUSA VAULT: Temporary backup creation completed (placeholder).")

    @oracle_focus # Apply decorator if available
    def apply_schema(self):
        """
        Apply the database schema from an SQL file with transactional management.

        Reads the schema SQL file, splits it into individual commands, and
        executes them within a single transaction.
        """
        # Prefer schema_path from db_config if available, else fallback to VaultIdentity
        schema_path_str = getattr(self, "db", None)
        if schema_path_str and hasattr(self.db, "config"):
            schema_path_str = self.db.config.get("schema_path")
        else:
            schema_path_str = None
        if not schema_path_str:
            # Fallback to VaultIdentity.vault_paths.SCHEMA
            vault_paths = getattr(VaultIdentity, "vault_paths", None)
            if vault_paths:
                if hasattr(vault_paths, "SCHEMA"):
                    schema_path_str = getattr(vault_paths, "SCHEMA", None)
                elif isinstance(vault_paths, dict):
                    schema_path_str = vault_paths.get("SCHEMA")
        if not schema_path_str:
            logger.error(
                "Schema path not found in VaultIdentity configuration (vault_paths.SCHEMA) or db_config."
            )
            raise ValueError("Database schema path is not configured.")
        schema_path = Path(schema_path_str)
        if not schema_path.exists():
            logger.error(f"Schema file not found at {schema_path.resolve()}.")
            raise FileNotFoundError(
                f"Database schema file not found: {schema_path.resolve()}"
            )
        logger.info(f"📖 Applying schema from {schema_path.resolve()}...")

        try:
            # Read the entire SQL file
            with schema_path.open(
                "r", encoding="utf-8"
            ) as f: # Specify encoding for safety
                sql_script = f.read()

            # Simple split by semicolon to get individual commands
            # This might need more sophisticated parsing for complex SQL with semicolons inside strings or comments
            sql_commands = sql_script.split(";")
            logger.info(
                f"Split schema script into {len(sql_commands)} potential commands."
            )

            # Filter out empty commands resulting from trailing semicolons or blank lines
            executable_commands = [cmd.strip() for cmd in sql_commands if cmd.strip()]
            logger.info(
                f"Identified {len(executable_commands)} executable SQL commands after stripping."
            )

            if not executable_commands:
                logger.warning("TITAN WARNING: No executable SQL commands found in the schema file.")
                return

            # Execute the schema commands within a transaction
            self._execute_in_transaction(executable_commands)

            logger.info("MEDUSA VAULT: Database schema applied successfully.")
        except Exception as e:
            logger.error(
                f"TITAN PROCESSING FAILED: apply database schema from {schema_path.resolve()}: {e}",
                exc_info=True,
            )
            raise # Re-raise the exception

    @oracle_focus # Apply decorator if available
    def validate_team_data(self, team: Dict[str, Any]) -> bool:
        """
        Validate the structure and required fields of team data records from reference data.

        Compares against the expected schema for the 'olympian_squads' table.

        Args:
        team: A dictionary representing a single team record from reference data.

        Returns:
        True if the team data is valid, False otherwise.
        """
        # Based on schema.sql: olympian_squads has id, epithet, realm, elemental_alignment, chronicle, UNIQUE(realm, epithet)
        # Reference data has: mythic_roster_id, name, abbreviation, league
        # There's a naming inconsistency here (mythic_roster_id vs id, name vs epithet, abbreviation missing in schema, league missing in schema)
        # Assuming the reference data keys map to schema columns as follows:
        # 'mythic_roster_id' -> 'id'
        # 'name' -> 'epithet'
        # 'abbreviation' is extra in ref data but not schema - might be okay or ignored
        # 'league' is extra in ref data but not schema - might be okay or ignored
        # Required schema columns are 'id', 'epithet', 'realm', 'elemental_alignment'.
        # The validation should check if the *reference data keys* needed to populate *schema columns* are present and valid.

        # For now, let's validate based on the keys present in the dummy reference data,
        # but note the mismatch with the schema's required columns if doing a strict schema validation.
        # Required keys present in the dummy reference data:
        required_ref_keys = [
            "mythic_roster_id",
            "name",
        ] # These likely map to schema's 'id' and 'epithet'

        is_valid = (
            all(key in team for key in required_ref_keys)
            and team.get("mythic_roster_id") is not None
        ) # Check for presence and non-None id
        if not is_valid:
            logger.warning(
                f"Invalid team data found: Missing required reference keys ({required_ref_keys}) or missing ID. Data: {team}"
            )
            return False

        # Optional: Add checks for 'realm' and 'elemental_alignment' if they were expected in the ref data
        # if 'realm' not in team or 'elemental_alignment' not in team:
        # logger.warning(f"Team data missing schema required keys 'realm' or 'elemental_alignment'. Data: {team}")
        # # Decide if this makes the data invalid or just incomplete

        # Optional: Add type checks based on schema.sql
        # if not isinstance(team.get('mythic_roster_id'), int): logger.warning("TITAN WARNING: mythic_roster_id not int"); is_valid = False
        # if not isinstance(team.get('name'), str): logger.warning("TITAN WARNING: name not str"); is_valid = False

        return is_valid # Return true if all checks pass

    @oracle_focus # Apply decorator if available
    def validate_player_data(self, player: Dict[str, Any]) -> bool:
        """
        Validate the structure and required fields of player data records from reference data.

        Compares against the expected schema for the 'mortal_heroes' table.

        Args:
        player: A dictionary representing a single player record from reference data.

        Returns:
        True if the player data is valid, False otherwise.
        """
        # Based on schema.sql: mortal_heroes has id, olympian_squad_id, heroic_epithet, divine_spark, ichor_level, fate_boundary
        # Reference data has: hero_id, full_name, mythic_roster_id
        # Naming inconsistency: hero_id vs id, full_name vs heroic_epithet, mythic_roster_id vs olympian_squad_id.
        # Required schema columns: 'id', 'olympian_squad_id', 'heroic_epithet'. Others have defaults or can be null.
        # Validate based on reference data keys needed to populate schema columns.

        # Required keys in the REFERENCE DATA for the 'mortal_heroes' (Players) table based on schema:
        # 'id' (mapped from 'hero_id' in ref data), 'olympian_squad_id' (mapped from 'mythic_roster_id' in ref data),
        # 'heroic_epithet' (mapped from 'full_name' in ref data).

        # Required keys present in the dummy reference data:
        required_ref_keys = [
            "hero_id",
            "full_name",
        ] # 'mythic_roster_id' is optional in schema (ON DELETE SET NULL)

        is_valid = (
            all(key in player for key in required_ref_keys)
            and player.get("hero_id") is not None
        ) # Check for presence and non-None id
        if not is_valid:
            logger.warning(
                f"Invalid player data found: Missing required reference keys ({required_ref_keys}) or missing ID. Data: {player}"
            )
            return False

        # Optional: Add type checks based on schema.sql
        # if not isinstance(player.get('hero_id'), int): logger.warning("TITAN WARNING: hero_id not int"); is_valid = False
        # if not isinstance(player.get('full_name'), str): logger.warning("TITAN WARNING: full_name not str"); is_valid = False
        # if player.get('mythic_roster_id') is not None and not isinstance(player.get('mythic_roster_id'), int): logger.warning("TITAN WARNING: mythic_roster_id not int or None"); is_valid = False

        return is_valid # Return true if all checks pass

    # The bulk_insert method in the Bootstrapper now acts as an orchestrator,
    # calling the bulk_insert method on the provided database keeper instance.
    # This method includes progress tracking and retry logic applied per chunk.
    @database_retry # Apply retry decorator per chunk
    @oracle_focus # Apply decorator if available
    def bulk_insert(
        self,
        table: str,
        records: List[Dict[str, Any]],
        chunk_size: Optional[int] = None,
    ): # Made chunk_size optional
        """
        Orchestrate bulk insert operation for a list of records into a specified table.

        Splits records into chunks and calls the underlying database keeper's
        bulk_insert or insert_batch_or_ignore method for each chunk. Includes progress tracking and retry
        mechanism at the chunk level.

        Args:
        table: The name of the target table (schema name, e.g., 'olympian_squads').
        records: A list of dictionaries, where each dictionary is a record
        formatted according to the target table's schema.
        chunk_size: The number of records to insert in each batch. If None,
        uses the default chunk size from configuration (VaultIdentity)
        or a hardcoded default.

        Raises:
        Exception: If a bulk insert chunk fails after all retries.
        """
        if not records:
            logger.info(f"No records to insert into {table}.")
            return

        total_records = len(records)
        config_chunk_size_key = f"mnemosyne_core.{table}_chunk_size"
        effective_chunk_size = (
            chunk_size
            if chunk_size is not None
            else getattr(
                VaultIdentity,
                config_chunk_size_key,
                getattr(
                    VaultIdentity,
                    "mnemosyne_core.default_chunk_size",
                    DEFAULT_CHUNK_SIZE,
                ),
            )
        )

        if effective_chunk_size <= 0:
            logger.warning(
                f"Invalid effective chunk size ({effective_chunk_size}). Using default {DEFAULT_CHUNK_SIZE}."
            )
            effective_chunk_size = DEFAULT_CHUNK_SIZE

        logger.info(
            f"🌱 Seeding {total_records} records into '{table}' in chunks of {effective_chunk_size}..."
        )

        chunks = [
            records[i : i + effective_chunk_size]
            for i in range(0, total_records, effective_chunk_size)
        ]

        # Prefer bulk_insert, fallback to insert_batch_or_ignore
        insert_method = None
        if hasattr(self.db, "bulk_insert"):
            insert_method = self.db.bulk_insert
        elif hasattr(self.db, "insert_batch_or_ignore"):
            insert_method = self.db.insert_batch_or_ignore
        else:
            logger.error(
                "Database keeper instance does not have a 'bulk_insert' or 'insert_batch_or_ignore' method."
            )
            raise NotImplementedError(
                "Database keeper does not support bulk or batch inserts."
            )

        try:

            with tqdm.tqdm(
                chunks,
                desc=f"Inserting '{table}'",
                unit="records",
                total=len(chunks),
                leave=True,
            ) as progress_bar:
                for chunk in progress_bar:
                    insert_method(table, chunk)
                    logger.info(
                        f"Successfully processed a chunk of {len(chunk)} records into '{table}'."
                    )
        except Exception as e:
            logger.error(
                f"Bulk insert failed for a chunk into '{table}': {e}", exc_info=True
            )
            raise

    @oracle_focus # Apply decorator if available
    def seed_reference_data(self):
        """
        Load reference data from a JSON file, validate it, and seed it into
        the appropriate database tables using bulk inserts.

        Handles mapping between reference data keys and schema column names.
        """
        # Prefer reference_data_path from db_config if available, else fallback to VaultIdentity
        ref_data_path_str = getattr(self, "db", None)
        if ref_data_path_str and hasattr(self.db, "config"):
            ref_data_path_str = self.db.config.get("reference_data_path")
        else:
            ref_data_path_str = None
        if not ref_data_path_str:
            vault_paths = getattr(VaultIdentity, "vault_paths", None)
            if vault_paths:
                if hasattr(vault_paths, "REFERENCE_DATA"):
                    ref_data_path_str = getattr(vault_paths, "REFERENCE_DATA", None)
                elif isinstance(vault_paths, dict):
                    ref_data_path_str = vault_paths.get("REFERENCE_DATA")
        if not ref_data_path_str:
            logger.error(
                "Reference data path not found in VaultIdentity configuration (vault_paths.REFERENCE_DATA) or db_config."
            )
            raise ValueError("Reference data path is not configured.")
        ref_data_path = Path(ref_data_path_str)
        if not ref_data_path.exists():
            logger.error(f"Reference data file not found at {ref_data_path.resolve()}.")
            raise FileNotFoundError(
                f"Reference data file not found: {ref_data_path.resolve()}"
            )
        logger.info(f"🧬 Seeding reference data from {ref_data_path.resolve()}...")

        try:
            # Load data from the JSON file
            with ref_data_path.open("r", encoding="utf-8") as f: # Specify encoding
                ref_data: Dict[str, List[Dict[str, Any]]] = json.load(f)
            logger.info(
                f"Loaded reference data from {ref_data_path.resolve()}. Keys: {list(ref_data.keys())}"
            )

            # --- Seed OlympianSquads (Teams) ---
            # Reference data key: 'OlympianPantheon', Schema table name: 'olympian_squads'
            ref_team_key = "OlympianPantheon"
            schema_team_table = "olympian_squads"
            olympian_pantheon_data = ref_data.get(ref_team_key, [])
            logger.info(
                f"Found {len(olympian_pantheon_data)} records under key '{ref_team_key}'."
            )

            # Validate team data records from reference data
            valid_olympian_pantheon = [
                t for t in olympian_pantheon_data if self.validate_team_data(t)
            ]
            if len(valid_olympian_pantheon) < len(olympian_pantheon_data):
                logger.warning(
                    f"Skipped {len(olympian_pantheon_data) - len(valid_olympian_pantheon)} invalid records from '{ref_team_key}'."
                )

            # Map reference data keys to schema column names for teams
            # Ref keys: 'mythic_roster_id', 'name', 'abbreviation', 'league'
            # Schema cols: 'id', 'name', 'abbreviation', 'league'
            mapped_olympian_pantheon = []
            for team_record in valid_olympian_pantheon:
                mapped_record = {
                    "id": team_record.get("mythic_roster_id"),
                    "name": team_record.get("name"),
                    "abbreviation": team_record.get("abbreviation"),
                    "league": team_record.get("league"),
                }
                mapped_olympian_pantheon.append(mapped_record)
            logger.info(
                f"Mapped {len(mapped_olympian_pantheon)} team records to schema format."
            )

            # Perform bulk insert for teams
            self.bulk_insert(
                schema_team_table,
                mapped_olympian_pantheon,
                chunk_size=getattr(
                    VaultIdentity, "mnemosyne_core.team_chunk_size", None
                ),
            ) # Pass table-specific chunk size key, fallback to None to use default constant

            # --- Seed MortalHeroes (Players) ---
            # Reference data key: 'players', Schema table name: 'mortal_heroes'
            ref_player_key = "players"
            schema_player_table = "mortal_heroes"
            players_data = ref_data.get(ref_player_key, [])
            logger.info(
                f"Found {len(players_data)} records under key '{ref_player_key}'."
            )

            # Validate player data records from reference data
            valid_players = [p for p in players_data if self.validate_player_data(p)]
            if len(valid_players) < len(players_data):
                logger.warning(
                    f"Skipped {len(players_data) - len(valid_players)} invalid records from '{ref_player_key}'."
                )

            # Map reference data keys to schema column names for players
            # Ref keys: 'hero_id', 'full_name', 'mythic_roster_id'
            # Schema cols: 'id', 'full_name', 'olympian_squad_id'
            mapped_players = []
            for player_record in valid_players:
                mapped_record = {
                    "id": player_record.get("hero_id"),
                    "full_name": player_record.get("full_name"),
                    "olympian_squad_id": player_record.get("mythic_roster_id"),
                }
                mapped_players.append(mapped_record)
            logger.info(
                f"Mapped {len(mapped_players)} player records to schema format."
            )

            # Perform bulk insert for players
            self.bulk_insert(
                schema_player_table,
                mapped_players,
                chunk_size=getattr(
                    VaultIdentity, "mnemosyne_core.player_chunk_size", None
                ),
            ) # Pass table-specific chunk size key

            logger.info("MEDUSA VAULT: Reference data seeding completed.")

        except Exception as e:
            logger.error(
                f"🚨 Data seeding failed from {ref_data_path.resolve()}: {e}",
                exc_info=True,
            )
            raise # Re-raise the exception

    @oracle_focus # Apply decorator if available
    def verify_integrity(self):
        """
        Perform post-load database integrity checks.

        Verifies minimum record counts in critical tables based on schema names.

        Raises:
        ValueError: If any integrity check fails.
        NotImplementedError: If the database keeper lacks required methods.
        """
        logger.info("🔍 Verifying database integrity...")

        # Define checks as (table_name [schema name], count_query, minimum_expected_count, is_max_check)
        # Use schema table names: 'olympian_squads', 'mortal_heroes'
        checks = [
            (
                "olympian_squads",
                "SELECT COUNT(*) FROM olympian_squads;",
                0,
                False,
            ), # Check min count in schema table
            (
                "mortal_heroes",
                "SELECT COUNT(*) FROM mortal_heroes;",
                0,
                False,
            ), # Check min count in schema table
            # Add other integrity checks here
            # Example: Check for orphaned players (player with olympian_squad_id that doesn't exist in olympian_squads)
            # ("orphaned_mortal_heroes", "SELECT COUNT(*) FROM mortal_heroes WHERE olympian_squad_id IS NOT NULL AND olympian_squad_id NOT IN (SELECT id FROM olympian_squads);", 0, True) # True indicates max count 0
        ]

        # Ensure the database keeper instance has an execute method that returns a result object with a .scalar() method
        # Patch: Use plain SQL strings, not SQLAlchemy text objects
        if not hasattr(self.db, "execute"):
            logger.error(
                "Database keeper instance does not support integrity checks via .execute()."
            )
            raise NotImplementedError(
                "Database keeper does not support integrity verification queries."
            )

        for check in checks:
            table_name, query, expected_count, is_max_check = check

            try:
                logger.info(
                    f"Executing integrity check query for '{table_name}': {query[:100]}..."
                )
                # Execute the count query using the keeper's execute method (as a string)
                result = self.db.execute(query)
                # For sqlite3, fetchone()[0] gives the count
                if hasattr(result, "fetchone"):
                    row = result.fetchone()
                    actual_count = int(row[0]) if row and row[0] is not None else 0
                else:
                    actual_count = int(result) if result is not None else 0

                logger.info(
                    f"Integrity check for '{table_name}': Expected {'max' if is_max_check else 'min'} count {expected_count}, Actual count {actual_count}."
                )

                if is_max_check:
                    if actual_count > expected_count:
                        logger.error(
                            f"Integrity check failed for '{table_name}': Expected at most {expected_count} records, found {actual_count}."
                        )
                        raise ValueError(
                            f"Integrity check failed for '{table_name}': Found unexpected records ({actual_count})."
                        )
                else:
                    if actual_count < expected_count:
                        logger.error(
                            f"Integrity check failed for '{table_name}': Expected at least {expected_count} records, found {actual_count}."
                        )
                        raise ValueError(
                            f"Integrity check failed for '{table_name}': Insufficient records found ({actual_count})."
                        )
                logger.info(f"Integrity check passed for '{table_name}'.")

            except Exception as e:
                logger.error(
                    f"Error during integrity check for '{table_name}': {e}",
                    exc_info=True,
                )
                raise # Re-raise the exception

    @oracle_focus # Apply decorator if available
    def run_migrations(self):
        """
        Apply any pending database migrations using a migration tool.

        This is a placeholder method. Replace with actual migration logic
        (e.g., using Alembic or custom migration scripts).
        Assumes migrations are managed separately but triggered here.
        """
        logger.info("MEDUSA VAULT: Running database migrations (placeholder)...")
        if self.dry_run:
            logger.info("MEDUSA VAULT: [Dry Run] Would run database migrations.")
            return

        # --- Placeholder Implementation ---
        # In a real implementation, this would involve:
        # - Checking which migrations have already been applied (e.g., in moirai_prophetic_visions table).
        # - Identifying pending migration scripts.
        # - Applying pending migration scripts in order, using self.db to execute SQL statements from the scripts.
        # - Recording applied migrations in the moirai_prophetic_visions table.
        logger.warning("TITAN WARNING: Migration implementation is a placeholder.")

        # Example: Check for migrations table existence (already done in MnemosyneKeeper init's schema consecration)
        # Example: Apply a dummy migration (requires a migration script file or inline SQL)
        # try:
        # dummy_migration_sql = "ALTER TABLE mortal_heroes ADD COLUMN new_mythic_trait TEXT;"
        # self._execute_in_transaction([dummy_migration_sql]) # Use transactional execution
        # logger.info("MEDUSA VAULT: Applied dummy migration.")
        # # Record migration in moirai_prophetic_visions (requires a migration hash and name)
        # # self.db.inscribe_scroll(...) # Use keeper's upsert/insert for migration tracking table
        # except Exception as e:
        # logger.error(f"TITAN PROCESSING FAILED: apply dummy migration: {e}", exc_info=True)
        # raise # Fail bootstrap if migration fails
        # -----------------------------------
        logger.info("MEDUSA VAULT: Database migration process completed (placeholder).")

    @oracle_focus # Apply decorator if available
    def full_bootstrap(self):
        """
        Executes the complete database bootstrap workflow for the Hyper Medusa Vault:
        initial setup -> backup -> apply schema -> seed data -> run migrations -> verify integrity -> final state setup.
        Includes error handling and ensures foreign keys are properly managed.
        """
        logger.info("MEDUSA VAULT: Starting full database bootstrap workflow...")

        # Ensure foreign keys are off before potentially dropping tables or applying schema (SQLite specific)
        # Done in _setup_initial_state, but re-emphasize intention.
        try:
            if self.db.connection and hasattr(self.db.connection, "execute"):
                self.db.connection.execute(text("PRAGMA foreign_keys = OFF;"))
                logger.info(
                    "Ensured PRAGMA foreign_keys is OFF before main bootstrap steps."
                )
        except Exception as e:
            logger.warning(f"Could not ensure PRAGMA foreign_keys is OFF: {e}")

        try:
            self._backup_existing_data() # Placeholder backup
            self.apply_schema() # Apply the initial schema
            self.seed_reference_data() # Seed initial data
            self.run_migrations() # Apply any pending migrations (placeholder)
            self.verify_integrity() # Verify data counts and constraints

            # Re-enable foreign keys after successful bootstrap (SQLite specific)
            try:
                if self.db.connection and hasattr(self.db.connection, "execute"):
                    self.db.connection.execute(text("PRAGMA foreign_keys = ON;"))
                    logger.info(
                        "Re-enabled PRAGMA foreign_keys after successful bootstrap."
                    )
            except Exception as e:
                logger.warning(f"Could not re-enable PRAGMA foreign_keys: {e}")
                # Decide if this is a critical failure - likely not, but log

            logger.info("MEDUSA VAULT: Database bootstrap completed successfully!")

        except Exception as e:
            logger.error(f"💥 Critical bootstrap failure: {e}", exc_info=True)
            # Consider adding cleanup/rollback logic here for operations
            # not covered by _execute_in_transaction (e.g., removing partial files).
            sys.exit(1) # Exit with a non-zero status code on failure


# === CLI Interface ===
@oracle_focus # Apply decorator if available
def parse_arguments():
    """
    Parses command-line arguments for the database bootstrap script.

    Returns:
    An argparse.Namespace object containing the parsed arguments.
    """
    parser = argparse.ArgumentParser(
        description="Hyper Medusa Database Bootstrapper",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument(
        "--env",
        choices=["dev", "prod"],
        default="dev",
        help="Execution environment (dev or prod). Development environment automatically drops existing tables.",
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Simulate database operations without making actual changes.",
    )
    # The chunk size arguments can be used to override config for this run
    parser.add_argument(
        "--team-chunk-size",
        type=int,
        default=None,
        help=f"Override config chunk size for team data bulk inserts. Defaults to config or {DEFAULT_CHUNK_SIZE}.",
    )
    parser.add_argument(
        "--player-chunk-size",
        type=int,
        default=None,
        help=f"Override config chunk size for player data bulk inserts. Defaults to config or {DEFAULT_CHUNK_SIZE}.",
    )
    # Optional: Add arguments for specific steps (e.g., --schema-only, --seed-only)
    return parser.parse_args()


@oracle_focus # Apply decorator if available
def main():
    """
    Main entry point for the database bootstrap script.
    Parses arguments, initializes the database keeper, and runs the bootstrap workflow.
    """
    args = parse_arguments()
    logger.info(
        f"Starting Hyper Medusa database bootstrap ({args.env.upper()} environment)..."
    )

    # --- Debug: Print working directory and its contents ---
    config_dir = os.path.join(os.getcwd(), "config")
    if os.path.exists(config_dir):
        logger.info(f"Config directory found: {config_dir}")
    else:
        logger.warning(f"Config directory not found: {config_dir}")

    # --- Configuration Loading and VaultIdentity Setup ---
    config_path = os.path.join(config_dir, "sacred_config.development.toml")
    if os.path.exists(config_path):
        config_data = toml.load(config_path)

        class VaultIdentityClass:
            pass

        global VaultIdentity
        VaultIdentity = VaultIdentityClass()
        for section, value in config_data.items():
            setattr(VaultIdentity, section, value)
        logger.info(f"Loaded config from {config_path} and populated VaultIdentity.")
    else:
        logger.warning(
            f"Config file not found at {config_path}. Using mock VaultIdentity."
        )

    # Patch: Set schema_path to schema.sql in project root if not set
    db_config = getattr(VaultIdentity, "mnemosyne_core", {})
    if not db_config.get("schema_path"):
        db_config["schema_path"] = os.path.abspath("schema.sql")

    # Patch: Ensure schema_path is set from vault_paths.SCHEMA if present
    vault_paths = db_config.get("vault_paths")
    if vault_paths and isinstance(vault_paths, dict):
        schema_candidate = vault_paths.get("SCHEMA")
        if schema_candidate:
            db_config["schema_path"] = schema_candidate

    # --- Patch: Extract REFERENCE_DATA from vault_paths and set reference_data_path ---
    if vault_paths:
        reference_data_path = None
        if hasattr(vault_paths, "REFERENCE_DATA"):
            reference_data_path = getattr(vault_paths, "REFERENCE_DATA", None)
        elif isinstance(vault_paths, dict):
            reference_data_path = vault_paths.get("REFERENCE_DATA")
        logger.info(f"[DIAG] Extracted reference_data_path: {reference_data_path}")
        if reference_data_path:
            db_config["reference_data_path"] = reference_data_path

    # Update VaultIdentity mock with CLI overrides for chunk sizes if provided
    # In a real scenario, the config loader would handle CLI overrides on the loaded config object.
    cli_overrides = {}
    if args.team_chunk_size is not None:
        cli_overrides["mnemosyne_core.team_chunk_size"] = args.team_chunk_size
    if args.player_chunk_size is not None:
        cli_overrides["mnemosyne_core.player_chunk_size"] = args.player_chunk_size

    # If VaultIdentity is a mutable mock dictionary or has an update method:
    if hasattr(VaultIdentity, "update"):
        VaultIdentity.update(cli_overrides) # Apply CLI overrides to the mock config
        logger.info(
            f"Applied CLI chunk size overrides to mock VaultIdentity: {cli_overrides}"
        )
    elif cli_overrides:
        logger.warning(
            "Cannot apply CLI chunk size overrides to VaultIdentity object - it is not mutable."
        )

    # Initialize the database keeper instance (MnemosyneArchiveKeeper)
    # Get database config from VaultIdentity to initialize the keeper
    db_config = getattr(VaultIdentity, "mnemosyne_core", {})
    # Convert db_config to dict if it's a Pydantic model
    if hasattr(db_config, "model_dump"):
        db_config = db_config.model_dump()
    # Extract vitality_config fields
    vitality_config = getattr(VaultIdentity, "vitality_config", None)
    if vitality_config:
        if hasattr(vitality_config, "model_dump"):
            vitality_dict = vitality_config.model_dump()
        else:
            vitality_dict = dict(vitality_config)
        db_config.update(vitality_dict)
        db_config["encryption_key"] = vitality_dict.get("encryption_key")
        db_config["memory_path"] = vitality_dict.get("memory_path")
    # --- Diagnostic: Log vault_paths and db_config before schema extraction ---
    vault_paths = getattr(VaultIdentity, "vault_paths", None)
    logger.info(f"[DIAG] vault_paths type: {type(vault_paths)}, value: {vault_paths}")
    logger.info(f"[DIAG] db_config type: {type(db_config)}, value: {db_config}")
    if vault_paths:
        schema_path = None
        if hasattr(vault_paths, "SCHEMA"):
            schema_path = getattr(vault_paths, "SCHEMA", None)
        elif isinstance(vault_paths, dict):
            schema_path = vault_paths.get("SCHEMA")
        logger.info(f"[DIAG] Extracted schema_path: {schema_path}")
        if schema_path:
            db_config["schema_path"] = schema_path

    db_keeper_instance: Optional[MnemosyneArchiveKeeper] = (
        None # Initialize as Optional
    )

    # --- Patch: Instantiate QuantumEntanglementManager for dev bootstrap ---
    quantum_entangler = ExpertQuantumEntanglementManager(config=db_config)
    # --- Patch: Instantiate TemporalFluxStabilizer for dev bootstrap ---
    # Use a reasonable default or config value for stability_threshold
    stability_threshold = db_config.get("oracle_response", 10.0)
    temporal_stabilizer = TemporalFluxStabilizer(stability_threshold)

    try:
        # Pass database config parameters to the keeper constructor
        db_keeper_instance = MnemosyneArchiveKeeper(
            config=db_config,
            quantum_entangler=quantum_entangler,
            temporal_stabilizer=temporal_stabilizer,
            vault_type=db_config.get("vault_type"),
            scroll_path=db_config.get("scroll_path"), # For SQLite
            read_only=db_config.get("read_only", False),
            memory_temple=db_config.get("memory_temple", "migrations"),
        )
        logger.info("MEDUSA VAULT: Database keeper instance created using VaultIdentity config.")

        # Check if the keeper instance was successfully initialized (not None and looks functional)
        if db_keeper_instance is None or not hasattr(db_keeper_instance, "execute"):
            raise RuntimeError("Database keeper instance initialization failed.")

    except Exception as e:
        logger.critical(
            f"TITAN PROCESSING FAILED: initialize database keeper instance using configuration: {e}",
            exc_info=True,
        )
        sys.exit(1)

    # Initialize the bootstrapper
    bootstrapper = DatabaseBootstrapper(db_keeper_instance, dry_run=args.dry_run)
    logger.info("MEDUSA VAULT: DatabaseBootstrapper instance created.")

    try:
        # --- Database Reset in Dev Environment ---
        # Drop all tables if in development environment
        if args.env == "dev":
            logger.warning(
                "♻️ Development environment detected. Resetting database by dropping all tables..."
            )
            # Ensure the database keeper instance has a drop_all_tables method
            if hasattr(db_keeper_instance, "drop_all_tables"):
                try:
                    # Dropping tables might need to happen outside of the main bootstrap transaction
                    # It should be a separate step before applying the schema.
                    db_keeper_instance.drop_all_tables()
                    logger.info("MEDUSA VAULT: All tables dropped in dev environment.")
                except Exception as e:
                    logger.error(
                        f"TITAN PROCESSING FAILED: drop tables in dev environment: {e}", exc_info=True
                    )
                    # Failure to drop tables in dev is likely fatal for bootstrap
                    sys.exit(1)
            else:
                logger.warning(
                    "Database keeper instance does not have a 'drop_all_tables' method. Skipping table drop in dev."
                )

        # --- Run the full bootstrap process ---
        bootstrapper.full_bootstrap()

    except Exception as e:
        # Critical bootstrap failure is already logged by full_bootstrap and exits
        # This outer except block might catch errors during the setup phase before full_bootstrap runs
        logger.critical(
            f"An error occurred during the bootstrap process: {e}", exc_info=True
        )
        sys.exit(1) # Ensure script exits on failure

    finally:
        # Ensure database connection is closed at the end
        # Assuming the keeper instance has a close method
        if db_keeper_instance and hasattr(db_keeper_instance, "close"):
            try:
                db_keeper_instance.close()
                logger.info("MEDUSA VAULT: 🔚 Database connection closed.")
            except Exception as e:
                logger.error(f"TITAN PROCESSING FAILED: close database connection: {e}", exc_info=True)
        elif db_keeper_instance:
            logger.warning(
                "Database keeper instance does not have a 'close' method. Connection may remain open."
            )
        else:
            logger.warning(
                "Database keeper instance was not successfully initialized or is None. Cannot close connection."
            )


# Execute the main function when the script is run directly
if __name__ == "__main__":
    main()
