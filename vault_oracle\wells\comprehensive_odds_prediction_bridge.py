#!/usr/bin/env python3
# DIGITAL FINGERPRINT: UUID=f2b3c4d5-e6f7-8a9b-0c1d-2e3f4a5b6c7d | DATE=2025-06-26
"""
(C) 2025 Hyper Medusa Neural Vault, All Rights Reserved.
PROPRIETARY & CONFIDENTIAL — UNAUTHORIZED USE, COPYING, OR DISTRIBUTION IS STRICTLY PROHIBITED.

This file contains proprietary algorithms, business logic, and intellectual property of Hyper Medusa Neural Vault.
Use, reproduction, or disclosure by any means without explicit written permission is a violation of applicable law.
"""

"""
HYPER MEDUSA NEURAL VAULT - Comprehensive Odds Prediction Bridge Business Value Documentation
============================================================================================

comprehensive_odds_prediction_bridge.py
--------------------------------------
Provides advanced odds prediction and analytics for the Medusa Vault platform.

Business Value:
- Enables comprehensive, real-time odds prediction and analytics.
- Supports extensibility for new prediction models, analytics, and plugins.
- Accelerates the development of new predictive features and business logic.

Extension Points for Plugins & Custom Prediction Analytics:
----------------------------------------------------------
- Subclass `ComprehensiveOddsPredictionBridge` to add new prediction or analytics logic.
- Register prediction plugins via a plugin registry or callback system.
- Add new endpoints or data sources by extending the bridge class.
- Use the config system to enable/disable features per environment.

For further details, see module-level docstrings and architecture documentation.
"""


import os
import sys
import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum
import numpy as np
import pandas as pd
from vault_oracle.wells.expert_odds_integration import (
    ExpertOddsIntegrator,
    create_expert_odds_integrator,
    OddsData
)
from src.models.comprehensive_predictor import (
    ComprehensiveNBAWNBAPredictor,
    get_comprehensive_predictor,
    GamePrediction,
    League
)
from src.betting.market_analysis import MarketAnalysis, ValueBetting, BettingOpportunity
from vault_oracle.core.oracle_focus import oracle_focus
from vault_oracle.interfaces.expert_messaging_orchestrator import ExpertMessagingOrchestrator


# Add project root to path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../../..")))

try:
    from vault_oracle.wells.expert_odds_integration import ( # Import expert odds integration dependencies if available
        ExpertOddsIntegrator,
        create_expert_odds_integrator,
        OddsData
    )
    from src.models.comprehensive_predictor import ( # Import comprehensive predictor dependencies if available
        ComprehensiveNBAWNBAPredictor,
        get_comprehensive_predictor,
        GamePrediction,
        League
    )
    DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Some dependencies not available: {e}")
    def oracle_focus(func):
        return func
    DEPENDENCIES_AVAILABLE = False

logger = logging.getLogger("odds_prediction_bridge")

# Configure expert logging
if not logger.handlers:
    handler = logging.StreamHandler(sys.stdout)
    formatter = logging.Formatter(
        " %(asctime)s 💰 %(levelname)s %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)
    logger.propagate = False

@dataclass
class EnhancedBettingOpportunity:
    """Enhanced betting opportunity with prediction integration"""
    titan_clash_id: str
    sport: str
    home_team: str
    away_team: str
    commence_time: datetime

    # Prediction data
    predicted_winner: str
    win_probability: float
    predicted_spread: float
    predicted_total: float
    model_confidence: float

    # Odds data
    market_moneyline_home: int
    market_moneyline_away: int
    market_spread_line: float
    market_spread_odds: int
    market_total_line: float
    market_total_over_odds: int
    market_total_under_odds: int

    # Value analysis
    moneyline_edge: float
    spread_edge: float
    total_edge: float
    best_bet_type: str
    best_bet_side: str
    kelly_fraction: float
    expected_value: float

    # Recommendation
    recommendation: str
    reasoning: str
    confidence_tier: str
    max_bet_size: float

class ConfidenceTier(str, Enum):
    """Betting confidence tiers"""
    ELITE = "Elite"
    HIGH = "High"
    MEDIUM = "Medium"
    LOW = "Low"
    AVOID = "Avoid"

class OddsPredictionBridge:
    """
    Expert bridge between odds data and prediction models.

    Provides intelligent betting analysis by combining:
    - Live market odds from expert integration
    - ML predictions from comprehensive predictor
    - Advanced value betting analysis
    - Real-time opportunity detection
    """

    def __init__(self):
        """Initialize the odds-prediction bridge"""
        if not DEPENDENCIES_AVAILABLE:
            logger.warning(" Some dependencies not available - running in limited mode")
            return

        self.odds_integrator = create_expert_odds_integrator()
        self.predictor = get_comprehensive_predictor([League.NBA, League.WNBA])
        self.value_betting = ValueBetting(
            minimum_edge=0.03, # 3% minimum edge
            maximum_kelly=0.05, # 5% max Kelly
            confidence_threshold=0.65
        )

        # Initialize expert messaging if available
        try:
            self.expert_messaging = ExpertMessagingOrchestrator()
            logger.info(" MEDUSA VAULT: Expert messaging system connected")
        except Exception as e:
            logger.warning(f"Expert messaging not available: {e}")
            self.expert_messaging = None

        self.opportunities_cache = {}
        self.last_analysis_time = None

        logger.info(" MEDUSA VAULT: Odds-Prediction Bridge initialized successfully")

    @oracle_focus
    async def analyze_all_opportunities(self) -> List[EnhancedBettingOpportunity]:
        """
        Analyze all current betting opportunities across NBA and WNBA.

        Returns:
        List of enhanced betting opportunities
        """
        logger.info(" MEDUSA VAULT: Starting comprehensive odds-prediction analysis...")

        try:
            # Fetch current odds for all leagues (async, may raise network/API errors)
            odds_data = await self.odds_integrator.fetch_all_leagues()

            all_opportunities = []

            for sport_name, games in odds_data.items():
                league = League.NBA if sport_name == "NBA" else League.WNBA
                logger.info(f" Analyzing {len(games)} {sport_name} games...")

                for game in games:
                    try:
                        # Analyze each game for value opportunities
                        opportunity = await self._analyze_game_opportunity(game, league)
                        # Only include opportunities with positive expected value
                        if opportunity and opportunity.expected_value > 0:
                            all_opportunities.append(opportunity)

                    except Exception as e:
                        # Log and skip games with analysis errors (edge case: incomplete odds, prediction failure)
                        logger.warning(f"Failed to analyze game {getattr(game, 'titan_clash_id', 'unknown')}: {e}")
                        continue

            logger.info(f" MEDUSA VAULT: Found {len(all_opportunities)} value opportunities.")
            return all_opportunities

        except Exception as e:
            # Log and return empty list on fatal error (e.g., API outage)
            logger.error(f"Comprehensive odds-prediction analysis failed: {e}")
            return []

    async def _analyze_game_opportunity(
        self,
        odds_data: OddsData,
        league: League
    ) -> Optional[EnhancedBettingOpportunity]:
        """Analyze betting opportunity for a single game"""

        try:
            # Get prediction for this game
            game_data = {
                'titan_clash_id': odds_data.titan_clash_id,
                'home_team': odds_data.home_team,
                'away_team': odds_data.away_team,
                'game_date': odds_data.commence_time.strftime('%Y-%m-%d')
            }

            prediction = await self.predictor.predict_game(game_data, league)

            # Calculate edges for each market
            moneyline_edge = self._calculate_moneyline_edge(odds_data, prediction)
            spread_edge = self._calculate_spread_edge(odds_data, prediction)
            total_edge = self._calculate_total_edge(odds_data, prediction)

            # Determine best betting opportunity
            best_bet_type, best_bet_side, max_edge = self._find_best_bet(
                moneyline_edge, spread_edge, total_edge, odds_data
            )

            if max_edge < 0.03: # No significant edge found
                return None

            # Calculate optimal bet sizing using Kelly Criterion
            kelly_fraction = self._calculate_kelly_sizing(
                max_edge, best_bet_type, odds_data
            )

            # Calculate expected value
            expected_value = self._calculate_expected_value(
                max_edge, kelly_fraction, best_bet_type, odds_data
            )

            # Generate recommendation
            recommendation, reasoning, confidence_tier = self._generate_recommendation(
                max_edge, prediction.confidence, best_bet_type, odds_data
            )

            return EnhancedBettingOpportunity(
                titan_clash_id=odds_data.titan_clash_id,
                sport=league.value.upper(),
                home_team=odds_data.home_team,
                away_team=odds_data.away_team,
                commence_time=odds_data.commence_time,

                # Prediction data
                predicted_winner=prediction.predicted_winner,
                win_probability=prediction.home_win_probability,
                predicted_spread=prediction.point_spread,
                predicted_total=prediction.total_score_prediction,
                model_confidence=prediction.confidence,

                # Odds data
                market_moneyline_home=odds_data.moneyline_home or 0,
                market_moneyline_away=odds_data.moneyline_away or 0,
                market_spread_line=odds_data.spread_line or 0,
                market_spread_odds=odds_data.spread_home_odds or 0,
                market_total_line=odds_data.total_line or 0,
                market_total_over_odds=odds_data.total_over_odds or 0,
                market_total_under_odds=odds_data.total_under_odds or 0,

                # Value analysis
                moneyline_edge=moneyline_edge,
                spread_edge=spread_edge,
                total_edge=total_edge,
                best_bet_type=best_bet_type,
                best_bet_side=best_bet_side,
                kelly_fraction=kelly_fraction,
                expected_value=expected_value,

                # Recommendation
                recommendation=recommendation,
                reasoning=reasoning,
                confidence_tier=confidence_tier,
                max_bet_size=kelly_fraction * 1000 # Assuming $1000 bankroll
            )

        except Exception as e:
            logger.error(f" Error analyzing game opportunity: {e}")
            return None

    def _calculate_moneyline_edge(self, odds_data: OddsData, prediction: GamePrediction) -> float:
        """Calculate moneyline betting edge"""
        if not odds_data.moneyline_home or not odds_data.moneyline_away:
            return 0.0

        # Get model probability
        model_home_prob = prediction.home_win_probability
        model_away_prob = prediction.away_win_probability

        # Get market implied probabilities
        market_home_prob = MarketAnalysis.implied_probability(odds_data.moneyline_home)
        market_away_prob = MarketAnalysis.implied_probability(odds_data.moneyline_away)

        # Calculate edges
        home_edge = MarketAnalysis.calculate_edge(model_home_prob, market_home_prob)
        away_edge = MarketAnalysis.calculate_edge(model_away_prob, market_away_prob)

        return max(home_edge, away_edge)

    def _calculate_spread_edge(self, odds_data: OddsData, prediction: GamePrediction) -> float:
        """Calculate spread betting edge"""
        if not odds_data.spread_line or not odds_data.spread_home_odds:
            return 0.0

        # Compare predicted spread to market spread
        predicted_spread = prediction.point_spread
        market_spread = odds_data.spread_line

        # Calculate edge based on spread difference
        spread_diff = abs(predicted_spread - market_spread)

        # Convert spread difference to edge (simplified approach)
        edge = min(spread_diff * 0.02, 0.15) # Max 15% edge

        return edge if spread_diff > 1.5 else 0.0 # Minimum 1.5 point difference

    def _calculate_total_edge(self, odds_data: OddsData, prediction: GamePrediction) -> float:
        """Calculate total (over/under) betting edge"""
        if not odds_data.total_line or not odds_data.total_over_odds:
            return 0.0

        # Compare predicted total to market total
        predicted_total = prediction.total_score_prediction
        market_total = odds_data.total_line

        # Calculate edge based on total difference
        total_diff = abs(predicted_total - market_total)

        # Convert total difference to edge (simplified approach)
        edge = min(total_diff * 0.015, 0.12) # Max 12% edge

        return edge if total_diff > 3.0 else 0.0 # Minimum 3 point difference

    def _find_best_bet(
        self,
        moneyline_edge: float,
        spread_edge: float,
        total_edge: float,
        odds_data: OddsData
    ) -> Tuple[str, str, float]:
        """Find the best betting opportunity"""

        edges = {
            "moneyline": moneyline_edge,
            "spread": spread_edge,
            "total": total_edge
        }

        best_bet_type = max(edges, key=edges.get)
        max_edge = edges[best_bet_type]

        # Determine side based on bet type
        if best_bet_type == "moneyline":
            # Choose side based on which has better edge
            if odds_data.moneyline_home and odds_data.moneyline_away:
                home_prob = MarketAnalysis.implied_probability(odds_data.moneyline_home)
                away_prob = MarketAnalysis.implied_probability(odds_data.moneyline_away)
                best_bet_side = "home" if home_prob < away_prob else "away"
            else:
                best_bet_side = "home"
        elif best_bet_type == "spread":
            best_bet_side = "home" # Simplified - choose home
        else: # total
            best_bet_side = "over" # Simplified - choose over

        return best_bet_type, best_bet_side, max_edge

    def _calculate_kelly_sizing(
        self,
        edge: float,
        bet_type: str,
        odds_data: OddsData
    ) -> float:
        """Calculate optimal Kelly Criterion bet sizing"""

        # Get relevant odds based on bet type
        if bet_type == "moneyline":
            odds = odds_data.moneyline_home or -110
        elif bet_type == "spread":
            odds = odds_data.spread_home_odds or -110
        else: # total
            odds = odds_data.total_over_odds or -110

        decimal_odds = MarketAnalysis.american_to_decimal(odds)
        kelly = MarketAnalysis.kelly_criterion(edge, decimal_odds)

        # Apply conservative cap
        return min(kelly, 0.05) # Max 5% of bankroll

    def _calculate_expected_value(
        self,
        edge: float,
        kelly_fraction: float,
        bet_type: str,
        odds_data: OddsData
    ) -> float:
        """Calculate expected value of the bet"""

        # Simplified EV calculation
        base_ev = edge * 100 # Convert to percentage points

        # Adjust for Kelly sizing
        adjusted_ev = base_ev * kelly_fraction * 20 # Scale for display

        return max(0.0, adjusted_ev)

    def _generate_recommendation(
        self,
        edge: float,
        model_confidence: float,
        bet_type: str,
        odds_data: OddsData
    ) -> Tuple[str, str, str]:
        """Generate betting recommendation and reasoning"""

        # Determine recommendation strength
        combined_score = edge * model_confidence

        if combined_score >= 0.08:
            recommendation = "STRONG BET"
            confidence_tier = ConfidenceTier.ELITE
        elif combined_score >= 0.06:
            recommendation = "BET"
            confidence_tier = ConfidenceTier.HIGH
        elif combined_score >= 0.04:
            recommendation = "LEAN"
            confidence_tier = ConfidenceTier.MEDIUM
        else:
            recommendation = "MONITOR"
            confidence_tier = ConfidenceTier.LOW

        # Generate reasoning
        reasoning = f"{edge:.1%} edge on {bet_type} with {model_confidence:.1%} model confidence"

        if edge > 0.05:
            reasoning += " - Significant value opportunity"
            if model_confidence > 0.8:
                reasoning += " - High model confidence"

        return recommendation, reasoning, confidence_tier

    async def _send_opportunity_alerts(self, opportunities: List[EnhancedBettingOpportunity]):
        """Send alerts for high-value opportunities"""
        if not self.expert_messaging or not opportunities:
            return

        try:
            elite_opportunities = [
                opp for opp in opportunities
                if opp.confidence_tier == ConfidenceTier.ELITE
            ]

            if elite_opportunities:
                for opp in elite_opportunities[:3]: # Top 3 elite opportunities
                    await self.expert_messaging.send_alert(
                        title=f" ELITE Betting Opportunity",
                        message=f"{opp.away_team} @ {opp.home_team} - {opp.recommendation} {opp.best_bet_type} {opp.best_bet_side}",
                        topic="elite-betting",
                        alert_type="opportunity",
                        priority="high",
                        context={
                            "game": f"{opp.away_team} @ {opp.home_team}",
                            "sport": opp.sport,
                            "bet_type": opp.best_bet_type,
                            "bet_side": opp.best_bet_side,
                            "edge": f"{max(opp.moneyline_edge, opp.spread_edge, opp.total_edge):.1%}",
                            "expected_value": opp.expected_value,
                            "kelly_fraction": f"{opp.kelly_fraction:.1%}",
                            "reasoning": opp.reasoning
                        }
                    )

        except Exception as e:
            logger.error(f" Error sending opportunity alerts: {e}")

    @oracle_focus
    async def get_game_analysis(self, titan_clash_id: str) -> Optional[EnhancedBettingOpportunity]:
        """Get detailed analysis for a specific game"""
        try:
            # Get all opportunities and find the specific game
            opportunities = await self.analyze_all_opportunities()

            for opp in opportunities:
                if opp.titan_clash_id == titan_clash_id:
                    return opp

            logger.warning(f"No analysis found for game {titan_clash_id}")
            return None

        except Exception as e:
            logger.error(f" Error getting game analysis: {e}")
            return None

    @oracle_focus
    async def get_daily_report(self) -> Dict[str, Any]:
        """Generate comprehensive daily betting report"""
        logger.info(" MEDUSA VAULT: Generating daily betting report...")

        try:
            opportunities = await self.analyze_all_opportunities()

            # Categorize opportunities
            elite_opps = [opp for opp in opportunities if opp.confidence_tier == ConfidenceTier.ELITE]
            high_opps = [opp for opp in opportunities if opp.confidence_tier == ConfidenceTier.HIGH]

            # Calculate statistics
            total_expected_value = sum(opp.expected_value for opp in opportunities)
            avg_edge = np.mean([max(opp.moneyline_edge, opp.spread_edge, opp.total_edge)
                                for opp in opportunities]) if opportunities else 0

            # Get next update time
            next_update = self.odds_integrator._calculate_next_update_time([
                opp.commence_time for opp in opportunities
            ])

            report = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "total_opportunities": len(opportunities),
                "elite_opportunities": len(elite_opps),
                "high_opportunities": len(high_opps),
                "total_expected_value": round(total_expected_value, 2),
                "average_edge": round(avg_edge, 4),
                "top_opportunities": [
                    {
                        "game": f"{opp.away_team} @ {opp.home_team}",
                        "sport": opp.sport,
                        "recommendation": opp.recommendation,
                        "bet_type": opp.best_bet_type,
                        "bet_side": opp.best_bet_side,
                        "edge": f"{max(opp.moneyline_edge, opp.spread_edge, opp.total_edge):.1%}",
                        "expected_value": opp.expected_value,
                        "reasoning": opp.reasoning
                    }
                    for opp in opportunities[:10]
                ],
                "api_usage": await self.odds_integrator.get_daily_schedule(),
                "next_update": next_update
            }

            logger.info(f" Daily report generated: {len(opportunities)} opportunities")
            return report

        except Exception as e:
            logger.error(f" Error generating daily report: {e}")
            return {"error": str(e)}

    async def monitor_line_movements(self, interval_minutes: int = 30):
        """Monitor line movements and alert on significant changes"""
        logger.info(f"🔍 Starting line movement monitoring (every {interval_minutes} minutes)")

        previous_odds = {}

        while True:
            try:
                current_odds = await self.odds_integrator.fetch_all_leagues()

                # Compare with previous odds if available
                if previous_odds:
                    await self._detect_line_movements(previous_odds, current_odds)

                previous_odds = current_odds

                # Wait for next interval
                await asyncio.sleep(interval_minutes * 60)

            except Exception as e:
                logger.error(f" MEDUSA ERROR: line movement monitoring: {e}")
                await asyncio.sleep(60) # Wait 1 minute before retry

    async def _detect_line_movements(self, previous_odds: Dict, current_odds: Dict):
        """Detect and alert on significant line movements"""
        # Implementation for line movement detection
        # This would compare odds changes and send alerts for significant movements
        pass

# Factory function
def create_odds_prediction_bridge() -> OddsPredictionBridge:
    """Create and initialize the odds-prediction bridge"""
    return OddsPredictionBridge()

# Main execution for testing
async def main():
    """Demo the odds-prediction bridge"""
    logger.info(" MEDUSA VAULT: Starting Odds-Prediction Bridge Demo")

    if not DEPENDENCIES_AVAILABLE:
        logger.error(" MEDUSA ERROR: Required dependencies not available")
        return

    bridge = create_odds_prediction_bridge()

    # Analyze current opportunities
    opportunities = await bridge.analyze_all_opportunities()

    # Display results
    logger.info(f"\n Analysis Results:")
    logger.info(f" Total Opportunities: {len(opportunities)}")

    for i, opp in enumerate(opportunities[:5], 1):
        logger.info(f"\n Opportunity #{i}:")
        logger.info(f" Game: {opp.away_team} @ {opp.home_team}")
        logger.info(f" Sport: {opp.sport}")
        logger.info(f" Recommendation: {opp.recommendation}")
        logger.info(f" Best Bet: {opp.best_bet_type} {opp.best_bet_side}")
        logger.info(f" Edge: {max(opp.moneyline_edge, opp.spread_edge, opp.total_edge):.1%}")
        logger.info(f" Expected Value: {opp.expected_value:.2f}")
        logger.info(f" Kelly Fraction: {opp.kelly_fraction:.1%}")
        logger.info(f" Reasoning: {opp.reasoning}")

    # Generate daily report
    report = await bridge.get_daily_report()
    logger.info(f"\n Daily Report Summary:")
    logger.info(f" Total EV: {report.get('total_expected_value', 0):.2f}")
    logger.info(f" Average Edge: {report.get('average_edge', 0):.1%}")
    logger.info(f" Elite Opportunities: {report.get('elite_opportunities', 0)}")

if __name__ == "__main__":
    asyncio.run(main())
