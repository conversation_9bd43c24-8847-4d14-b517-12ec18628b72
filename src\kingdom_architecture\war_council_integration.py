#!/usr/bin/env python3
"""
🏛️ WAR COUNCIL INTEGRATION - HYPER MEDUSA NEURAL VAULT 🏛️
=========================================================

War Council integration system implementing hierarchical decision-making
with Original Five, Advanced Spires, and Basketball Spires governance.

WAR COUNCIL STRUCTURE:
🏛️ Original Five: Full voting rights on core decisions
  - ChronosOracle_Expert (Temporal Analysis)
  - NikeVictoryOracle_Expert (Victory Prediction)
  - AthenaStrategyEngine_Expert (Strategic Planning)
  - MetisOracle_Expert (Cunning Analysis)
  - AresOracle_Expert (Conflict Resolution)

⚔️ War Council's Two Right Hands:
  - Neural Intelligence Cortex (Neural processing)
  - Basketball Intelligence Cortex (Basketball expertise)

🏀 Basketball Spires: Full voting on basketball topics
  - CognitiveBasketballCortex
  - NeuralBasketballCore

🔮 Advanced Spires: Advisory roles
  - ProphecyOrchestrator_Expert
  - OlympianCouncil_Expert

GOVERNANCE PROTOCOLS:
- Hierarchical decision escalation
- Specialized domain expertise
- Autonomous conflict resolution
- Real-time adaptive governance
"""

import os
import sys
import asyncio
import logging
import time
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
import json


# Add project root to path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import kingdom core
try:  
    from src.kingdom_architecture.medusa_kingdom_core import (
        MedusaKingdomCore, DecisionContext, KingdomDecision, DecisionType,
        GovernanceLevel, SpireType, KingdomMember
    )
    KINGDOM_CORE_AVAILABLE = True
except ImportError as e:
    KINGDOM_CORE_AVAILABLE = False
    logger.warning(f"⚠️ Kingdom core not available: {e}")

# Import existing systems
try: 
    from src.Battlegrounds.war_council_simulator import WarCouncilSimulator_Expert
    WAR_COUNCIL_AVAILABLE = True
except ImportError:
    WAR_COUNCIL_AVAILABLE = False
    logger.warning("⚠️ War Council Simulator not available")

try: 
    from src.cognitive_spires import (
        ChronosOracle, NikeOracle, AthenaOracle, MetisOracle, AresOracle,
        OlympianCouncil, ProphecyOrchestrator
    )
    COGNITIVE_SPIRES_AVAILABLE = True
except ImportError:
    COGNITIVE_SPIRES_AVAILABLE = False
    logger.warning("⚠️ Cognitive Spires not available")

try: 
    from src.cognitive_basketball_cortex import CognitiveBasketballCortex
    BASKETBALL_CORTEX_AVAILABLE = True
except ImportError:
    BASKETBALL_CORTEX_AVAILABLE = False
    logger.warning("⚠️ Basketball Cortex not available")

class WarCouncilSession: 
    """Individual War Council session for decision making"""
    
    def __init__(self, session_id: str, decision_context: DecisionContext):
        self.session_id = session_id
        self.decision_context = decision_context
        self.participants = []
        self.votes = {}
        self.recommendations = {}
        self.session_start = datetime.now()
        self.session_status = "active"
        self.confidence_threshold = 0.7
        
    def add_participant(self, member_id: str, member_type: str):
        """Add participant to the session"""
        self.participants.append({
            "member_id": member_id,
            "member_type": member_type,
            "joined_at": datetime.now()
        })
    
    def record_vote(self, member_id: str, vote: str, confidence: float, reasoning: str):
        """Record a vote from a council member"""
        self.votes[member_id] = {
            "vote": vote,
            "confidence": confidence,
            "reasoning": reasoning,
            "timestamp": datetime.now()
        }
    
    def record_recommendation(self, member_id: str, recommendation: str, analysis: str):
        """Record a recommendation from an advisory member"""
        self.recommendations[member_id] = {
            "recommendation": recommendation,
            "analysis": analysis,
            "timestamp": datetime.now()
        }

class WarCouncilIntegration:
    """
    🏛️ War Council Integration System
    
    Integrates existing expert systems into a unified War Council
    governance structure with proper hierarchical decision making.
    """
    
    def __init__(self, kingdom_core: Optional['MedusaKingdomCore'] = None):
        self.kingdom_core = kingdom_core
        self.active_sessions = {}
        self.session_history = []
        
        # Initialize expert systems
        self.expert_systems = {}
        self.basketball_systems = {}
        self.advisory_systems = {}
        
        logger.info("🏛️ War Council Integration initialized")
    
    async def initialize_war_council_systems(self):
        """Initialize all War Council expert systems"""
        
        logger.info("🏛️ Initializing War Council Expert Systems...")
        
        try:
            # Initialize Original Five expert systems
            await self._initialize_original_five()
            
            # Initialize Basketball Spires
            await self._initialize_basketball_spires()
            
            # Initialize Advisory Spires
            await self._initialize_advisory_spires()
            
            # Initialize War Council Simulator
            await self._initialize_war_council_simulator()
            
            logger.info("✅ War Council Systems fully initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ War Council initialization failed: {e}")
            return False
    
    async def _initialize_original_five(self):
        """Initialize the Original Five expert systems"""
        
        logger.info("👥 Initializing Original Five...")
        
        if COGNITIVE_SPIRES_AVAILABLE:
            try:
                # ChronosOracle - Temporal Analysis
                self.expert_systems["chronos_oracle"] = {
                    "instance": ChronosOracle(),
                    "specialization": "temporal_analysis",
                    "voting_weight": 1.0,
                    "expertise_domains": ["timing", "temporal_patterns", "scheduling"]
                }
                
                # NikeOracle - Victory Prediction
                self.expert_systems["nike_oracle"] = {
                    "instance": NikeOracle(),
                    "specialization": "victory_prediction",
                    "voting_weight": 1.0,
                    "expertise_domains": ["victory", "performance", "optimization"]
                }
                
                # AthenaOracle - Strategic Planning
                self.expert_systems["athena_oracle"] = {
                    "instance": AthenaOracle(),
                    "specialization": "strategic_planning",
                    "voting_weight": 1.0,
                    "expertise_domains": ["strategy", "wisdom", "planning"]
                }
                
                # MetisOracle - Cunning Analysis
                self.expert_systems["metis_oracle"] = {
                    "instance": MetisOracle(),
                    "specialization": "cunning_analysis",
                    "voting_weight": 1.0,
                    "expertise_domains": ["cunning", "adaptation", "tactical"]
                }
                
                # AresOracle - Conflict Resolution
                self.expert_systems["ares_oracle"] = {
                    "instance": AresOracle(),
                    "specialization": "conflict_resolution",
                    "voting_weight": 1.0,
                    "expertise_domains": ["conflict", "competition", "resolution"]
                }
                
                logger.info(f"✅ Original Five initialized: {len(self.expert_systems)} systems")
                
            except Exception as e:
                logger.warning(f"⚠️ Some Original Five systems failed to initialize: {e}")
        else:
            logger.warning("⚠️ Cognitive Spires not available - using mock Original Five")
            await self._initialize_mock_original_five()
    
    async def _initialize_basketball_spires(self):
        """Initialize Basketball Spires"""
        
        logger.info("🏀 Initializing Basketball Spires...")
        
        if BASKETBALL_CORTEX_AVAILABLE:
            try:
                # CognitiveBasketballCortex
                self.basketball_systems["cognitive_basketball_cortex"] = {
                    "instance": CognitiveBasketballCortex(),
                    "specialization": "basketball_intelligence",
                    "voting_weight": 1.0,
                    "expertise_domains": ["basketball", "game_analysis", "player_analysis"]
                }
                
                logger.info(f"✅ Basketball Spires initialized: {len(self.basketball_systems)} systems")
                
            except Exception as e:
                logger.warning(f"⚠️ Basketball Spires failed to initialize: {e}")
        else:
            logger.warning("⚠️ Basketball Cortex not available - using mock Basketball Spires")
            await self._initialize_mock_basketball_spires()
    
    async def _initialize_advisory_spires(self):
        """Initialize Advisory Spires"""
        
        logger.info("🔮 Initializing Advisory Spires...")
        
        if COGNITIVE_SPIRES_AVAILABLE:
            try:
                # ProphecyOrchestrator
                self.advisory_systems["prophecy_orchestrator"] = {
                    "instance": ProphecyOrchestrator(),
                    "specialization": "prediction_orchestration",
                    "advisory_weight": 0.8,
                    "expertise_domains": ["prediction", "orchestration", "future_analysis"]
                }
                
                # OlympianCouncil
                self.advisory_systems["olympian_council"] = {
                    "instance": OlympianCouncil(),
                    "specialization": "council_coordination",
                    "advisory_weight": 0.9,
                    "expertise_domains": ["coordination", "aggregation", "consensus"]
                }
                
                logger.info(f"✅ Advisory Spires initialized: {len(self.advisory_systems)} systems")
                
            except Exception as e:
                logger.warning(f"⚠️ Advisory Spires failed to initialize: {e}")
        else:
            logger.warning("⚠️ Cognitive Spires not available - using mock Advisory Spires")
            await self._initialize_mock_advisory_spires()
    
    async def _initialize_war_council_simulator(self):
        """Initialize War Council Simulator"""
        
        logger.info("⚔️ Initializing War Council Simulator...")
        
        if WAR_COUNCIL_AVAILABLE:
            try: 
                from src.Battlegrounds.war_council_simulator import WarCouncilConfig
                config = WarCouncilConfig(
                    enable_ensemble_orchestration=True,
                    enable_wisdom_validation=True,
                    async_processing=True,
                    enable_monitoring=True
                )
                
                self.war_council_simulator = WarCouncilSimulator_Expert(config)
                logger.info("✅ War Council Simulator initialized")
                
            except Exception as e:
                logger.warning(f"⚠️ War Council Simulator failed to initialize: {e}")
                self.war_council_simulator = None
        else:
            logger.warning("⚠️ War Council Simulator not available")
            self.war_council_simulator = None
    
    async def _initialize_mock_original_five(self):
        """Initialize mock Original Five for testing"""
        
        mock_systems = ["chronos_oracle", "nike_oracle", "athena_oracle", "metis_oracle", "ares_oracle"]
        
        for system_id in mock_systems:
            self.expert_systems[system_id] = {
                "instance": MockExpertSystem(system_id),
                "specialization": f"mock_{system_id}",
                "voting_weight": 1.0,
                "expertise_domains": [system_id.split("_")[0]]
            }
        
        logger.info("✅ Mock Original Five initialized")
    
    async def _initialize_mock_basketball_spires(self):
        """Initialize mock Basketball Spires for testing"""
        
        self.basketball_systems["cognitive_basketball_cortex"] = {
            "instance": MockBasketballSystem("cognitive_basketball_cortex"),
            "specialization": "mock_basketball_intelligence",
            "voting_weight": 1.0,
            "expertise_domains": ["basketball", "mock_analysis"]
        }
        
        logger.info("✅ Mock Basketball Spires initialized")
    
    async def _initialize_mock_advisory_spires(self):
        """Initialize mock Advisory Spires for testing"""
        
        mock_advisory = ["prophecy_orchestrator", "olympian_council"]
        
        for system_id in mock_advisory:
            self.advisory_systems[system_id] = {
                "instance": MockAdvisorySystem(system_id),
                "specialization": f"mock_{system_id}",
                "advisory_weight": 0.8,
                "expertise_domains": [system_id.split("_")[0]]
            }
        
        logger.info("✅ Mock Advisory Spires initialized")
    
    async def convene_war_council_session(self, decision_context: DecisionContext) -> KingdomDecision:
        """Convene a War Council session for decision making"""
        
        session_id = f"war_council_{decision_context.decision_id}_{int(time.time())}"
        logger.info(f"🏛️ Convening War Council Session: {session_id}")
        
        # Create session
        session = WarCouncilSession(session_id, decision_context)
        self.active_sessions[session_id] = session
        
        try:
            # Determine participants based on decision type
            participants = await self._determine_session_participants(decision_context)
            
            # Add participants to session
            for participant in participants:
                session.add_participant(participant["member_id"], participant["member_type"])
            
            # Collect votes from voting members
            await self._collect_votes(session)
            
            # Collect recommendations from advisory members
            await self._collect_recommendations(session)
            
            # Aggregate decision
            decision = await self._aggregate_war_council_decision(session)
            
            # Close session
            session.session_status = "completed"
            self.session_history.append(session)
            del self.active_sessions[session_id]
            
            logger.info(f"✅ War Council Session completed: {decision.outcome}")
            return decision
            
        except Exception as e:
            logger.error(f"❌ War Council Session failed: {e}")
            session.session_status = "failed"
            
            # Return escalation decision
            return KingdomDecision(
                decision_id=decision_context.decision_id,
                decision_type=decision_context.decision_type,
                outcome="escalated",
                confidence_score=0.0,
                reasoning=f"War Council session failed: {e}",
                implemented_by="war_council_integration"
            )
    
    async def _determine_session_participants(self, context: DecisionContext) -> List[Dict[str, str]]:
        """Determine participants for War Council session"""
        
        participants = []
        
        # Always include Original Five for strategic decisions
        if context.decision_type in [DecisionType.STRATEGIC, DecisionType.TACTICAL]:
            for system_id in self.expert_systems.keys():
                participants.append({
                    "member_id": system_id,
                    "member_type": "voting_member",
                    "system_type": "original_five"
                })
        
        # Include Basketball Spires for basketball decisions
        if context.decision_type == DecisionType.BASKETBALL_SPECIFIC:
            for system_id in self.basketball_systems.keys():
                participants.append({
                    "member_id": system_id,
                    "member_type": "voting_member",
                    "system_type": "basketball_spires"
                })
        
        # Include Advisory Spires for all decisions
        for system_id in self.advisory_systems.keys():
            participants.append({
                "member_id": system_id,
                "member_type": "advisory_member",
                "system_type": "advisory_spires"
            })
        
        return participants
    
    async def _collect_votes(self, session: WarCouncilSession):
        """Collect votes from voting members"""
        
        for participant in session.participants:
            if participant["member_type"] == "voting_member":
                member_id = participant["member_id"]
                
                # Get vote from appropriate system
                if participant["system_type"] == "original_five":
                    vote_result = await self._get_expert_system_vote(member_id, session.decision_context)
                elif participant["system_type"] == "basketball_spires":
                    vote_result = await self._get_basketball_system_vote(member_id, session.decision_context)
                else:
                    continue
                
                session.record_vote(
                    member_id,
                    vote_result["vote"],
                    vote_result["confidence"],
                    vote_result["reasoning"]
                )
    
    async def _collect_recommendations(self, session: WarCouncilSession):
        """Collect recommendations from advisory members"""
        
        for participant in session.participants:
            if participant["member_type"] == "advisory_member":
                member_id = participant["member_id"]
                
                recommendation_result = await self._get_advisory_recommendation(member_id, session.decision_context)
                
                session.record_recommendation(
                    member_id,
                    recommendation_result["recommendation"],
                    recommendation_result["analysis"]
                )
    
    async def _get_expert_system_vote(self, system_id: str, context: DecisionContext) -> Dict[str, Any]:
        """Get vote from an expert system"""
        
        system_info = self.expert_systems.get(system_id)
        if not system_info:
            return {"vote": "abstain", "confidence": 0.0, "reasoning": "System not available"}
        
        # Simulate expert system decision making
        expertise_match = any(domain in context.description.lower() 
                            for domain in system_info["expertise_domains"])
        
        base_confidence = 0.8 if expertise_match else 0.6
        confidence = min(1.0, base_confidence + (system_info["voting_weight"] * 0.1))
        
        if confidence >= 0.7:
            vote = "approve"
        elif confidence >= 0.5:
            vote = "conditional"
        else:
            vote = "reject"
        
        return {
            "vote": vote,
            "confidence": confidence,
            "reasoning": f"Expert analysis by {system_id} (expertise match: {expertise_match})"
        }
    
    async def _get_basketball_system_vote(self, system_id: str, context: DecisionContext) -> Dict[str, Any]:
        """Get vote from a basketball system"""
        
        system_info = self.basketball_systems.get(system_id)
        if not system_info:
            return {"vote": "abstain", "confidence": 0.0, "reasoning": "Basketball system not available"}
        
        # Basketball systems have high confidence on basketball decisions
        if context.decision_type == DecisionType.BASKETBALL_SPECIFIC:
            confidence = 0.9
            vote = "approve"
        else:
            confidence = 0.6
            vote = "conditional"
        
        return {
            "vote": vote,
            "confidence": confidence,
            "reasoning": f"Basketball analysis by {system_id}"
        }
    
    async def _get_advisory_recommendation(self, system_id: str, context: DecisionContext) -> Dict[str, Any]:
        """Get recommendation from an advisory system"""
        
        system_info = self.advisory_systems.get(system_id)
        if not system_info:
            return {"recommendation": "neutral", "analysis": "Advisory system not available"}
        
        # Advisory systems provide recommendations
        advisory_weight = system_info["advisory_weight"]
        
        if advisory_weight >= 0.8:
            recommendation = "positive"
        elif advisory_weight >= 0.6:
            recommendation = "neutral"
        else:
            recommendation = "negative"
        
        return {
            "recommendation": recommendation,
            "analysis": f"Advisory analysis by {system_id} (weight: {advisory_weight})"
        }
    
    async def _aggregate_war_council_decision(self, session: WarCouncilSession) -> KingdomDecision:
        """Aggregate War Council votes and recommendations into final decision"""
        
        # Count votes
        approve_votes = len([v for v in session.votes.values() if v["vote"] == "approve"])
        conditional_votes = len([v for v in session.votes.values() if v["vote"] == "conditional"])
        reject_votes = len([v for v in session.votes.values() if v["vote"] == "reject"])
        total_votes = len(session.votes)
        
        # Calculate confidence
        if total_votes > 0:
            avg_confidence = sum(v["confidence"] for v in session.votes.values()) / total_votes
        else:
            avg_confidence = 0.0
        
        # Determine outcome
        if total_votes == 0:
            outcome = "escalated"
            reasoning = "No votes received - escalating to higher authority"
        elif approve_votes / total_votes >= 0.6 and avg_confidence >= 0.7:
            outcome = "approved"
            reasoning = f"War Council approved ({approve_votes}/{total_votes} approve votes, {avg_confidence:.1%} confidence)"
        elif avg_confidence < 0.7:
            outcome = "escalated"
            reasoning = f"Low confidence ({avg_confidence:.1%}) - escalating to Medusa Queen"
        else:
            outcome = "rejected"
            reasoning = f"War Council rejected ({approve_votes}/{total_votes} approve votes)"
        
        # Include advisory recommendations in reasoning
        if session.recommendations:
            positive_recs = len([r for r in session.recommendations.values() if r["recommendation"] == "positive"])
            total_recs = len(session.recommendations)
            reasoning += f" | Advisory: {positive_recs}/{total_recs} positive recommendations"
        
        return KingdomDecision(
            decision_id=session.decision_context.decision_id,
            decision_type=session.decision_context.decision_type,
            outcome=outcome,
            votes={k: v["vote"] for k, v in session.votes.items()},
            confidence_score=avg_confidence,
            reasoning=reasoning,
            implemented_by="war_council_integration"
        )
    
    def get_war_council_status(self) -> Dict[str, Any]:
        """Get War Council integration status"""
        
        return {
            "integration_type": "war_council_integration",
            "expert_systems_count": len(self.expert_systems),
            "basketball_systems_count": len(self.basketball_systems),
            "advisory_systems_count": len(self.advisory_systems),
            "active_sessions": len(self.active_sessions),
            "session_history_length": len(self.session_history),
            "war_council_simulator_available": self.war_council_simulator is not None,
            "cognitive_spires_available": COGNITIVE_SPIRES_AVAILABLE,
            "basketball_cortex_available": BASKETBALL_CORTEX_AVAILABLE,
            "systems_status": {
                "original_five": list(self.expert_systems.keys()),
                "basketball_spires": list(self.basketball_systems.keys()),
                "advisory_spires": list(self.advisory_systems.keys())
            }
        }

# Mock classes for testing
# TODO: Replace with production implementation
# TODO: Replace with production implementation
class MockExpertSystem:
    def __init__(self, system_id: str):
        self.system_id = system_id

# TODO: Replace with production implementation
# TODO: Replace with production implementation
class MockBasketballSystem:
    def __init__(self, system_id: str):
        self.system_id = system_id

# TODO: Replace with production implementation
# TODO: Replace with production implementation
class MockAdvisorySystem:
    def __init__(self, system_id: str):
        self.system_id = system_id
